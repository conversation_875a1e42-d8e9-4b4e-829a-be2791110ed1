/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwNavigationBar.h
@Time    :   2025/03/03 20:42:24
*
**************************************************************************/

#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "qw_os_gui.h"
#include "QwTopStatus/QwTopStatus.h"

typedef enum
{
    QNB_TOP,                    // 状态栏
    QNB_TOP_TITLE,              // 状态栏+标题
    QNB_TOP_ICON_TITLE,         // 状态栏+图标_标题
    QNB_TITLE,                  // 标题
    QNB_TITLE_DESCRIBE,         // 标题+描述
    QNB_ICON_TITLE_DESCRIBE,    // 图标_标题+描述
    QNB_ICON_DESCRIBE,          // 图标+描述
    QNB_ICON_TITLE,             // 图标+标题

    QNB__MAX,
}QWNAVIBAR_TYPE;

class QwNavigationBar : public Container
{
private:
    QWNAVIBAR_TYPE type_;

    QwTopStatus top_;
    Image icon_;
	TextArea title_;
    WideTextAction title_show_way_;
	TextArea describe_;



public:

    QwNavigationBar();
    ~QwNavigationBar() {};

    /**
     * @brief 生成提示框
     */
    virtual void setup();

    /**
     * @brief 刷新
     */
    virtual void on_notify() override;

	void handleTickEvent() override;
	void handleKeyEvent(uint8_t c) override;
	void handleClickEvent(const ClickEvent& evt) override;
	void handleDragEvent(const DragEvent& evt) override;
	void handleGestureEvent(const GestureEvent& evt) override;

    /**
     * @brief 设置类型
     * @param type 类型
     */
    void set_type(QWNAVIBAR_TYPE type);

    /**
     * @brief 获取类型
     * @return 类型
     */
    QWNAVIBAR_TYPE get_type();

    /**
     * @brief 设置图标内容
     * @param bmp 图标内容
     */
    void set_icon_bitmap(const Bitmap& bmp);

    /**
     * @brief 设置文本内容
     * @param t 文本内容
     */
    void set_title_typed_dynamic_text(TypedTextId t);

    /**
     * @brief 设置文本内容
     * @param t 文本内容
     */
    void set_describe_typed_dynamic_text(TypedTextId t);

    /**
     * @brief 获取状态栏设置类型
     */
    QwTopStatus* get_top_status();

    /**
     * @brief 重置组件
     */
    void set_reset();

    /**
     * @brief 更新标题的显示方式
     * 滚动/省略
     */
    void set_tittle_show_way(WideTextAction way);
};
