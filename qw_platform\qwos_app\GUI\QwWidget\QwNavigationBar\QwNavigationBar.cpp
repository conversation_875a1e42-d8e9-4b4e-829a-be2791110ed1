/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwNavigationBar.cpp
@Time    :   2024/12/24 17:42:04
*
**************************************************************************/


#include "QwNavigationBar.h"
#include "MvcApp.h"
#include "image/images.h"

QwNavigationBar::QwNavigationBar() : type_(QNB_TOP)
    , title_show_way_(WIDE_TEXT_WORDWRAP_DOT)
{
}

/**
 * @brief 生成toast提示框
 */
void QwNavigationBar::setup()
{
	add(top_);
	add(icon_);
	add(title_);
	add(describe_);
}

void QwNavigationBar::on_notify()
{
    setPosition(0, 0, 466, 128);
    if(top_.isVisible())
    {
        top_.setup();
    }

    if (type_ == QNB_TOP)
    {
        top_.setAlign(ALIGN_IN_BM, 0, -52);
    }
	else if(type_ == QNB_TOP_TITLE)
	{
        if (title_show_way_ == WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR)
        {
            title_.setWidthHeight(340, PUBLIC_NO_38_M_FONT.line_height);
        }
        else
        {
            title_.setWidthHeight(322, PUBLIC_NO_38_M_FONT.line_height);
            title_.setTextVerticalAlignment(ALIGN_Y_MID);
        }
        title_.setTextAlignment(CENTER);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setLabelAlpha(LV_OPA_TRANSP);
        title_.setWideTextAction(title_show_way_);
        //title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        top_.setAlign(ALIGN_IN_BM, 0, -52);
        title_.setAlign(ALIGN_IN_BM, 0, 0);
	}
    else if (type_ == QNB_TOP_ICON_TITLE)
	{
		title_.setWidthHeight((322 - 44 - 4), PUBLIC_NO_38_M_FONT.line_height);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setTextAlignment(CENTER);
		title_.setTextVerticalAlignment(ALIGN_Y_MID);
		title_.setLabelAlpha(LV_OPA_TRANSP);
		title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);

        title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        top_.setAlign(ALIGN_IN_BM, 0, -52);
        int width = title_.getWidth() + 4 + icon_.getWidth();
        int x_offset_ = (rect.width - width) / 2;
        icon_.setAlign(ALIGN_IN_LB, x_offset_, 0);
        x_offset_ += (icon_.getWidth() + 4);
        title_.setAlign(ALIGN_IN_LB, x_offset_, 0);
	}
    else if (type_ == QNB_TITLE)
    {
        title_.setWidthHeight(322, PUBLIC_NO_38_M_FONT.line_height);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setTextAlignment(CENTER);
		title_.setTextVerticalAlignment(ALIGN_Y_MID);
		title_.setLabelAlpha(LV_OPA_TRANSP);
		title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        title_.setAlign(ALIGN_IN_CENTER, 0, 0);
    }
    else if (type_ == QNB_TITLE_DESCRIBE)
    {
        title_.setWidthHeight(322, PUBLIC_NO_38_M_FONT.line_height);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setTextAlignment(CENTER);
		title_.setTextVerticalAlignment(ALIGN_Y_MID);
		title_.setLabelAlpha(LV_OPA_TRANSP);
		title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        describe_.setWidthHeight(322, PUBLIC_NO_32_R_FONT.line_height);
        describe_.setColor(lv_color_hex(0xFFFFFF));
        describe_.setAlpha(LV_OPA_60);
		describe_.setTextFont(&PUBLIC_NO_32_R_FONT);
		describe_.setTextAlignment(CENTER);
		describe_.setTextVerticalAlignment(ALIGN_Y_MID);
		describe_.setLabelAlpha(LV_OPA_TRANSP);
		describe_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        describe_.resizeToCurrentTextWithAlignment();
        describe_.setHeight(52);

        title_.setAlign(ALIGN_IN_BM, 0, -52);
        describe_.setAlign(ALIGN_IN_BM, 0, 0);
    }
    else if (type_ == QNB_ICON_TITLE_DESCRIBE)
    {
        title_.setWidthHeight((322 - 44 - 4), PUBLIC_NO_38_M_FONT.line_height);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setTextAlignment(CENTER);
		title_.setTextVerticalAlignment(ALIGN_Y_MID);
		title_.setLabelAlpha(LV_OPA_TRANSP);
		title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        describe_.setWidthHeight(322, PUBLIC_NO_32_R_FONT.line_height);
        describe_.setColor(lv_color_hex(0xFFFFFF));
        describe_.setAlpha(LV_OPA_60);
		describe_.setTextFont(&PUBLIC_NO_32_R_FONT);
		describe_.setTextAlignment(CENTER);
		describe_.setTextVerticalAlignment(ALIGN_Y_MID);
		describe_.setLabelAlpha(LV_OPA_TRANSP);
		describe_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        describe_.resizeToCurrentTextWithAlignment();
        describe_.setHeight(52);

        int width = title_.getWidth() + 4 + icon_.getWidth();
        int x_offset_ = (rect.width - width) / 2;
        icon_.setAlign(ALIGN_IN_LB, x_offset_, -52);
        x_offset_ += (icon_.getWidth() + 4);
        title_.setAlign(ALIGN_IN_LB, x_offset_, -52);
        describe_.setAlign(ALIGN_IN_BM, 0, 0);
    }
    else if (type_ == QNB_ICON_DESCRIBE)
    {
        describe_.setWidthHeight(322, PUBLIC_NO_32_R_FONT.line_height);
        describe_.setColor(lv_color_hex(0xFFFFFF));
        describe_.setAlpha(LV_OPA_60);
		describe_.setTextFont(&PUBLIC_NO_32_R_FONT);
		describe_.setTextAlignment(CENTER);
		describe_.setTextVerticalAlignment(ALIGN_Y_MID);
		describe_.setLabelAlpha(LV_OPA_TRANSP);
		describe_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        describe_.resizeToCurrentTextWithAlignment();
        describe_.setHeight(52);

        icon_.setAlign(ALIGN_IN_BM, 0, -52);
        describe_.setAlign(ALIGN_IN_BM, 0, 0);
    }
    else if (type_ == QNB_ICON_TITLE)
    {
        title_.setWidthHeight((322 - 44 - 4), PUBLIC_NO_38_M_FONT.line_height);
        title_.setColor(lv_color_hex(0xFFFFFF));
		title_.setTextFont(&PUBLIC_NO_38_M_FONT);
		title_.setTextAlignment(CENTER);
		title_.setTextVerticalAlignment(ALIGN_Y_MID);
		title_.setLabelAlpha(LV_OPA_TRANSP);
		title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        title_.resizeToCurrentTextWithAlignment();
        title_.setHeight(52);

        icon_.setAlign(ALIGN_IN_BM, 0, -52);
        title_.setAlign(ALIGN_IN_BM, 0, 0);
    }
}

void QwNavigationBar::handleTickEvent()
{
    if(top_.isVisible())
    {
        top_.handleTickEvent();
    }
}

void QwNavigationBar::handleKeyEvent(uint8_t c)
{
}

void QwNavigationBar::handleClickEvent(const ClickEvent& evt)
{
}

void QwNavigationBar::handleDragEvent(const DragEvent& evt)
{
}

void QwNavigationBar::handleGestureEvent(const GestureEvent& evt)
{
}

/**
 * @brief 设置toast类型
 * @param type toast类型
 */
void QwNavigationBar::set_type(QWNAVIBAR_TYPE type)
{
	type_ = type;
    if (type_ == QNB_TOP || type_ == QNB_TOP_TITLE || type_ == QNB_TOP_ICON_TITLE)
    {
        top_.setVisible(true);
    }
    else
    {
        top_.setVisible(false);
    }
}

/**
 * @brief 获取类型
 * @return 类型
 */
QWNAVIBAR_TYPE QwNavigationBar::get_type()
{
    return type_;
}

/**
 * @brief 设置toast图标内容
 * @param bmp 图标内容
 */
void QwNavigationBar::set_icon_bitmap(const Bitmap& bmp)
{
    icon_.setVisible(true);
	icon_.setBitmap(bmp);
}

/**
 * @brief 设置toast文本内容
 * @param t 文本内容
 */
void QwNavigationBar::set_title_typed_dynamic_text(TypedTextId t)
{
    title_.setVisible(true);
	title_.setTypedDynamicText(t);
}

/**
 * @brief 设置toast文本内容
 * @param t 文本内容
 */
void QwNavigationBar::set_describe_typed_dynamic_text(TypedTextId t)
{
    describe_.setVisible(true);
	describe_.setTypedDynamicText(t);
}

QwTopStatus* QwNavigationBar::get_top_status()
{
    return &top_;
}

void QwNavigationBar::set_reset()
{
    top_.setVisible(false);
    icon_.setVisible(false);
    title_.setVisible(false);
    describe_.setVisible(false);
}

void QwNavigationBar::set_tittle_show_way(WideTextAction way)
{
    title_show_way_ = way;
}
