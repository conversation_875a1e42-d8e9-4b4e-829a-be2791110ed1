﻿/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   sensor_evt_handler.c
@Time    :   2024/12/10 19:59:05
*
************************************************************/
#include "sensor_evt_handler.h"
#include "subscribe_service.h"
#include "qw_sensor_common.h"
#include "qw_time_service.h"
#include "qw_time_util.h"
#include "ble_ant_module.h"
#include "qw_timer.h"
#ifdef SIMULATOR
#include "activity_record/activity_fit_app.h"
#else
#include "algo_service_sport_status.h"
#endif
#include "hr_push.h"
#include "view_page_model_sports.h"
static sensor_search_infor_t sensor_search_infor;

static void event_sensor_data_update(sensor_id_t *sensor_id, sensor_type_t sensor_type, sensor_radio_type_t sensor_radio_type, int16_t data, bool is_valid)
{
	sensor_original_data_t* original_data = conn_open_device_data_all();
	int32_t res = 0;

	switch(sensor_type)
	{
		case SENSOR_TYPE_HRM:
		{
			hr_data_t out_data;
			if(is_valid)
			{
				out_data.heart_rate = original_data->hrData.heart_rate;
			}
			else
			{
				out_data.heart_rate = UINT8_MAX;
			}
			res = qw_dataserver_publish("ble_hr_sensor", &out_data, sizeof(hr_data_t));
    		if (res != ERRO_CODE_OK)
    		{
				// rt_kprintf("publish ble_hr_sensor failed %d\n", res);
    		}
		}
			break;
		case SENSOR_TYPE_CAD:
		{
			cad_data_t out_data;
			if(is_valid)
			{
				out_data.cadence = original_data->cadData.cadence;
			}
			else
			{
				out_data.cadence = UINT8_MAX;
			}
			res = qw_dataserver_publish("ble_cad_sensor", &out_data, sizeof(cad_data_t));
    		if (res != ERRO_CODE_OK)
    		{
				// rt_kprintf("publish ble_cad_sensor failed %d\n", res);
    		}
		}
			break;
		case SENSOR_TYPE_SPD:
		{
			spd_data_t out_data;
			if(is_valid)
			{
				out_data.wheel_delta = original_data->spdData.wheel_delta;
				out_data.wheel_speed = original_data->spdData.wheel_speed;
                original_data->spdData.wheel_delta = 0; //赋值后清零
			}
			else
			{
			    out_data.wheel_delta = UINT16_MAX;
				out_data.wheel_speed = UINT16_MAX;
			}
			res = qw_dataserver_publish("ble_spd_sensor", &out_data, sizeof(spd_data_t));
    		if (res != ERRO_CODE_OK)
    		{
				// rt_kprintf("publish ble_spd_sensor failed %d\n", res);
    		}
            original_data->spdData.wheel_delta = 0;
		}
			break;
		case SENSOR_TYPE_CBSC:
		{
			cbsc_data_t out_data;
			if(is_valid)
			{
				out_data.cadence = original_data->cbscData.cadence;
				out_data.wheel_delta = original_data->cbscData.wheel_delta;
				out_data.wheel_speed = original_data->cbscData.wheel_speed;
                original_data->cbscData.wheel_delta = 0; //赋值后清零
			}
			else
			{
			    out_data.cadence = UINT8_MAX;
				out_data.wheel_delta = UINT16_MAX;
				out_data.wheel_speed = UINT16_MAX;
			}
			res = qw_dataserver_publish("ble_cbsc_sensor", &out_data, sizeof(cbsc_data_t));
    		if (res != ERRO_CODE_OK)
    		{
				// rt_kprintf("publish ble_spd_sensor failed %d\n", res);
    		}
            original_data->cbscData.wheel_delta = 0;
		}
			break;
        case SENSOR_TYPE_BPWR:
        {
            pwr_data_t out_data;
            if(is_valid)
            {
                out_data.power                      = original_data->pwrData.power;
                out_data.wheel_speed                = original_data->pwrData.wheel_speed;
                out_data.wheel_delta                = original_data->pwrData.wheel_delta;
                out_data.cadence                    = original_data->pwrData.cadence;
                out_data.pedal                      = original_data->pwrData.pedal;
                out_data.left_torque_effectiveness  = original_data->pwrData.left_torque_effectiveness;
                out_data.right_torque_effectiveness = original_data->pwrData.right_torque_effectiveness;
                out_data.left_pedal_smoothness      = original_data->pwrData.left_pedal_smoothness;
                out_data.right_pedal_smoothness     = original_data->pwrData.right_pedal_smoothness;
                out_data.hw_revision                = original_data->pwrData.hw_revision;
                out_data.manufacturer_id            = original_data->pwrData.manufacturer_id;
                out_data.model_number               = original_data->pwrData.model_number;
                out_data.sw_revision                = original_data->pwrData.sw_revision;
                out_data.serial_number              = original_data->pwrData.serial_number;
                out_data.number_of_battery          = original_data->pwrData.number_of_battery;
                out_data.battery_identifier         = original_data->pwrData.battery_identifier;
                out_data.cumulative_operating_time  = original_data->pwrData.cumulative_operating_time;
                out_data.battery_voltage            = original_data->pwrData.battery_voltage;
                out_data.battery_status             = original_data->pwrData.battery_status;
                out_data.is_support_params_get      = original_data->pwrData.is_support_params_get;
                out_data.is_support_cl_set          = original_data->pwrData.is_support_cl_set;
                out_data.crank_length               = original_data->pwrData.crank_length;
                out_data.cl_status                  = original_data->pwrData.cl_status;
                out_data.sw_status                  = original_data->pwrData.sw_status;
                out_data.availability_status        = original_data->pwrData.availability_status;
                out_data.custom_calib_status        = original_data->pwrData.custom_calib_status;
                out_data.auto_crank_length          = original_data->pwrData.auto_crank_length;
            }
            else
            {
                out_data.power                      = UINT16_MAX;
                out_data.wheel_speed                = UINT16_MAX;
                out_data.wheel_delta                = UINT16_MAX;
                out_data.cadence                    = UINT8_MAX;
                out_data.pedal                      = UINT8_MAX;
                out_data.left_torque_effectiveness  = UINT8_MAX;
                out_data.right_torque_effectiveness = UINT8_MAX;
                out_data.left_pedal_smoothness      = UINT8_MAX;
                out_data.right_pedal_smoothness     = UINT8_MAX;
                out_data.hw_revision                = UINT8_MAX;
                out_data.manufacturer_id            = UINT16_MAX;
                out_data.model_number               = UINT16_MAX;
                out_data.sw_revision                = UINT16_MAX;
                out_data.serial_number              = UINT32_MAX;
                out_data.number_of_battery          = UINT8_MAX;
                out_data.battery_identifier         = UINT8_MAX;
                out_data.cumulative_operating_time  = UINT32_MAX;
                out_data.battery_voltage            = UINT16_MAX;
                out_data.battery_status             = UINT8_MAX;
                out_data.is_support_params_get      = UINT8_MAX;
                out_data.is_support_cl_set          = UINT8_MAX;
                out_data.crank_length               = UINT16_MAX;
                out_data.cl_status                  = UINT8_MAX;
                out_data.sw_status                  = UINT8_MAX;
                out_data.availability_status        = UINT8_MAX;
                out_data.custom_calib_status        = UINT8_MAX;
                out_data.auto_crank_length          = UINT8_MAX;
            }
            res = qw_dataserver_publish("ble_pwr_sensor", &out_data, sizeof(pwr_data_t));
            if (res != ERRO_CODE_OK)
            {
                // rt_kprintf("publish ble_bpwr_sensor failed %d\n", res);
            }
            original_data->pwrData.wheel_delta = 0;
        }
            break;
        case SENSOR_TYPE_FEC:
        {
            fe_data_t out_data;
            if(is_valid)
            {
                out_data.accumulated_power          = original_data->feData.accumulated_power;
                out_data.instantaneous_power        = original_data->feData.instantaneous_power;
                out_data.speed                      = original_data->feData.speed;
                out_data.zero_offset                = original_data->feData.zero_offset;
                out_data.spin_down_time             = original_data->feData.spin_down_time;
                out_data.zero_offset_calib          = original_data->feData.zero_offset_calib;
                out_data.spin_down_calib            = original_data->feData.spin_down_calib;
                out_data.temperature                = original_data->feData.temperature;
                out_data.equipment_type             = original_data->feData.equipment_type;
                out_data.elapsed_time               = original_data->feData.elapsed_time;
                out_data.distance_traveled          = original_data->feData.distance_traveled;
                out_data.heart_rate                 = original_data->feData.heart_rate;
                out_data.capabilities_and_state     = original_data->feData.capabilities_and_state;
                out_data.update_event_count         = original_data->feData.update_event_count;
                out_data.instantaneous_cadence      = original_data->feData.instantaneous_cadence;
                out_data.bicycle_power_calibration  = original_data->feData.bicycle_power_calibration;
                out_data.resistance_calibration     = original_data->feData.resistance_calibration;
                out_data.user_configuration         = original_data->feData.user_configuration;
                out_data.target_power_limits        = original_data->feData.target_power_limits;
            }
            else
            {
                out_data.accumulated_power          = UINT16_MAX;
                out_data.instantaneous_power        = UINT16_MAX;
                out_data.speed                      = UINT16_MAX;
                out_data.zero_offset                = UINT16_MAX;
                out_data.spin_down_time             = UINT16_MAX;
                out_data.zero_offset_calib          = UINT8_MAX;
                out_data.spin_down_calib            = UINT8_MAX;
                out_data.temperature                = UINT8_MAX;
                out_data.equipment_type             = UINT8_MAX;
                out_data.elapsed_time               = UINT8_MAX;
                out_data.distance_traveled          = UINT8_MAX;
                out_data.heart_rate                 = UINT8_MAX;
                out_data.capabilities_and_state     = UINT8_MAX;
                out_data.update_event_count         = UINT8_MAX;
                out_data.instantaneous_cadence      = UINT8_MAX;
                out_data.bicycle_power_calibration  = UINT8_MAX;
                out_data.resistance_calibration     = UINT8_MAX;
                out_data.user_configuration         = UINT8_MAX;
                out_data.target_power_limits        = UINT8_MAX;
            }
            res = qw_dataserver_publish("ble_fe_sensor", &out_data, sizeof(fe_data_t));
            if (res != ERRO_CODE_OK)
            {
                // rt_kprintf("publish ble_bpwr_sensor failed %d\n", res);
            }
        }
            break;
        case SENSOR_TYPE_RD:
        {
            rd_data_t out_data;
            if(is_valid)
            {
                out_data.chn_freq               = original_data->rdData.chn_freq;
                out_data.cadence                = original_data->rdData.cadence;
                out_data.walking_flag           = original_data->rdData.walking_flag;
                out_data.vertical_oscillation   = original_data->rdData.vertical_oscillation;
                out_data.ground_contact_time    = original_data->rdData.ground_contact_time;
                out_data.stance_time_percent    = original_data->rdData.stance_time_percent;
                out_data.step_count             = original_data->rdData.step_count;
                out_data.ground_contact_balance = original_data->rdData.ground_contact_balance;
                out_data.vertical_ratio         = original_data->rdData.vertical_ratio;
                out_data.step_length            = original_data->rdData.step_length;
                out_data.module_orientation     = original_data->rdData.module_orientation;
                out_data.session_leader_id      = original_data->rdData.session_leader_id;
            }
            else
            {
                out_data.chn_freq               = UINT8_MAX;
                out_data.cadence                = UINT8_MAX;
                out_data.walking_flag           = UINT8_MAX;
                out_data.vertical_oscillation   = UINT16_MAX;
                out_data.ground_contact_time    = UINT16_MAX;
                out_data.stance_time_percent    = UINT16_MAX;
                out_data.step_count             = UINT8_MAX;
                out_data.ground_contact_balance = UINT16_MAX;
                out_data.vertical_ratio         = UINT16_MAX;
                out_data.step_length            = UINT16_MAX;
                out_data.module_orientation     = UINT8_MAX;
                out_data.session_leader_id      = UINT16_MAX;
            }
            res = qw_dataserver_publish("ble_rd_sensor", &out_data, sizeof(rd_data_t));
            if (res != ERRO_CODE_OK)
            {
                // rt_kprintf("publish ble_bpwr_sensor failed %d\n", res);
            }
        }
            break;
		default:
			break;
	}
}

void sensor_evt_handler(uint16_t p_event, sensor_id_t *sensor_id, sensor_type_t sensor_type, sensor_radio_type_t sensor_radio_type, int16_t data)
{
	if(sensor_search_mode_get() & SENSOR_SEARCH_MODE_FACTORY)
	{
		return;
	}
    if ((EVENT_SENSOR_DATA_UPDATE != p_event) && (SENSOR_TYPE_INVALID != sensor_type))
    {
        SENSOR_EVT_LOG_D("event:0x%x type:%u radio:%u data:%d",p_event,sensor_type, sensor_radio_type, data);
        if (sensor_id)
        {
            if (sensor_radio_type == SENSOR_RADIO_TYPE_BLE)
            {
                SENSOR_EVT_LOG_D("sensor_id:%02x%02x%02x%02x%02x%02x", 
                        sensor_id->ble_mac_addr[0], sensor_id->ble_mac_addr[1], sensor_id->ble_mac_addr[2],
                        sensor_id->ble_mac_addr[3], sensor_id->ble_mac_addr[4], sensor_id->ble_mac_addr[5]);
            }
            else
            {
                SENSOR_EVT_LOG_D("sensor_id:%d", sensor_id->ant_id.id);
            }
        }
    }

    switch(p_event)
    {
		case  EVENT_SENSOR_CONNECT_STATUS_CHANGE:
			// rt_kprintf("sensor_evt_handler EVENT_SENSOR_CONNECT_STATUS_CHANGE\r\n");
			if(data == 0)
			{
                uint8_t sport_status = 0;
#ifdef SIMULATOR
                sport_status = get_simulator_sport_status();   // 初始化运动状态
#else
                sport_status = get_sport_status();   // 初始化运动状态
                SENSOR_EVT_LOG_D("sport_status:%d", sport_status);
#endif // !SIMULATOR
				event_sensor_data_update(sensor_id, sensor_type, sensor_radio_type, data, false);
                if (sport_status != 0)//只有当前处于运动状态才提醒传感器已断开
                {
                    sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_DISCONNECT_IN_MOTION);//运动中连接失败弹窗
                }
			}
			break;
        case EVENT_SENSOR_STATE_CHANGE:

        	break;
        case EVNET_SENOSR_DI2_CTRL:

        	break;
        case EVENT_SENSOR_BPWR_CALIB_SUCCESS:
            if (get_bpwr_calib_status() == BPWR_CALIB_STATUS_START)
            {
                set_bpwr_calib_status(BPWR_CALIB_STATUS_SUCCESS);
                set_bpwr_calib_result(data);
            }
        	break;
        case EVENT_SENSOR_BPWR_CALIB_FAILED:
            if (get_bpwr_calib_status() == BPWR_CALIB_STATUS_START)
            {
                set_bpwr_calib_status(BPWR_CALIB_STATUS_FAILED);
                set_bpwr_calib_result(0);
            }
        	break;
        case EVENT_SENSOR_RADAR_WARNING:

        	break;
        case EVENT_SENSOR_LOW_POWER:
            sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_LOW_POWER);
        	break;
        case EVENT_SENSOR_DATA_UPDATE:
			event_sensor_data_update(sensor_id, sensor_type, sensor_radio_type, data, true);
        	break;
		case EVENT_SENSOR_MANUAL_CONNECT_STATUS:
            if (data == 1)//手动连接成功
            {
                if(get_sport_status() != 0)//运动中连接成功
                {
                    sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_CONNECT_IN_MOTION);
                }
                else//非运动中连接成功
                {
                    sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION);
                }
            }
            else//手动连接失败
            {
                if(get_sport_status() != 0)//运动中连接失败
                {
                    sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_DISCONNECT_IN_MOTION);
                }
                else//非运动中连接成功
                {
                    sensor_popup_msgbox_handler(sensor_type, sensor_radio_type, sensor_id, data, enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION);
                }
            }
			break;
    }
}

void sensor_popup_msgbox_handler(sensor_type_t sensor_type, 
                                sensor_radio_type_t sensor_radio_type, 
                                sensor_id_t *sensor_id, 
                                int data,
                                GUI_MSGBOX msgbox_type)
{
    memset(&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    sensor_search_infor.sensor_type = sensor_type;
    sensor_search_infor.radio_type = sensor_radio_type;
    switch (msgbox_type)
    {
        case enumPOPUP_SENSOR_LOW_POWER:
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SENSOR_LOW_POWER, (void*) &(sensor_search_infor.sensor_type));
            break;

        case enumPOPUP_RECONNECT_SENSOR:
            if(sensor_id != NULL) //传感器ID不为空  
            {
                if(sensor_radio_type == SENSOR_RADIO_TYPE_BLE) //蓝牙
                {
                    memcpy(sensor_search_infor.sensor_id.ble_mac_addr, sensor_id->ble_mac_addr, BLE_GAP_ADDR_LEN);   
                }
                else if(sensor_radio_type == SENSOR_RADIO_TYPE_ANT) //ANT   
                {
                    sensor_search_infor.sensor_id.ant_id.id = sensor_id->ant_id.id;
                }
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_RECONNECT_SENSOR | enumGUI_POPUP_FORCE_FLAG, (void*) &sensor_search_infor); //重连弹窗
            }
            break;

        case enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION:
        case enumPOPUP_SENSOR_CONNECT_IN_MOTION:
            if (if_need_to_popup_msgbox(sensor_type))
            {
                sensor_search_infor.sensor_work_state = SENSOR_WORK_STATE_CONNECTED;
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, msgbox_type, (void*) &sensor_search_infor);
            }
            break;
        
        case enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION:
        case enumPOPUP_SENSOR_DISCONNECT_IN_MOTION:
            if (if_need_to_popup_msgbox(sensor_type))
            {
                sensor_search_infor.sensor_work_state = SENSOR_WORK_STATE_IDLE;
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, msgbox_type, (void*) &sensor_search_infor);
            }
            break;
        default:
            break;
    }
    return;
}
// 判断是否需要弹窗
bool if_need_to_popup_msgbox(sensor_type_t sensor_type)
{
    uint8_t sport_status = get_sport_status();
    if (sport_status == 0)//非运动状态
    {
        return true;
    }
    SPORTTYPE sport_type = get_current_sport_mode();
    int is_sport = get_sensor_is_sport(sport_type, sensor_type);
    if (is_sport == 0)
    {
        return false;//不支持的运动类型
    }
    else
    {
        return true;//支持的运动类型
    }
}
#ifndef SIMULATOR
#if SENSOR_DATA_TEST
static void algo_sensor_data_callback(const void* in, uint32_t len)
{
    if (in == NULL)
    {
        return;
    }

    hr_data_t*tmp = (hr_data_t*)in;
    printf("@@@@@@@@@ hrm= %d\n", tmp->heart_rate);
}

void algo_sensor_data_handle(void)
{
    static bool flag = false;

    if(!flag)
    {
        flag = true;
        optional_config_t config = {.sampling_rate = 0};
        uint8_t ret = qw_dataserver_subscribe("ble_hr_sensor", algo_sensor_data_callback, &config);
        if (ret != 0)
        {
            printf("@@@@@@@@@@@@algo_sensor_data_handle qw_dataserver_subscribe failed\n");
        }
        printf("@@@@@@@@@@@@algo_sensor_data_handle qw_dataserver_subscribe ok\n");
    }
    else
    {
        flag = false;
        uint8_t ret = qw_dataserver_unsubscribe("ble_hr_sensor", algo_sensor_data_callback);
        if (ret != 0)
        {
            printf("@@@@@@@@@@@@algo_sensor_data_handle qw_dataserver_unsubscribe failed\n");
        }
        printf("@@@@@@@@@@@@algo_sensor_data_handle qw_dataserver_unsubscribe ok\n");
    }
}

#endif // SENSOR_DATA_TEST
#endif // !SIMULATOR
