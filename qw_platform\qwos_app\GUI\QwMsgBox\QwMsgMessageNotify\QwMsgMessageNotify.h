/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwMsgMessageNotify.h
@Time    :   2024/12/26 19:41:39
*
**************************************************************************/
#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "qw_os_gui.h"
#include "QwFaBtn/QwFaBtn.h"
#include "QwHalfScreen/QwHalfScreen.h"
#include <touchgfx/hal/Types.hpp>

/**
 * @brief QwMsgMessageNotify 的弹框配置
 */
typedef struct 
{
    QHSTT_SUB_TITLE_TYPE sub_title_type; // 副区域标题类型
    const char* sub_title; // 副区域标题
    const char* sub_text;  // 副区域文本
    qhst_icon_t qhst_icon_info; //图标信息
    
}qw_msg_message_notify_info_t;

class QwMsgMessageNotify :
    public QwMsgBase
{
private:
    QwHalfScreen halfscreen_;

	Callback<QwMsgMessageNotify, void*, int, int> select_item_pos_;

    int start_tick_;
    qw_msg_message_notify_info_t msg_message_notify_info_;

	touchgfx::GenericCallback<>* push_message_details_;

    lv_font_t *title_font_;//副区域标题字体
    lv_font_t *text_font_;//副区域文本字体

    WideTextAction sub_text_wide_text_action_;//副文本的换行模式

public:
	QwMsgMessageNotify();
	virtual ~QwMsgMessageNotify() {};

    /**
     * @brief 初始化
     */
    virtual void setup() override;

    /**
     * @brief 刷新
     */
    virtual void on_notify() override;

    /**
     * @brief 刷新组件参数
     */
    virtual void update_parameter();

    /**
     * @brief 设置组件信息参数
     */
    virtual void set_info_parameter() = 0;

    /**
     * @brief 强制关闭弹窗
     */
    virtual void force_close() override;

    /**
     * @brief 继承自Drawable的tick事件
     */
    virtual void handleTickEvent() override;

    /**
     * @brief 继承自Drawable的key事件
     * @param c KeyEvent事件
     */
	virtual void handleKeyEvent(uint8_t c) override;

    /**
     * @brief 继承自Drawable的touch点击事件
     * @param event ClickEvent事件
     */
    virtual void handleClickEvent(const touchgfx::ClickEvent& evt) override;

    /**
     * @brief 继承自Drawable的tick事件
     * @param evt DragEvent事件
     */
	virtual void handleDragEvent(const touchgfx::DragEvent& evt) override;

    /**
     * @brief 继承自Drawable的tick事件
     * @param evt GestureEvent事件
     */
	virtual void handleGestureEvent(const touchgfx::GestureEvent& evt) override;

    /**
     * @brief 获取弹窗信息体指针
     */
    qw_msg_message_notify_info_t* get_msg_message_notify_info();

    /**
     * @brief 点击事件响应
     */
    void select_item_pos(void* item, int x, int y);

    void set_push_message_details_handle(GenericCallback<>& notify);

    void set_title_font(lv_font_t *font_);

    void set_text_font(lv_font_t *font_);

    /**
     * @brief 设置副文本的换行模式
     * @param action 换行模式
     */
    void set_sub_text_wide_text_action(WideTextAction action);
};
