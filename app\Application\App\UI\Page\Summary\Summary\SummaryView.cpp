/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SummaryView.cpp
@Time    :   2025-01-08 17:43:51
*
**************************************************************************/

#include "SummaryView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#ifndef SIMULATOR
#include "qw_fs.h"
#endif

#ifdef <PERSON>IMU<PERSON>
#define SUMMARY_SNAPSHORT_PATH "./RES_Disk/SummarySnapShort"
#else
#define SUMMARY_SNAPSHORT_PATH "0:/iGPSPORT/SummarySnapShort"
#endif


SummaryView::SummaryView(PageManager* manager) :
	PageView(manager),
	chart_index_(0),
	zone_index_(0),
	p_sport_type_(nullptr),p_fit_time_(nullptr),p_summary_sport_data_(nullptr),
	p_summary_detail_data_(nullptr),p_set_fit_name_(nullptr),p_get_lap_count_(nullptr),
	p_get_lap_info_(nullptr),p_get_summary_data_count_(nullptr),p_get_summary_data_(nullptr),
	p_get_zone_val_(nullptr),p_set_change_chart_type_(nullptr),p_set_route_page_name_(nullptr),
	update_sport_type_(this, &SummaryView::update_sport_type),
	update_fit_time_(this, &SummaryView::update_fit_time),
	update_summary_sport_data_(this, &SummaryView::update_summary_sport_data),
	update_summary_detail_data_(this, &SummaryView::update_summary_detail_data),
	update_chart_type_(this, &SummaryView::update_chart_type),
	update_title_(this, &SummaryView::update_title),
	update_left_(this, &SummaryView::update_left),
	update_right_(this, &SummaryView::update_right),
	update_total_(this, &SummaryView::update_total),
	update_timer_(this, &SummaryView::update_timer),
	update_train_info_(this, &SummaryView::update_train_info),
	update_route_page_name_(this, &SummaryView::update_route_page_name),
	pageUpdateDemonCallback_(this, &SummaryView::updateTestPageDemonContent),
	page_count_(0)
{
	for(int i = 0; i < 18; i++)
		page_type[i] = 0;
}

SummaryView::~SummaryView()
{

}
#include "qw_time_util.h"

static bool is_summmary_dir_exit(char *path)
{
#ifdef SIMULATOR
	int res = 0;
    res = chdir(SUMMARY_SNAPSHORT_PATH);
    if (res != 0)
    {
        res = mkdir(SUMMARY_SNAPSHORT_PATH);
        if (res != 0)
        {
            // assert(false && "[FAT_DATA]: create summmary dir err");
        }
    }
    else
    {
        QW_LOG_D("FAT_DATA:", "\n dir exist\n");
    }
#else
	QW_FIL *fp = NULL;
	char path[50] = {0};
	sprintf(path, "%s", SUMMARY_SNAPSHORT_PATH);
    QW_DIR dir;
    QW_FRESULT ret = qw_f_opendir(&dir, path);
    if (QW_OK != ret) {
        if (QW_ERROR == ret) {
            rt_kprintf("health dir mkdir = 0 FR_NO_FILESYSTEM");
            return false;
        } else {
            ret = qw_f_mkdir(path);
            if (QW_OK != ret) {
                rt_kprintf("health dir f_mkdir = %d", ret);
                return false;
            }
        }
    } else {
        qw_f_closedir(&dir);
    }
#endif
    return true;

}
bool SummaryView::create_summary_snapshort(int index, void *buf)
{
    QW_FIL *fp = NULL;
    uint32_t write_data_len = 0;
    lv_img_dsc_t resizeImgDsc;
	char path[50] = {0};

	sprintf(path, "%s/%d_Snapshot.bin", SUMMARY_SNAPSHORT_PATH, index);
    memset(&resizeImgDsc, 0, sizeof(lv_img_dsc_t));
    resizeImgDsc.header.h = HAL::DISPLAY_HEIGHT;
    resizeImgDsc.header.w = HAL::DISPLAY_WIDTH;
    resizeImgDsc.header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA;
    resizeImgDsc.header.always_zero = 0;
	rt_kprintf("@@@@start save index:%d, mesc:%u\n", index, (uint32_t)get_boot_msec());
    if (FS_OK == qw_f_open(&fp, path, QW_FA_WRITE | QW_FA_CREATE_ALWAYS))
    {
		qw_f_write_quick(fp, &resizeImgDsc.header, sizeof(lv_img_header_t), &write_data_len);	
		qw_f_write_quick(fp, buf, HAL::DISPLAY_WIDTH * HAL::DISPLAY_HEIGHT * sizeof(lv_color_t), &write_data_len);
        qw_f_close(fp);
		rt_kprintf("@@@@end save index:%d, mesc:%u\n", index, (uint32_t)get_boot_msec());
		return true;
    }
	return false;
}

bool SummaryView::is_summary_snapshort_exit(int index)
{
	QW_FIL *fp = NULL;
	char path[50] = {0};
	sprintf(path, "%s/%d_Snapshot.bin", SUMMARY_SNAPSHORT_PATH, index);
	if (FS_OK == qw_f_open(&fp, path, QW_FA_READ | QW_FA_OPEN_EXISTING)) {
		qw_f_close(fp);
		return true;
	}else{
		return false;
	}
}

bool SummaryView::get_summary_snapshort_buf_app(int index, void *buf)
{
#ifndef SIMULATOR
    FIL fp;
    lv_img_header_t header;
	char imgPath[50] = {0};
	sprintf(imgPath, "%s/%d_Snapshot.bin", SUMMARY_SNAPSHORT_PATH, index);

    uint8_t ret = f_open(&fp, imgPath, QW_FA_READ);
    if (FR_OK == ret)
    {
        uint32_t data_size, br;
        ret = f_read(&fp, &header, sizeof(lv_img_header_t), &br);
        if(FR_OK == ret)
        {
            FILINFO fileInfo;
            f_stat(imgPath, &fileInfo);
            data_size = fileInfo.fsize - sizeof(lv_img_header_t);
            rt_kprintf("fileInfo.size = %u dataSize:%u", fileInfo.fsize, data_size);

            f_lseek(&fp, sizeof(lv_img_header_t));
            f_read(&fp, static_cast<uint8_t *>(buf), data_size, &br);
            f_close(&fp);

			lv_memcpy(&cache_img_dsc[index%3].header, &header, sizeof(lv_img_header_t));
			cache_img_dsc[index%3].data_size = data_size;
			cache_img_dsc[index%3].data = static_cast<uint8_t *>(buf);
			rt_kprintf("@@@@get_summary_snapshort_buf end index:%d, mesc:%u\n", index, (uint32_t)get_boot_msec());
			return true;
        }
        else 
        {
            rt_kprintf("@@@read dial bin fail:%u", ret);
            f_close(&fp);
			return false;
        }
    }
    rt_kprintf("open %s fail:%u\n", imgPath, ret);
    return false;
#endif
}
bool SummaryView::get_summary_snapshort_buf_win32(int index, void *buf)
{
    lv_fs_file_t f;
    lv_img_header_t header;
	char imgPath[50] = {0};
	sprintf(imgPath, "%s/%d_Snapshot.bin", SUMMARY_SNAPSHORT_PATH, index);

    lv_fs_res_t res = lv_fs_open(&f, imgPath, LV_FS_MODE_RD);
    if (res == LV_FS_RES_OK) {
        uint32_t rn, size, data_size, br;

        res = lv_fs_read(&f, &header, sizeof(lv_img_header_t), &rn);
        if (res != LV_FS_RES_OK) {
            lv_fs_close(&f);
            rt_kprintf("read file failed\n");
            return false;
        }
        lv_fs_seek(&f, 0, LV_FS_SEEK_END);
        lv_fs_tell(&f, &size);

        data_size = size - sizeof(lv_img_header_t);
        lv_fs_seek(&f, sizeof(lv_img_header_t), LV_FS_SEEK_SET);
        lv_fs_read(&f, static_cast<uint8_t *>(buf), data_size, &br);
        lv_fs_close(&f);

        lv_memcpy(&cache_img_dsc[index%3].header, &header, sizeof(lv_img_header_t));
        cache_img_dsc[index%3].data_size = data_size;
        cache_img_dsc[index%3].data = static_cast<uint8_t *>(buf);
		rt_kprintf("@@@@get_summary_snapshort_buf end index:%d, mesc:%u\n", index, (uint32_t)get_boot_msec());
        return true;
    }
    return false;
}

bool SummaryView::get_summary_snapshort_buf(int index, void *buf)
{
	rt_kprintf("@@@@get_summary_snapshort_buf start index:%d, mesc:%u\n", index, (uint32_t)get_boot_msec());
#ifndef SIMULATOR
	return get_summary_snapshort_buf_app(index,buf);
#else
	return get_summary_snapshort_buf_win32(index,buf);
#endif
	
}
void SummaryView::updateTestPageDemonContent(QwSwipePageBase* page, int16_t pageIndex)
{
	// rt_kprintf("updateTestPageDemonContent: pageIndex = %d\n", pageIndex);
	bool hasExistingContent = (page->getFirstChild() != nullptr);
	if (page == nullptr && !hasExistingContent)
	{
		printf("ERROR: page is null!\n");
		return;
	}

	void* cashBuf[3] = {HAL::getScreenCachedBuffer(0), HAL::getScreenCachedBuffer(1), HAL::getAnimationBuffer()};
	if(page_type[pageIndex] == 1)
	{
		// return;
		if(!is_summary_snapshort_exit(pageIndex))
		{
			SnapshotWidget snapshot_;
			snapshot_.setPosition(0,0, 466,466);
			snapshot_.makeSnapshot(*page_base_[pageIndex], cashBuf[pageIndex%3], true);
			create_summary_snapshort(pageIndex, cashBuf[pageIndex%3]);
		}
		get_summary_snapshort_buf(pageIndex, cashBuf[pageIndex%3]);
		img_[pageIndex%3].setPosition(0,0,466,466);
		img_[pageIndex%3].setBitmap(Bitmap(&cache_img_dsc[pageIndex%3]));

		chart_page_base_[pageIndex%3].setPosition(0,0, 466,466);
		chart_page_base_[pageIndex%3].add(img_[pageIndex%3]);
		page->add(chart_page_base_[pageIndex%3]);
		printf("\n");
	}else
	{
		page->add(*page_base_[pageIndex]);
	}

}

void SummaryView::setup()
{
	is_summmary_dir_exit();
    add(bg_);
    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(lv_color_hex(0x000000));

	swipe_.setXY(0, 0);
    swipe_.setWidthHeight(HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    swipe_.setPageIndicatorBitmaps(Bitmap(&normalPage), Bitmap(&highlightedPage));
    swipe_.setPageIndicatorXY(12, 166);
    swipe_.setSwipeCutoff(30);
    swipe_.setEndSwipeElasticWidth(30);
	if(get_interval_train_is_show()) // 间歇训练结束
	{
		summary_interval_train_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
		summary_interval_train_con_.set_sport_type(*p_sport_type_->get_val(0));
		summary_interval_train_con_.setup();
		page_base_[page_count_] = &summary_interval_train_con_;
		page_type[page_count_] = 0;
		page_count_++;
        set_need_show_interval_train(false);
        set_cur_course_type(enum_course_type_course);
        close_sport_webgrid_page(get_current_sport_mode(), GRAPH_CONST_PAGE_OTHER);
	}

	//轨迹图
	summary_sport_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    summary_sport_con_.set_sport_type((SPORTTYPE) *p_sport_type_->get_val(0));
	summary_sport_con_.setup();
	summary_sport_con_.update_fit_time(&summary_sport_con_, p_fit_time_->get_parameter(0), 0);
	summary_sport_con_.update_sport_type(&summary_sport_con_, p_sport_type_->get_parameter(0), 0);
	for(int i = 0; i < (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL; i++)
	{
		summary_sport_con_.update_summary_data(&summary_sport_con_, p_summary_sport_data_->get_parameter(i), i);
	}
	page_base_[page_count_] = &summary_sport_con_;
	page_type[page_count_] = 0;
	page_count_++;

	//详细数据
	summary_detail_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	summary_detail_con_.set_sport_img_icon_index(*p_sport_type_->get_val(0));
	summary_detail_con_.setup();
	summary_detail_con_.update_sport_type(&summary_detail_con_, p_sport_type_->get_parameter(0), 0);
	for (int i = 0; i < MAX_SUMMARY_NUMBER; i++)
	{
		summary_detail_con_.update_summary_data(&summary_detail_con_, p_summary_detail_data_->get_parameter(i), i);
	}
	page_base_[page_count_] = &summary_detail_con_;
	page_type[page_count_] = 0;
	page_count_++;

	for(int i = 0; i <= FIT_CHART_TOTAL; i++)
	{
		p_set_change_chart_type_->notify(true);

		if((int)*p_chart_type_->get_val(0) == -1)
		{
			break;
		}

		switch (*p_chart_type_->get_val(0))
		{
		case CHART_SECTION_HRM:
    	case CHART_SECTION_PACE:
    	case CHART_SECTION_SPD:
    	case CHART_SECTION_PWR:
    	case CHART_SECTION_STEPFREQ:
    	case CHART_SECTION_STEPLEN:
    	case CHART_SECTION_CAD:
    	case CHART_SECTION_ALT:
		{	
			if(!is_summary_snapshort_exit(page_count_))
			{
				summary_chart_con_[chart_index_].setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
				summary_chart_con_[chart_index_].set_get_summary_data_count(p_get_summary_data_count_);
				summary_chart_con_[chart_index_].set_get_summary_data(p_get_summary_data_);
				summary_chart_con_[chart_index_].set_update_chart_type(p_chart_type_);
				summary_chart_con_[chart_index_].set_update_title(p_title_);
				summary_chart_con_[chart_index_].set_update_left(p_left_);
				summary_chart_con_[chart_index_].set_update_right(p_right_);
				summary_chart_con_[chart_index_].set_update_total(p_total_);
				summary_chart_con_[chart_index_].set_update_timer(p_timer_);
				summary_chart_con_[chart_index_].setup();
				page_base_[page_count_] = &summary_chart_con_[chart_index_];
			}
			page_type[page_count_] = 1;
			page_count_++;
			chart_index_++;
        	break;
    	}
		case ZONE_SECTION_HRM:
    	case ZONE_SECTION_PACE:
    	case ZONE_SECTION_SPD:
    	case ZONE_SECTION_PWR:
    	{
    	    summary_zone_con_[zone_index_].setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    	    summary_zone_con_[zone_index_].set_get_zone_val(p_get_zone_val_);
    	    summary_zone_con_[zone_index_].set_update_chart_type(p_chart_type_);
    	    summary_zone_con_[zone_index_].set_update_title(p_title_);
    	    summary_zone_con_[zone_index_].setup();
			page_base_[page_count_] = &summary_zone_con_[zone_index_];
			page_type[page_count_] = 0;
			page_count_++;
    	    zone_index_++;
    	    break;
    	}
    	case CHART_SECTION_RE_TIME:
		{
			//训练分析--恢复时间
			recovery_time_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
			recovery_time_con_.setup();
			page_base_[page_count_] = &recovery_time_con_;
			page_type[page_count_] = 0;
			page_count_++;
    	    break;
		}
    	case CHART_SECTION_EFF_LOAD:
		{
			if(!is_summary_snapshort_exit(page_count_))
			{
				summary_workout_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
				summary_workout_con_.set_sports_type(*p_sport_type_->get_val(0));
				summary_workout_con_.set_aerobic_effect(p_train_info_->get_val(0)->aerobic);
				summary_workout_con_.set_anaerobic_effect(p_train_info_->get_val(0)->anaerobic);
				summary_workout_con_.set_training_load(p_train_info_->get_val(0)->training_load);
				summary_workout_con_.setup();
				page_base_[page_count_] = &summary_workout_con_;
			}
			page_type[page_count_] = 1;
			page_count_++;
    	    break;
		}
    	case CHART_SECTION_OXY:
		{
			if(!is_summary_snapshort_exit(page_count_))
			{
				summary_vo2max_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
				summary_vo2max_con_.set_sports_type(*p_sport_type_->get_val(0));
				summary_vo2max_con_.setup();
				page_base_[page_count_] = &summary_vo2max_con_;
			}
			page_type[page_count_] = 1;
			page_count_++;
    	    break;
		}
    	case FIT_CHART_TOTAL:
    	{
    	    summary_lap_con_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
            summary_lap_con_.set_sport_type(*p_sport_type_->get_val(0));
    		summary_lap_con_.set_get_lap_count(p_get_lap_count_);
    		summary_lap_con_.set_get_lap_info(p_get_lap_info_);
			summary_lap_con_.setup();
			page_base_[page_count_] = &summary_lap_con_;
			page_type[page_count_] = 0;
			page_count_++;
        	break;
    	}
		default:
			break;
		}

        if (*p_chart_type_->get_val(0) == FIT_CHART_TOTAL)
        {
            break;
        }
	}
	// 设置swipe模式为动态模式
	swipe_.setDynamicCaching(true, page_count_);
	// 然后设置页面更新回调（这会触发页面内容更新）
	swipe_.setPageUpdateCallback(pageUpdateDemonCallback_);

	// 设置初始页面
	swipe_.setSelectedPage(0);
    add(swipe_);
}

void SummaryView::quit()
{

}

void SummaryView::handleTickEvent()
{
    swipe_.handleTickEvent();
}

void SummaryView::handleKeyEvent(uint8_t c)
{
    swipe_.handleKeyEvent(c);

	if (c == KEY_CLK_BACK)
    {
        char router_name[50] = {0};
        manager_->page_command("Summary", (int)Summary_CMD::GET_ROUTER, router_name);
        manager_->push(router_name);
    }
}

void SummaryView::handleClickEvent(const ClickEvent& evt)
{
    swipe_.handleClickEvent(evt);
}

void SummaryView::handleDragEvent(const DragEvent& evt)
{
    swipe_.handleDragEvent(evt);
}

void SummaryView::handleGestureEvent(const GestureEvent& evt)
{
    swipe_.handleGestureEvent(evt);

	if(evt.getType() == GestureEvent::SWIPE_HORIZONTAL && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
	{
		char router_name[50] = {0};
        manager_->page_command("Summary", (int)Summary_CMD::GET_ROUTER, router_name);
        manager_->push(router_name);
	}
}

// Notification Callback function
void SummaryView::set_on_set_fit_name(Notification<uint32_t>* command)
{
	p_set_fit_name_ = command;
}

void SummaryView::set_on_get_lap_count(Notification<int&>* command)
{
	p_get_lap_count_ = command;
}

void SummaryView::set_on_get_lap_info(Notification<int, SummaryModel::LAP_INFO&>* command)
{
	p_get_lap_info_ = command;
}

void SummaryView::set_on_get_summary_data_count(Notification<int16_t&>* command)
{
	p_get_summary_data_count_ = command;
}

void SummaryView::set_on_get_summary_data(Notification<int, int16_t&>* command)
{
	p_get_summary_data_ = command;
}

void SummaryView::set_on_get_zone_val(Notification<int, uint8_t&, uint32_t&>* command)
{
	p_get_zone_val_ = command;
}

void SummaryView::set_on_set_change_chart_type(Notification<bool>* command)
{
	p_set_change_chart_type_ = command;
}

void SummaryView::set_on_set_route_page_name(Notification<const char*>* command)
{
	p_set_route_page_name_ = command;
}

// ObserverDrawable Callback function
void SummaryView::set_update_sport_type(ObserverDrawable<Drawable, SPORTTYPE, 1>* observer)
{
	if (observer != nullptr)
	{
		p_sport_type_ = observer;
		observer->bind_ctrl(0, summary_sport_con_);
		observer->bind_notify(update_sport_type_);
	}
}

void SummaryView::update_sport_type(Drawable* ctrl, Parameters<SPORTTYPE>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_fit_time(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_fit_time_ = observer;
		observer->bind_ctrl(0, summary_sport_con_);
		observer->bind_notify(update_fit_time_);
	}
}

void SummaryView::update_fit_time(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_summary_sport_data(ObserverDrawable<Drawable, SummaryModel::SPORT_INFO, (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL>* observer)
{
	if (observer != nullptr)
	{
		p_summary_sport_data_ = observer;
		for (int i = 0; i < (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL; i++)
		{
			observer->bind_ctrl(i, summary_sport_con_);
		}
		observer->bind_notify(update_summary_sport_data_);
	}
}

void SummaryView::update_summary_sport_data(Drawable* ctrl, Parameters<SummaryModel::SPORT_INFO>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr && idx < (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL)
	{

	}
}

void SummaryView::set_update_summary_detail_data(ObserverDrawable<Drawable, QwItemSummary::INFO, MAX_SUMMARY_NUMBER>* observer)
{
	if (observer != nullptr)
	{
		p_summary_detail_data_ = observer;
		for (int i = 0; i < MAX_SUMMARY_NUMBER; i++)
		{
			observer->bind_ctrl(i, summary_detail_con_);
		}
		observer->bind_notify(update_summary_detail_data_);
	}
}

void SummaryView::update_summary_detail_data(Drawable* ctrl, Parameters<QwItemSummary::INFO>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr && idx < MAX_SUMMARY_NUMBER)
	{

	}
}

void SummaryView::set_update_chart_type(ObserverDrawable<Drawable, FIT_CHART_SECTION, 1>* observer)
{
	if (observer != nullptr)
	{
		p_chart_type_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_chart_type_);
	}
}

void SummaryView::update_chart_type(Drawable* ctrl, Parameters<FIT_CHART_SECTION>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_title(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_title_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_title_);
	}
}

void SummaryView::update_title(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_left(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_left_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_left_);
	}
}

void SummaryView::update_left(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_right(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_right_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_right_);
	}
}

void SummaryView::update_right(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_total(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_total_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_total_);
	}
}

void SummaryView::update_total(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_timer(ObserverDrawable<Drawable, uint32_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_timer_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_timer_);
	}
}

void SummaryView::update_timer(Drawable* ctrl, Parameters<uint32_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_route_page_name(ObserverDrawable<Drawable, const char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_route_page_name_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_route_page_name_);
	}
}

void SummaryView::update_route_page_name(Drawable* ctrl, Parameters<const char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SummaryView::set_update_train_info(ObserverDrawable<Drawable, SummaryModel::TRAIN_INFO, 1>* observer)
{
	if (observer != nullptr)
	{
		p_train_info_ = observer;
		observer->bind_ctrl(0, swipe_);
		observer->bind_notify(update_train_info_);
	}
}

void SummaryView::update_train_info(Drawable* ctrl, Parameters<SummaryModel::TRAIN_INFO>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

// custom function

