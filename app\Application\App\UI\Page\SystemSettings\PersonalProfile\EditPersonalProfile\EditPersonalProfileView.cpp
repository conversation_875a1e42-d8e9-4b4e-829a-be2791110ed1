/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   EditPersonalProfileView.cpp
@Time    :   2024/12/25 10:55:34
*
**************************************************************************/
#include "EditPersonalProfileView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "GUI/QwGUIKey.h"
#include "Image/images.h"
#include "service_datetime.h"

typedef enum {
	AUTO_DETECTION,//自动检测
	CUSTOM,//手动输入
	PROFILE_ITEM_MAX_,
}EDIT_PERSONAL_INFO_ITEM;

static const char* g_display_edit_personal_profile_item_text[] = {
    "_auto_detection_1",//自动检测
    "_custom",//手动输入
};
TM_DECLARE(g_display_edit_personal_profile_item_text)


const qw_menu_info g_edit_personal_profile_item_info[] = {
	{(const int)ITEM_TYPES::ITEM_SWITCH, g_display_edit_personal_profile_item_text[AUTO_DETECTION]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_edit_personal_profile_item_text[CUSTOM]},
};

static const char* g_display_edit_personal_profile_title[] = {
    "_running_maximum_heart_rate",             //跑步最大心率
    "_running_lactate_threshold_heart_rate",   //跑步乳酸阈值
    "_cycling_ftp",                            //骑行FTP
    "_user_information",                       //用户信息
};
TM_DECLARE(g_display_edit_personal_profile_title)

static const char *g_display_custom_data_title[] = {
    "_heart_rate",      //心率
    "FTP",              //骑行FTP
	"_gender",//性别
	"_date_of_birth",//出生日期
	"_height",//身高
	"_weight",//体重
	"_wearing_hand",//佩戴手
};
TM_DECLARE(g_display_custom_data_title)

typedef enum {
	GENDER,//性别
	BIRTH,//出生日期
	HEIGHT,//身高
	WEIGHT,//体重
    // HAND_HABIT,//佩戴手
    USR_INFO_MAX_,   //用户信息最大值
} USER_INFO_DATA_ITEM;

static const char* g_display_user_info_item_text[] = {
    "_gender",//性别
    "_date_of_birth",//出生日期
    "_height",//身高
    "_weight",//体重
    // "_wearing_hand",//佩戴手
};
TM_DECLARE(g_display_user_info_item_text)

static const char* g_display_user_info_title[] = {
    "_user_information",//用户信息
};
TM_DECLARE(g_display_user_info_title)


static const char* g_display_user_info_gender_text[] = {
	"_female",//女性
	"_male",//男性
};
TM_DECLARE(g_display_user_info_gender_text)

static const char* g_display_user_info_hand_text[] = {
    "_left_hand",//左手
	"_right_hand",//右手
};
TM_DECLARE(g_display_user_info_hand_text)

const qw_menu_info g_display_user_info_item_info[] = {
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_user_info_item_text[GENDER]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_user_info_item_text[BIRTH]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_user_info_item_text[HEIGHT]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_user_info_item_text[WEIGHT]},
	// {(const int)ITEM_TYPES::ITEM_TEXT, g_display_user_info_item_text[HAND_HABIT]},
};

EditPersonalProfileView::EditPersonalProfileView(PageManager *manager)
    : QwMenuView(manager)
    , p_custom_data_unit(nullptr)
    , custom_data_item_number(0)
    , current_value(0)
    , user_info_index(0)
    , custom_data_min(100)
    , custom_data_max(220)
    , custom_data_default(180)
    , p_type_(nullptr)
    , p_custom_data_(nullptr)
    , p_auto_detect_flag_(nullptr)
    , p_weight_(nullptr)
    , p_height_(nullptr)
    , p_gender_(nullptr)
    , p_hand_habit_(nullptr)
    , p_year_(nullptr)
    , p_mon_(nullptr)
    , p_item_select_(nullptr)
    , p_detect_data_(nullptr)
    , p_set_value_(nullptr)
    , p_edit_data_type_(nullptr)
    , p_set_weight_(nullptr)
    , p_set_height_(nullptr)
    , p_set_gender_(nullptr)
    , p_set_hand_habit_(nullptr)
    , p_set_year_(nullptr)
    , p_set_mon_(nullptr)
    , p_set_item_select_(nullptr)
    , on_detect_data_(this, &EditPersonalProfileView::on_click_detect_data)
    , on_set_value_(this, &EditPersonalProfileView::on_click_set_value)
    , on_edit_data_type_(this, &EditPersonalProfileView::on_click_edit_data_type)
    , on_set_weight_(this, &EditPersonalProfileView::on_click_set_weight)
    , on_set_height_(this, &EditPersonalProfileView::on_click_set_height)
    , on_set_gender_(this, &EditPersonalProfileView::on_click_set_gender)
    , on_set_hand_habit_(this, &EditPersonalProfileView::on_click_set_hand_habit)
    , on_set_year_(this, &EditPersonalProfileView::on_click_set_year)
    , on_set_mon_(this, &EditPersonalProfileView::on_click_set_mon)
    , on_set_item_select_(this, &EditPersonalProfileView::on_click_set_item_select)
    , update_type_(this, &EditPersonalProfileView::update_type)
    , update_custom_data_(this, &EditPersonalProfileView::update_custom_data)
    , update_auto_detect_flag_(this, &EditPersonalProfileView::update_auto_detect_flag)
    , select_item_pos_(this, &EditPersonalProfileView::select_item_pos)
    , custom_data_input_confirm_(this, &EditPersonalProfileView::custom_data_input_confirm)
    , custom_data_input_cancel_(this, &EditPersonalProfileView::custom_data_input_cancel)
    , custom_data_input_create_(this, &EditPersonalProfileView::custom_data_input_create_item)
    , update_weight_(this, &EditPersonalProfileView::update_weight)
    , update_height_(this, &EditPersonalProfileView::update_height)
    , update_gender_(this, &EditPersonalProfileView::update_gender)
    , update_hand_habit_(this, &EditPersonalProfileView::update_hand_habit)
    , update_year_(this, &EditPersonalProfileView::update_year)
    , update_mon_(this, &EditPersonalProfileView::update_mon)
    , update_item_select_(this, &EditPersonalProfileView::update_item_select)
    , custom_data_gender_create_(this, &EditPersonalProfileView::custom_data_gender_create_item)
    , custom_data_gender_confirm_(this, &EditPersonalProfileView::custom_data_gender_confirm)
    , custom_data_gender_cancel_(this, &EditPersonalProfileView::custom_data_gender_cancel)
    , custom_data_birth_create_(this, &EditPersonalProfileView::custom_data_birth_create_item)
    , custom_data_birth_confirm_(this, &EditPersonalProfileView::custom_data_birth_confirm)
    , custom_data_birth_cancel_(this, &EditPersonalProfileView::custom_data_birth_cancel)
{}

EditPersonalProfileView::~EditPersonalProfileView()
{}

void EditPersonalProfileView::setup()
{
	switch(*p_type_->get_val(0))
	{
		case EDIT_PERSONAL_PROFILE_RIDE_FTP_://骑行FTP
			custom_data_min = 1;
			custom_data_max = 999;
			custom_data_default = 200;
			p_custom_data_unit = const_cast<char*>("w");
			custom_data_item_number = 1;
			current_value = *p_custom_data_->get_val(0);
			break;
		case EDIT_PERSONAL_PROFILE_HRM_MAX_://跑步最大心率
			custom_data_min = 100;
			custom_data_max = 220;
			custom_data_default = 180;
			p_custom_data_unit = const_cast<char*>("bpm");
			custom_data_item_number = 0;
			current_value = *p_custom_data_->get_val(0);
			break;
		case EDIT_PERSONAL_PROFILE_HRM_LACTIC_://跑步乳酸阈值
			custom_data_min = 100;
			custom_data_max = 220;
			custom_data_default = 150;
			p_custom_data_unit = const_cast<char*>("bpm");
			custom_data_item_number = 0;
			current_value = *p_custom_data_->get_val(0);
			break;
		default:
			p_custom_data_unit = nullptr;
			custom_data_item_number = 0;
        break;
    }
    uint8_t focus_index = *p_item_select_->get_val(0);
	if (*p_type_->get_val(0) < EDIT_PERSONAL_PROFILE_USER_INFO_)
	{
		int items_num_ = 1;//自动检测
		if (*p_auto_detect_flag_->get_val(0) == false)
		{
			items_num_ += 1;//手动输入
		}
		QwMenuView::show_menu(items_num_, focus_index, _TM(g_display_edit_personal_profile_title[*p_type_->get_val(0)]));
	}
	else
	{
		QwMenuView::show_menu(USR_INFO_MAX_, focus_index, _TM(g_display_edit_personal_profile_title[*p_type_->get_val(0)]));
	}
	// bool state = *p_auto_detect_flag_->get_val(0);
	// item_update_show(0, ITEM_UPDATE_TYPES::ITEM_UPDATE_RIGHT_IMAGE, &state);
	// update_list_number_and_sub_title(state);
}

void EditPersonalProfileView::quit()
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void EditPersonalProfileView::jump(const char* page_name)
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void EditPersonalProfileView::handleTickEvent()
{
	if (custom_data_input_.is_slide_in())
    {
        custom_data_input_.handleTickEvent();
    }
	else if (custom_data_gender_.is_slide_in())
    {
        custom_data_gender_.handleTickEvent();
    }
	else if (custom_data_birth_.is_slide_in())
    {
        custom_data_birth_.handleTickEvent();
    }
	else
	{
		QwMenuView::list_.handleTickEvent();
	}
}

void EditPersonalProfileView::handleKeyEvent(uint8_t c)
{
	if (custom_data_input_.is_slide_in())
    {
        custom_data_input_.handleKeyEvent(c);
    }
	else if (custom_data_gender_.is_slide_in())
    {
        custom_data_gender_.handleKeyEvent(c);
    }
	else if (custom_data_birth_.is_slide_in())
    {
        custom_data_birth_.handleKeyEvent(c);
    }
	else
	{
		QwMenuView::list_.handleKeyEvent(c);
		if (c == KEY_CLK_START)
		{
			int select_index = QwMenuView::list_.get_focus_item_index();
			switch_to_app(select_index, CUSTOM_DATA_CENTER_BOUNDARY, 0);
		}
		else if (c == KEY_CLK_BACK)
		{
			manager_->push("PersonalProfile");
        }
    }
}

void EditPersonalProfileView::handleClickEvent(const ClickEvent& evt)
{
	if (custom_data_input_.is_slide_in())
    {
        custom_data_input_.handleClickEvent(evt);
    }
	else if (custom_data_gender_.is_slide_in())
    {
        custom_data_gender_.handleClickEvent(evt);
    }
	else if (custom_data_birth_.is_slide_in())
    {
        custom_data_birth_.handleClickEvent(evt);
    }
	else
	{
		QwMenuView::list_.handleClickEvent(evt);
	}
}

void EditPersonalProfileView::handleDragEvent(const DragEvent& evt)
{
	if (custom_data_input_.is_slide_in())
    {
        custom_data_input_.handleDragEvent(evt);
    }
	else if (custom_data_gender_.is_slide_in())
    {
        custom_data_gender_.handleDragEvent(evt);
    }
	else if (custom_data_birth_.is_slide_in())
    {
        custom_data_birth_.handleDragEvent(evt);
    }
	else
	{
		QwMenuView::list_.handleDragEvent(evt);
	}
}

void EditPersonalProfileView::handleGestureEvent(const GestureEvent& evt)
{
	if (custom_data_input_.is_slide_in())
    {
        custom_data_input_.handleGestureEvent(evt);
    }
	else if (custom_data_gender_.is_slide_in())
    {
        custom_data_gender_.handleGestureEvent(evt);
    }
	else if (custom_data_birth_.is_slide_in())
    {
        custom_data_birth_.handleGestureEvent(evt);
    }
	else
	{
		QwMenuView::list_.handleGestureEvent(evt);
        if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
		{
			manager_->push("PersonalProfile");
		}
	}
}

// Notification Callback function
void EditPersonalProfileView::set_on_detect_data(Notification<bool>* command)
{
	p_detect_data_ = command;
}

void EditPersonalProfileView::on_click_detect_data(bool t1)
{
	if (p_detect_data_ != nullptr)
	{
		p_detect_data_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_value(Notification<uint16_t>* command)
{
	p_set_value_ = command;
}

void EditPersonalProfileView::on_click_set_value(uint16_t t1)
{
	if (p_set_value_ != nullptr)
	{
		p_set_value_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_edit_data_type(Notification<uint8_t>* command)
{
	p_edit_data_type_ = command;
}

void EditPersonalProfileView::on_click_edit_data_type(uint8_t t1)
{
	if (p_edit_data_type_ != nullptr)
	{
		p_edit_data_type_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_weight(Notification<uint16_t>* command)
{
	p_set_weight_ = command;
}

void EditPersonalProfileView::on_click_set_weight(uint16_t t1)
{
	if (p_set_weight_ != nullptr)
	{
		p_set_weight_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_height(Notification<uint8_t>* command)
{
	p_set_height_ = command;
}

void EditPersonalProfileView::on_click_set_height(uint8_t t1)
{
	if (p_set_height_ != nullptr)
	{
		p_set_height_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_gender(Notification<uint8_t>* command)
{
	p_set_gender_ = command;
}

void EditPersonalProfileView::on_click_set_gender(uint8_t t1)
{
	if (p_set_gender_ != nullptr)
	{
		p_set_gender_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_hand_habit(Notification<uint8_t>* command)
{
	p_set_hand_habit_ = command;
}

void EditPersonalProfileView::on_click_set_hand_habit(uint8_t t1)
{
	if (p_set_hand_habit_ != nullptr)
	{
		p_set_hand_habit_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_year(Notification<uint16_t>* command)
{
	p_set_year_ = command;
}

void EditPersonalProfileView::on_click_set_year(uint16_t t1)
{
	if (p_set_year_ != nullptr)
	{
		p_set_year_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_mon(Notification<uint8_t>* command)
{
	p_set_mon_ = command;
}

void EditPersonalProfileView::on_click_set_mon(uint8_t t1)
{
	if (p_set_mon_ != nullptr)
	{
		p_set_mon_->notify(t1);
	}
}

void EditPersonalProfileView::set_on_set_item_select(Notification<uint8_t>* command)
{
	p_set_item_select_ = command;
}

void EditPersonalProfileView::on_click_set_item_select(uint8_t t1)
{
	if (p_set_item_select_ != nullptr)
	{
		p_set_item_select_->notify(t1);
	}
}

// ObserverDrawable Callback function
void EditPersonalProfileView::set_update_type(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_type_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_type_);
	}
}

void EditPersonalProfileView::update_type(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_custom_data(ObserverDrawable<Drawable, uint16_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_custom_data_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_custom_data_);
	}
}

void EditPersonalProfileView::update_custom_data(Drawable* ctrl, Parameters<uint16_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_auto_detect_flag(ObserverDrawable<Drawable, bool, 1>* observer)
{
	if (observer != nullptr)
	{
		p_auto_detect_flag_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_auto_detect_flag_);
	}
}

void EditPersonalProfileView::update_auto_detect_flag(Drawable* ctrl, Parameters<bool>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
		char sub_title[20] = {0};
		bool status = data->get_val();
		//刷新右侧开关图片
		item_update_show(0, ITEM_UPDATE_TYPES::ITEM_UPDATE_RIGHT_IMAGE, &status);
		//刷新子标题和列表个数
		update_list_number_and_sub_title(status);
		//刷新子标题
		if (status)
		{
			get_sub_title(sub_title);
		}
		item_update_show(0, ITEM_UPDATE_TYPES::ITEM_UPDATE_SUB_TITLE, sub_title);
	}
}

// custom function
void EditPersonalProfileView::set_item_info(item_info_t* info, int index)
{
	if (info == nullptr)
    {
    	assert(false && "[PersonalProfileView:8001]set_item_info index is out");
		return;
    }

	if(*p_type_->get_val(0) < EDIT_PERSONAL_PROFILE_USER_INFO_)
	{
		if(index >= PROFILE_ITEM_MAX_)
		{
			assert(false && "[PersonalProfileView:8001]set_item_info index is out");
			return;
		}
	}
	else
	{
		if(index >= USR_INFO_MAX_)
		{
			assert(false && "[PersonalProfileView:8001]set_item_info index is out");
			return;
		}
	}

    info->item_index = index;
	memset(info->title, 0, sizeof(info->title));
	memset(info->subtitle, 0, sizeof(info->subtitle));
	if (*p_type_->get_val(0) < EDIT_PERSONAL_PROFILE_USER_INFO_)
	{
		info->type = (ITEM_TYPES)g_edit_personal_profile_item_info[index].type;
		memcpy(info->title, _TM(g_edit_personal_profile_item_info[index].name), strlen(_TM(g_edit_personal_profile_item_info[index].name)));
		get_sub_title(info->subtitle);
		if (index == AUTO_DETECTION)
		{
			if (*p_auto_detect_flag_->get_val(0) == false)
			{
				memset(info->subtitle, 0, sizeof(info->subtitle));
			}
			info->is_selected = *p_auto_detect_flag_->get_val(0);//
		}
	}
	else
	{
		info->type = (ITEM_TYPES)g_display_user_info_item_info[index].type;
		memcpy(info->title, _TM(g_display_user_info_item_info[index].name), strlen(_TM(g_display_user_info_item_info[index].name)));
		user_info_sub_title(index, info->subtitle);
	}

	info->item_text_info.text_align = 1;
}

void EditPersonalProfileView::set_item_notify(QwMenuItem* item, int index)
{
	item->set_select_pos_handle(select_item_pos_);
}

void EditPersonalProfileView::select_item_pos(void* item, int x, int y)
{
	char show_text[20] = {0};
	if (item == nullptr)
	{
		assert("[EditPersonalProfileView:8001] item is nullptr");
		return;
	}
	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	if (item_ == nullptr)
	{
		assert("[EditPersonalProfileView:8001] item_ not is QwMenuItem");
		return;
	}
    // 6.获取子菜单在当前menu中的index
    int select_index = (int)item_->get_user_data();
	switch_to_app(select_index, x, y);
}

void EditPersonalProfileView::switch_to_app(int select_index, int x, int y)
{
	if (*p_type_->get_val(0) < EDIT_PERSONAL_PROFILE_USER_INFO_)
	{
		if ((select_index < 0) || (select_index >= PROFILE_ITEM_MAX_))
		{
			return;
		}
	}
	else
	{
		if ((select_index < 0) || (select_index >= USR_INFO_MAX_))
		{
			return;
		}
	}
	if (*p_type_->get_val(0) < EDIT_PERSONAL_PROFILE_USER_INFO_)
	{
		if (select_index == AUTO_DETECTION)
		{
			if (x < CUSTOM_DATA_LEFT_BOUNDARY || x > CUSTOM_DATA_RIGHT_BOUNDARY)
			{
				GUI_VIEW_LOG_E("%s %d out of range x=%d y=%d\n", __func__,__LINE__,x,y);
				return;
			}
			bool status = (bool)(!(*p_auto_detect_flag_->get_val(0)));
			//设置右侧开关状态到model
			on_click_detect_data(status);
		}
		else if (select_index == CUSTOM)
		{
			show_custom_data_input();
		}
	}
	else
	{
		user_info_index = select_index;
		if (select_index == GENDER)//性别
		{
			show_custom_data_gender();
		}
		else if (select_index == BIRTH)//出生日期`
		{
			show_custom_data_birth();
		}
        else if (select_index == HEIGHT)//身高
        {
            if (get_unit_length() == 0)
            {
                p_custom_data_unit = const_cast<char*>("in.");
                current_value = static_cast<int>(*p_height_->get_val(0)*0.393701 + 0.5f); // 厘米转英尺显示
            }
            else
            {
                p_custom_data_unit = const_cast<char*>("c\nm");
                current_value = *p_height_->get_val(0);
            }
            custom_data_item_number = 4;//身高
            show_custom_data_input();
        }
        else if (select_index == WEIGHT)//体重
        {
            if (get_unit_weight() == 0)
            {
                p_custom_data_unit = const_cast<char*>("l\nb");
                current_value = static_cast<int>((*p_weight_->get_val(0) * 2.20462 / 10) + 0.5f); // 磅转千克显示
            }
            else
            {
                p_custom_data_unit = const_cast<char*>("k\ng");
                current_value = *p_weight_->get_val(0) / 10;
            }
            custom_data_item_number = 5;//体重
            show_custom_data_input();
        }
		// else if (select_index == HAND_HABIT)//佩戴手
		// {
		// 	show_custom_data_gender();
		// }
	}
}

void EditPersonalProfileView::show_custom_data_input()   //自定义数据
{
    custom_data_input_.set_digital_input_type(INPUT_DIGITAL_CUSTOM);
    custom_data_input_.set_digital_input_title(_TM(g_display_custom_data_title[custom_data_item_number]));
    int value = current_value;
    for (int i = 0; i < 3; i++)
    {
        custom_data_input_.set_section_items_number(2-i, 10);
        custom_data_input_.set_section_default(2-i, value%10);
        value= value/10;
    }

    if (p_custom_data_unit != nullptr)
    {
        //当选中个人资料-->>身高时，设置单位
        if (*p_type_->get_val(0) == EDIT_PERSONAL_PROFILE_USER_INFO_ && QwMenuView::list_.get_focus_item_index() == HEIGHT)
        {
             custom_data_input_.set_unit_flag(true, p_custom_data_unit);
        }
    }
    custom_data_input_.set_create_section_handle(custom_data_input_create_);
    custom_data_input_.set_confirm_handle(custom_data_input_confirm_);
    custom_data_input_.set_cancel_handle(custom_data_input_cancel_);
    custom_data_input_.show_input(true, ANIMAT_TYPE::EAST, this->getRootContainer());
    custom_data_input_.setVisible(true);
}

void EditPersonalProfileView::custom_data_input_confirm()
{
    int index = custom_data_input_.get_section_val(0);
    int16_t custom_data = custom_data_min + index;
    int value = 0;

    for (int i = 0; i < 3; i++)
    {
        value = custom_data_input_.get_section_val(i) * pow(10, 2-i) + value;
    }

    switch (user_info_index)
    {
        case HEIGHT:
        if (get_unit_length() == 0)
        {
                value = static_cast<int>(value * 2.54 + 0.5f); // 英尺转厘米保存
            }
            // 限制身高范围
        if ((value > HEIGHT_MAX_VALUE) || (value < HEIGHT_MIN_VALUE))
        {
                value = HEIGHT_DEFAULT_VALUE;
            }
            on_click_set_height(value);
            break;
        case WEIGHT:
        if (get_unit_weight() == 0)
        {
                value = static_cast<int>(value * 4.5359237);
        }
        else
        {
                value = value * 10;
            }
            on_click_set_weight(value);
            break;
        default:
            on_click_set_value(value);
            break;
    }

    manager_->push("EditPersonalProfile");
}

void EditPersonalProfileView::custom_data_input_cancel()
{
    QwMenuView::list_.stop_list_animate();
    QwMenuView::list_.set_focus_to_item(QwMenuView::list_.get_focus_item_index(), false);
    custom_data_input_.hide_input(true, ANIMAT_TYPE::WEST, this->getRootContainer());
}

void EditPersonalProfileView::custom_data_input_create_item(Drawable* item, QwInput_item_info* info)
{
    if (item != nullptr && info != nullptr)
    {
        int16_t value = info->idx_menu;
        char text[20] = { 0 };
        WatchesInputItem* p_text = (WatchesInputItem*)item;


        sprintf(text, "%d", value);
        p_text->set_text_str(info, text);
    }
}

void EditPersonalProfileView::custom_data_gender_create_item(Drawable* item, QwInput_item_info* info)
{
    if (item != nullptr && info != nullptr)
    {
        int16_t value = info->idx_menu;
        char text[20] = { 0 };
        WatchesInputItem* p_text = (WatchesInputItem*)item;
        if (info->idx_section == 0)
        {
            if (value == 0)
			{
				if (user_info_index == GENDER)
				{
					sprintf(text, "%s", _TM(g_display_user_info_gender_text[0]));//女
				}
				else
				{
					sprintf(text, "%s", _TM(g_display_user_info_hand_text[0]));//左手
                }
			}
			else
			{
				if (user_info_index == GENDER)
				{
					sprintf(text, "%s", _TM(g_display_user_info_gender_text[1]));//男
				}
				else
				{
					sprintf(text, "%s", _TM(g_display_user_info_hand_text[1]));//右手
				}
			}
        }

        p_text->set_text_str(info, text);
        p_text->set_text_font(info, &PUBLIC_NO_44_M_FONT);
    }
}

void EditPersonalProfileView::custom_data_gender_confirm()
{
    int index = custom_data_gender_.get_section_val(0);
	if (user_info_index == GENDER)
	{
		on_click_set_gender(index);
	}
	else
	{
		on_click_set_hand_habit(index);
	}

    manager_->push("EditPersonalProfile");
}

void EditPersonalProfileView::custom_data_gender_cancel()
{
    QwMenuView::list_.stop_list_animate();
    QwMenuView::list_.set_focus_to_item(QwMenuView::list_.get_focus_item_index(), false);
    custom_data_gender_.hide_input(true, ANIMAT_TYPE::WEST, this->getRootContainer());
}

void EditPersonalProfileView::show_custom_data_gender()   //性别
{
	int16_t value = 0;
    custom_data_gender_.set_digital_input_type(INPUT_DIGITAL_CUSTOM);
	if (user_info_index == GENDER)
	{
		value =*p_gender_->get_val(0);
        custom_data_gender_.set_digital_input_title(_TM(g_display_custom_data_title[2]));   //性别
	}
	else
	{
		value = *p_hand_habit_->get_val(0);
		custom_data_gender_.set_digital_input_title(_TM(g_display_custom_data_title[6]));//佩戴手
    }
    custom_data_gender_.set_section_items_number(0, 2);                                     //
    custom_data_gender_.set_section_default(0, value);                                      //
    custom_data_gender_.set_create_section_handle(custom_data_gender_create_);
    custom_data_gender_.set_confirm_handle(custom_data_gender_confirm_);
    custom_data_gender_.set_cancel_handle(custom_data_gender_cancel_);
    custom_data_gender_.show_input(true, ANIMAT_TYPE::EAST, this->getRootContainer());
    custom_data_gender_.setVisible(true);
}

void EditPersonalProfileView::custom_data_birth_create_item(Drawable* item, QwInput_item_info* info)
{
    if (item != nullptr && info != nullptr)
    {
        int16_t value = info->idx_menu;
        char text[20] = { 0 };
        WatchesInputItem* p_text = (WatchesInputItem*)item;

        if (info->idx_section == 0)//年
        {
			int year = 1900 + value;
			sprintf(text, "%d", year);
        }
		else//月
		{
			int mon = value + 1;
			sprintf(text, "%02d", mon);
		}
        p_text->set_text_str(info, text);
    }
}

void EditPersonalProfileView::custom_data_birth_confirm()
{
    int index = custom_data_birth_.get_section_val(0);

    int year = 1900 + index;
    on_click_set_year(year);

	int mon = custom_data_birth_.get_section_val(1);
	mon += 1;
	on_click_set_mon(mon);

    manager_->push("EditPersonalProfile");
}

void EditPersonalProfileView::custom_data_birth_cancel()
{
    QwMenuView::list_.stop_list_animate();
    QwMenuView::list_.set_focus_to_item(QwMenuView::list_.get_focus_item_index(), false);
    custom_data_birth_.hide_input(true, ANIMAT_TYPE::WEST, this->getRootContainer());
}

void EditPersonalProfileView::show_custom_data_birth()//出生日期
{
	qw_tm_t rtc_time = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int16_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(gmt, &rtc_time, timezone);
    int year_count = rtc_time.tm_year - 1900;

	if ((*p_year_->get_val(0) < 1900) || (*p_year_->get_val(0) > rtc_time.tm_year))
	{
		*p_year_->get_val(0) = 1900;
	}
	if ((*p_mon_->get_val(0) < 1) || (*p_mon_->get_val(0) > 12))
	{
		*p_mon_->get_val(0) = 1;
	}
    custom_data_birth_.set_digital_input_type(INPUT_DIGITAL_CUSTOM);
    custom_data_birth_.set_digital_input_title(_TM(g_display_custom_data_title[3]));   //
    custom_data_birth_.set_section_items_number(0, year_count + 1);                    //
    custom_data_birth_.set_section_items_number(1, 12);                                //
    custom_data_birth_.set_section_default(0, *p_year_->get_val(0) - 1900);            //
    custom_data_birth_.set_section_default(1, *p_mon_->get_val(0) - 1);                //
    custom_data_birth_.set_create_section_handle(custom_data_birth_create_);
    custom_data_birth_.set_confirm_handle(custom_data_birth_confirm_);
    custom_data_birth_.set_cancel_handle(custom_data_birth_cancel_);
    custom_data_birth_.show_input(true, ANIMAT_TYPE::EAST, this->getRootContainer());
    custom_data_birth_.setVisible(true);
}

void EditPersonalProfileView::get_sub_title(char* sub_title)
{
	if (sub_title == nullptr)
	{
		assert("[EditPersonalProfileView:8001] sub_title is nullptr");
		return;
	}
	switch (*p_type_->get_val(0))
	{
		case EDIT_PERSONAL_PROFILE_HRM_MAX_:
		case EDIT_PERSONAL_PROFILE_HRM_LACTIC_:
			sprintf(sub_title, "%dbpm", (*p_custom_data_->get_val(0)));
			break;
		case EDIT_PERSONAL_PROFILE_RIDE_FTP_:
			sprintf(sub_title, "%dw", (*p_custom_data_->get_val(0)));
			break;
		default:
			break;
	}
	return;
}

void EditPersonalProfileView::user_info_sub_title(int index, char* sub_title)
{
	int weight_kg = 60;
	if (sub_title == nullptr)
	{
		assert("[EditPersonalProfileView:8001] sub_title is nullptr");
		return;
	}
	switch (index)
	{
		case GENDER:
			if (*p_gender_->get_val(0) == 0)
			{
				sprintf(sub_title, "%s", _TM(g_display_user_info_gender_text[0]));//女
			}
			else
			{
				sprintf(sub_title, "%s", _TM(g_display_user_info_gender_text[1]));//男
			}
			break;
		case BIRTH:
			sprintf(sub_title, "%d/%02d", (*p_year_->get_val(0)), (*p_mon_->get_val(0)));//出生日期
			break;
        case HEIGHT:
        if (get_unit_length() == 0)
        {
                int height = static_cast<int>(*p_height_->get_val(0) * 0.393701 + 0.5f); // 厘米转英寸
                sprintf(sub_title, "%dinch", height);//身高
        }
        else
        {
                sprintf(sub_title, "%dcm", (*p_height_->get_val(0)));//身高
            }
            break;
        case WEIGHT:
        if (get_unit_weight() == 0)
        {
                weight_kg = static_cast<int>(*p_weight_->get_val(0) * 2.20462 + 5); // kg转lb
                sprintf(sub_title, "%dlb", weight_kg / 10);
        }
        else
        {
                weight_kg = *p_weight_->get_val(0);//体重
                sprintf(sub_title, "%dkg", weight_kg / 10);
            }
            break;
		// case HAND_HABIT:
		// 	sprintf(sub_title, "%s", (*p_hand_habit_->get_val(0) == 0) ? _TM(g_display_user_info_hand_text[0]) : _TM(g_display_user_info_hand_text[1]));//0=左手，1=右手
		// 	break;
		default:
			break;
	}
	return;
}

void EditPersonalProfileView::update_list_number_and_sub_title(bool is_auto_detect)
{
	if (!is_auto_detect && QwMenuView::total_ == 1)
	{
		QwMenuView::total_ = QwMenuView::total_ + 1;
		QwMenuView::list_.change_list_item(0, 1);
	}
	else if (is_auto_detect && QwMenuView::total_ == 2)
	{
		QwMenuView::total_ = QwMenuView::total_ - 1;
		QwMenuView::list_.change_list_item(0, -1);
	}
}

void EditPersonalProfileView::set_update_weight(ObserverDrawable<Drawable, uint16_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_weight_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_weight_);
	}
}

void EditPersonalProfileView::update_weight(Drawable* ctrl, Parameters<uint16_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_height(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_height_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_height_);
	}
}

void EditPersonalProfileView::update_height(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_gender(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_gender_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_gender_);
	}
}

void EditPersonalProfileView::update_gender(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_hand_habit(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_hand_habit_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_hand_habit_);
	}
}

void EditPersonalProfileView::update_hand_habit(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_year(ObserverDrawable<Drawable, uint16_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_year_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_year_);
	}
}

void EditPersonalProfileView::update_year(Drawable* ctrl, Parameters<uint16_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_mon(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_mon_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_mon_);
	}
}

void EditPersonalProfileView::update_mon(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

void EditPersonalProfileView::set_update_item_select(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_item_select_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_item_select_);
	}
}

void EditPersonalProfileView::update_item_select(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
	}
}

// custom function
