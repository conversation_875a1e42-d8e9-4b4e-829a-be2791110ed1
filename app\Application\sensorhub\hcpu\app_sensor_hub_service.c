/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   app_sensor_hub_service.c
@Time    :   2025/01/02 21:03:14
*
**************************************************************************/

#include "app_sensor_hub_service.h"
#include "app_sensor_hub.h"
#include "bf0_hal.h"
#include "qw_ramlog_service.h"
#include "service_daily_activity.h"
#include "subscribe_commu.h"
#include <rtthread.h>
#include <string.h>
#include "service_health.h"
#include "easyflash.h"
#include "qwos.h"
#include "pm.h"

int32_t app_send_msg_to_sensor_hub(uint8_t *data, uint16_t len, uint16_t timeout_ms)
{
    int32_t ret;
    sensorhub_msg_head_t msg_header = {0};

    msg_header.data_len = len;
    msg_header.crc = crc_test((uint8_t *) &len, sizeof(uint16_t));

    ret = app_core_bridge_send_cmd((uint8_t *)&msg_header, sizeof(sensorhub_msg_head_t), 0);
    if (ret == 0)
    {
        ret = app_core_bridge_send_cmd(data, len, timeout_ms);
    }

    return ret;
}

int32_t app_event_notify(uint8_t type, uint32_t module_id, uint16_t data_len,
                         const uint8_t *pdata, uint16_t timeout_ms)
{
    int32_t ret;
    uint16_t len;
    app_sensorhub_msg_t p_msg;
    memset((void *) &p_msg, 0, sizeof(app_sensorhub_msg_t));

    p_msg.msg_head.msg_type = type;
    p_msg.module_id = module_id;
    p_msg.msg_head.len = data_len;
    memcpy(p_msg.data, pdata, data_len);
    len = data_len + sizeof(p_msg.msg_head) + sizeof(p_msg.module_id);
    //SENSORHUB_LOG_D("type: %d module id: %d len:%d", type, module_id, len);
    if (data_len != 0) {
        ret = app_send_msg_to_sensor_hub((uint8_t *) &p_msg, len, timeout_ms);
        if (ret != 0) {
            SENSORHUB_LOG_D("app send msg failed %d", ret);
        }
    } else {
        ret = -1;
        SENSORHUB_LOG_D("data_len is 0 !! type: %d module id: %d len:%d", type, module_id, len);
    }

    return ret;
}

static bool sensorhub_test_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data)
    {
        return false;
    }

    //SENSORHUB_LOG_D("HCPU received module id: %d data: %d data_len:%d", module_id, data[0], len);

    return true;
}

static bool sensorhub_test_recv_rsp_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data)
    {
        return false;
    }

    //SENSORHUB_LOG_D("HCPU received module id: %d rsp: %d data_len:%d", module_id, data[0], len);
    //app_core_bridge_cmd_rsp_handler();

    return true;
}

static bool sensorhub_test_rsp_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data)
    {
        return false;
    }
    uint8_t rsp = 0x55;

    //SENSORHUB_LOG_D("HCPU received module id: %d data: %d data_len:%d", module_id, data[0], len);
    //app_event_notify(EVT_APP_SEND_RSP_TEST, 0, sizeof(rsp), &rsp, 0);

    return true;
}

/**
 * @brief 异核订阅事件处理
 *
 * @param module_id 模块id
 * @param data 数据
 * @param len 数据长度
 * @return true 处理完成
 * @return false 处理中有异常
 */
static bool sensorhub_sub_event_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data && len == 0)
    {
        return false;
    }
    (void) sub_commu_rcv_data_from_src_core(module_id, data, len);
    return true;
}

static bool sensorhub_ble_event_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    // if (NULL == data && len == 0) {
    //     return false;
    // }
    SENSORHUB_LOG_D("HCPU received module id: %d data: %d data_len:%d", module_id,
                    data[0], len);
// #if defined(BLE_PERIPHERAL_CHIP) && (BLE_PERIPHERAL_CHIP == BLE_PERIPHERAL_LCPU)
    extern int32_t ble_app_hcpu_exec(uint32_t func_type, uint8_t * data, uint16_t len);
    ble_app_hcpu_exec(module_id, data, len);
// #endif
    return true;
}

static bool lcpu_syslog_requst_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (!data || !len)
        return false;
    qw_syslog_request_t *log_req = (qw_syslog_request_t *) data;
    SCB_InvalidateDCache_by_Addr((void *) log_req->cache_start,
                                 log_req->cache_end - log_req->cache_start);
#ifdef QW_SYSLOG_RAMLOG_IPC_DEBUG
    SENSORHUB_LOG_D("log_req type:%d,addr:[0x%08x],len:%d,", log_req->type,
                    log_req->log_addr, log_req->length);
    SENSORHUB_LOG_D("InvalidateDCache 0x%08x ~ 0x%08x\n", log_req->cache_start,
                    log_req->cache_end);
#endif
    ramlog_flush_request((const char *) log_req->log_addr, log_req->length);
    return true;
}

static bool sensorhub_sleep_period_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    sensorhub_pack_data_t output = {0};
    memcpy(&output, data, sizeof(sensorhub_pack_data_t));
    service_health_sleep_stage_data_update((sensorhub_pack_data_t *)&output);
    return true;
}


static bool sensorhub_kvdb_operation_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    kvdb_operation_t *req = (kvdb_operation_t *)data;
    switch (req->op_type) {
        case LCPU_KVDB_OP_GET:
            {
                char *value = (char *)qwos_malloc(req->value_len + 1); // Allocate memory for the value plus null terminator
                if (value) {
                    ef_get_env_blob(req->key, value, req->value_len, NULL);
                    memcpy(req->data, value, req->value_len);
                    value[req->value_len] = '\0'; // Null terminate the string
                    SENSORHUB_LOG_D("kvdb_get key: %s, value: %s, value_len: %d", req->key, value, req->value_len);
                    qwos_free(value);
                }
            }
            break;
        case LCPU_KVDB_OP_SET:
            {
                ef_set_env_blob(req->key, req->data, req->value_len);
                SENSORHUB_LOG_D("kvdb_set key: %s, value: %s, value_len: %d", req->key, req->data, req->value_len);
            }
            break;
        case LCPU_KVDB_OP_DEL:
            {
                ef_del_env(req->key);
                SENSORHUB_LOG_D("kvdb_del key: %s, value_len: %d", req->key, req->value_len);
            }
            break;
        default:
            break;
    }
    return true;
}

static bool sensorhub_hrv_period_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    sensorhub_pack_data_t output = {0};
    memcpy(&output, data, sizeof(sensorhub_pack_data_t));
    service_health_hrv_pack_data_update((sensorhub_pack_data_t *)&output);

    return true;
}

static bool sensorhub_gps_event_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    static int8_t gps_pm_flag = 0;
    if (NULL == data) {
        return false;
    }

    uint8_t level = data[0];
    if (level == GPIO_SENSORHUB_LEVEL_HIGH && gps_pm_flag == 0) {
        rt_pm_request(PM_SLEEP_MODE_IDLE);
        gps_pm_flag = 1;
    } else if (level == GPIO_SENSORHUB_LEVEL_LOW && gps_pm_flag == 1) {
        rt_pm_release(PM_SLEEP_MODE_IDLE);
        gps_pm_flag = 0;
    }

    return true;
}

const static app_msg_func_t app_msg_cb_array[] = {
    [EVT_SENSORHUB_TEST]                       =        {sensorhub_test_handle},
    [EVT_SENSORHUB_SEND_RSP_TEST]              =        {sensorhub_test_recv_rsp_handle},
    [EVT_SENSORHUB_RSP_TEST]                   =        {sensorhub_test_rsp_handle},
    [EVT_SENSORHUB_SUBSCRIBE_DATA]             =        {sensorhub_sub_event_handle},
    [EVT_SENSORHUB_BLE]                        =        {sensorhub_ble_event_handle},
    [EVT_SENSORHUB_SYSLOG_REQ]                 =        {lcpu_syslog_requst_handle},
    [EVT_SENSORHUB_SLP_PERIOD_DATA]            =        {sensorhub_sleep_period_handle},
    [EVT_SENSORHUB_KVDB_OPERATION]             =        {sensorhub_kvdb_operation_handle},
    [EVT_SENSORHUB_HRV_PERIOD_DATA]            =        {sensorhub_hrv_period_handle},
    [EVT_SENSORHUB_GPS_WAKEUP_INT]             =        {sensorhub_gps_event_handle},

    [EVT_SENSORHUB_END]                        =        {NULL},
};

void app_sensor_hub_msg_received(uint8_t *ptr, uint16_t len)
{
    uint16_t index;
    app_sensorhub_msg_t *pMsg = (app_sensorhub_msg_t *) ptr;
    if (len == 0) {
        SENSORHUB_LOG_E("%s len is 0!! msg type: %d-%d", __FUNCTION__, pMsg->msg_head.msg_type, pMsg->module_id);
        return;
    }
    if (pMsg->msg_head.msg_type >= EVT_APP_END)
    {
        SENSORHUB_LOG_E("msg type error %d", pMsg->msg_head.msg_type);
        return;
    }
    index = pMsg->msg_head.msg_type;
    if (NULL != app_msg_cb_array[index].msg_handle)
    {
        app_msg_cb_array[index].msg_handle(pMsg->module_id, pMsg->data,
                                           pMsg->msg_head.len);
    }
}

uint16_t crc_test(uint8_t const *p_data, uint32_t size)
{
    uint16_t crc = 0;
    while (size--)
    {
        crc = (uint8_t) (crc >> 8) | (crc << 8);
        crc ^= (*p_data++);
        crc ^= (uint8_t) (crc & 0xFF) >> 4;
        crc ^= (crc << 8) << 4;
        crc ^= ((crc & 0xFF) << 4) << 1;
    }
    return crc;
}


#if defined(RT_USING_FINSH)

#include <finsh.h>

void msh_app_send_msg_to_sensorhub(int32_t argc, char **argv)
{
    SENSORHUB_LOG_D("CMD: send_msg_from_app_to_sensorhub!");
    uint8_t i = 0;
    if (argc < 3)
    {
        SENSORHUB_LOG_E("Wrong argument");
    }

    if (0 == strcmp(argv[1], "test"))
    {
        i = (uint8_t) atoi(argv[2]);
        app_event_notify(EVT_APP_TEST, 0, sizeof(i), &i, 0);
    }
    else if (0 == strcmp(argv[1], "test_rsp"))
    {
        i = (uint8_t) atoi(argv[2]);
        app_event_notify(EVT_APP_RSP_TEST, 0, sizeof(i), &i, 500);
    }
}

MSH_CMD_EXPORT_ALIAS(msh_app_send_msg_to_sensorhub, h2l, h2l : hcpu message to lcpu);


#endif
