/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ItemText.cpp
@Time    :   2024/12/10 19:28:15
* 
**************************************************************************/

#include "limits.h"
#include "ItemText.h"

ItemText::ItemText() :
	index_(INT_MAX),
	focus_(false)
{};

void ItemText::setup(void* info)
{
	item_info_t* item_info = static_cast<item_info_t*>(info);
	if (item_info == nullptr)
	{
		assert("[ItemText:8001]ItemText info is nullptr");
		return;
	}

	index_ = item_info->item_index;

	add(text_con_);
	text_con_.setWidthHeight(getWidth(), getHeight());

	text_con_.add(text_title_);
	text_con_.add(text_subtitle_);
	text_title_.setPosition(0, 0, MENU_TITLE_WIDTH, MENU_TITLE_HIGHT);
	text_title_.setTextFont(item_info->title_font);
	text_title_.setTextAlignment(item_info->item_text_info.text_align);
	text_title_.setTextVerticalAlignment(ALIGN_Y_MID);
	text_title_.setLabelAlpha(LV_OPA_TRANSP);
	if (item_info->text_color != 0)
	{
		text_title_.setColor(lv_color_hex(item_info->text_color));
	}
	else
	{
		text_title_.setColor(lv_color_hex(MENU_TITLE_COLOR));
	}
	text_title_.setAlpha(MENU_TITLE_COLOR_ALPHA);
	if (strlen(item_info->title) == NULL && item_info->long_title == NULL)
	{
		text_title_.setHeight(0);
	}
	else
	{
		if(strlen(item_info->title))
		{
			text_title_.setTypedDynamicText(item_info->title);
		}
		else if(item_info->long_title)
		{
			text_title_.setTypedDynamicText(item_info->long_title);
		}
		// text_title_.resizeHeightToCurrentText();
	}

	text_subtitle_.setPosition(0, 0, MENU_TITLE_WIDTH, MENU_SUB_TEXT_HIGHT);
    text_subtitle_.setTextFont(&MENU_SUB_TEXT_FONT);
	text_subtitle_.setTextAlignment(item_info->item_text_info.text_align);
	text_subtitle_.setTextVerticalAlignment(ALIGN_Y_MID);
	text_subtitle_.setLabelAlpha(LV_OPA_TRANSP);
	if (item_info->text_color != 0)
	{
		text_subtitle_.setColor(lv_color_hex(item_info->text_color));
	}
	else
	{
		text_subtitle_.setColor(lv_color_hex(MENU_SUB_TEXT_COLOR));
	}
	text_subtitle_.setAlpha(MENU_SUB_TEXT_COLOR_ALPHA);
	text_subtitle_.setVisible(true);
	if (strlen(item_info->subtitle) == NULL && item_info->long_subtitle == NULL)
	{
		text_subtitle_.setHeight(0);
	}
	else
	{
		if(strlen(item_info->subtitle))
		{
			text_subtitle_.setTypedDynamicText(item_info->subtitle);
		}
		else if(item_info->long_subtitle)
		{
			text_subtitle_.setTypedDynamicText(item_info->long_subtitle);
		}
		// text_subtitle_.resizeHeightToCurrentText();
	}

	text_con_.setWidth(text_title_.getWidth() > text_subtitle_.getWidth() ? text_title_.getWidth() : text_subtitle_.getWidth());
	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight() + (text_subtitle_.getHeight() > 0 ? TITLE_TO_SUBTEXT_GAP : 0));
    align_ = item_info->item_text_info.text_align;
    if (item_info->item_text_info.text_align == 2)
	{
		text_con_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_title_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_subtitle_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_con_.setAlign(ALIGN_IN_CENTER);
	}
	else
	{
		text_con_.setAlign(ALIGN_IN_LM, LEFT_TO_TITLE_GAP);
	}

    text_title_.setAlign(ALIGN_IN_LT);
    text_subtitle_.setAlign(ALIGN_IN_LB);
    text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.resizeHeightToCurrentText();
    text_title_.resizeHeightToCurrentText();


	//gfx_printf("focus_type_:%d\n", index_);
};

void ItemText::quit()
{
    focus_update(false);
	text_con_.removeAll();
	removeAll();
};

void ItemText::focus_update(bool focus)
{
	if (focus_ != focus)
	{
		focus_ = focus;
		if (focus_)
		{
            text_title_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
            text_subtitle_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
		}
		else
		{
            text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
            text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		}
        text_title_.resizeHeightToCurrentText();
        text_subtitle_.resizeHeightToCurrentText();
	}
};


void* ItemText::get_user_data()
{
	return (void*)index_;
}

int ItemText::set_type()
{
    int ret = 0;
    if (align_ == 2)
    {
	    ret = (int)CTRL_TYPE::TYPE_ONWER_DRAW;
    }
    else
    {
	    ret = (int)CTRL_TYPE::TYPE_ENTER_ICON;
    }
    return ret;
}

void ItemText::update_sub_title(void* info)
{
	if (info == nullptr)
	{
		return;
	}

	char* text = static_cast<char*>(info);
	if(strlen(text) == NULL)
	{
		text_subtitle_.setTypedDynamicText("");
		text_subtitle_.invalidate();
		text_subtitle_.setHeight(0);
	}
	else
	{
		text_subtitle_.setTypedDynamicText(text);
		text_subtitle_.invalidate();
		text_subtitle_.resizeHeightToCurrentText();
	}

	text_con_.setWidth(text_title_.getWidth() > text_subtitle_.getWidth() ? text_title_.getWidth() : text_subtitle_.getWidth());
	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight() + (text_subtitle_.getHeight() > 0 ? TITLE_TO_SUBTEXT_GAP : 0));
	if (align_ == 2)
	{
		text_con_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_title_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_subtitle_.setWidth(MENU_TITLE_CENTER_WIDTH);
		text_con_.setAlign(ALIGN_IN_CENTER);	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight());
	}
	else
	{
		text_con_.setAlign(ALIGN_IN_LM, LEFT_TO_TITLE_GAP);
	}
	text_title_.setAlign(ALIGN_IN_LT);
    text_subtitle_.setAlign(ALIGN_IN_LB);

    if (focus_)
    {
        text_title_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
        text_subtitle_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
    }
    else
    {
        text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    }
    text_title_.resizeHeightToCurrentText();
    text_subtitle_.resizeHeightToCurrentText();
}






