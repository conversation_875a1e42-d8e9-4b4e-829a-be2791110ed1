./build/WR02_APP/.obj/__/__/Application/App/touchx_gui/touchx_embed/UserButtonController.o: \
  ..\..\Application\App\touchx_gui\touchx_embed\UserButtonController.cpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\hal\Types.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_color.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\lv_conf_internal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\lv_conf_kconfig.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\lv_conf.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_assert.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_log.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_types.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_mem.h \
  ..\..\..\sifli\rtos\rtthread\include\rtthread.h rtconfig.h \
  ..\wr02_board_config.h ..\..\..\sifli\rtos\rtthread\include\rtdebug.h \
  ..\..\..\sifli\rtos\rtthread\include\rtdef.h \
  ..\..\..\sifli\rtos\rtthread\include\rtlibc.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_stat.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_errno.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_fcntl.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_ioctl.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_dirent.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_signal.h \
  ..\..\..\sifli\rtos\rtthread\include\libc\libc_fdset.h \
  ..\..\..\sifli\rtos\rtthread\include\rtservice.h \
  ..\..\..\sifli\rtos\rtthread\include\rtm.h \
  ..\..\..\sifli\rtos\rtthread\components\finsh\finsh_api.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_math.h \
  ..\..\..\qw_platform\qwos\module\fit\inc_fit_codec\stdbool.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\hal\HAL.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\hal\Gestures.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\events\ClickEvent.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Event.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\events\DragEvent.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\events\GestureEvent.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\events\ScaleRotateEvent.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\UIEventListener.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Drawable.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Application.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\Transition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Callback.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Bitmap.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\drawengine_api.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\..\..\lv_engine\extra\gx_utility.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\..\..\lv_engine\extra\gx_api.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\..\..\lv_engine\lvgl.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_log.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_math.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_mem.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_printf.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_tlsf.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_canvas.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\lv_conf_internal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_style.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\font\lv_font.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\font\..\lv_conf_internal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\font\lv_symbol_def.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\font\..\misc\lv_area.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_area.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_txt.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_printf.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_bidi.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_txt.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_img_decoder.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_img_buf.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_color.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_area.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_fs.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_types.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_img_cache.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_rect.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\sw\lv_draw_sw_gradient.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\sw\..\..\misc\lv_color.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\sw\..\..\misc\lv_style.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\sw\lv_draw_sw_dither.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\sw\..\..\lv_conf_internal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_label.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_bidi.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_img.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_line.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_triangle.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_arc.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_mask.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\..\misc\lv_math.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_transform.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\..\draw\lv_draw_layer.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_label.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_qr_consts.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\misc\lv_qr_encode.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\lv_hal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\lv_hal_disp.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\..\draw\lv_draw.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\..\misc\lv_color.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\..\misc\lv_area.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\..\misc\lv_ll.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\lv_hal_tick.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\hal\..\lv_conf_internal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\core\lv_refr.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\core\..\hal\lv_hal.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\core\lv_disp.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\font\lv_font.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\font\lv_font_loader.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\font\lv_font_fmt_txt.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\src\draw\lv_draw.h \
  ..\..\..\qw_platform\qwos\module\touchx_external\media\libjpeg_app.h \
  ..\..\..\qw_platform\qwos\module\touchx_external\media\video_avi_player.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\gfxprintf.hpp \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\GUICtrl\QwUtility\QwCtrlBase.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\platform\driver\button\ButtonController.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\platform\driver\touch\TouchController.hpp \
  ..\..\Application\App\touchx_gui\touchx_embed\UserButtonController.hpp \
  ..\..\..\qw_platform\qwos_app\GUI\QwGUIKey.h \
  ..\..\Application\App\key_module\key_module.h \
  ..\..\Application\App\key_module\multi_button.h \
  ..\..\Application\App\key_module\button_config.h \
  ..\..\..\qw_platform\qwos_app\log\qw_log.h \
  ..\..\..\sifli\rtos\rtthread\components\utilities\ulog\ulog.h \
  ..\..\..\sifli\rtos\rtthread\components\utilities\ulog\ulog_def.h \
  ..\..\..\qw_platform\qw_platform.h \
  ..\..\..\qw_platform\qwos\inc\qwos.h \
  ..\..\Application\Drivers\sw_version\version_check_api.h \
  ..\..\..\sifli\rtos\rtthread\components\utilities\ulog\ulog_def.h \
  ..\..\Application\App\basic_app_module\backlight_module\backlight_module.h \
  ..\..\Application\App\basic_app_module\battery_srv_module\battery_srv.h \
  ..\..\Application\App\basic_app_module\global_button_srv\global_button.h \
  ..\..\..\qw_platform\qwos_app\GUI\QwGUIKey.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\GUI\PageBase.h \
  ..\..\..\qw_platform\qwos_app\GUI\..\..\qwos_app\GUI\PageManager\PageManager.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos\module\gui\GUICtrl\QwUtility\PM_List.hpp \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\GUI\PageManager\PageManagerDef.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\GUI\PageTransition.h \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\GUICtrl\touchgfx.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Utils.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\EasingEquations.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Color.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\Screen.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\Container.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\JSMOCHelper.hpp \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\GUICtrl\QwUtility\QwLayerScreen.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\lv_draw_engine.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\ListLayout.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\SwipeContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Image.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Widget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\TiledImage.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\Slider.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\SlideMenu.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\MoveAnimator.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\AbstractButton.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Button.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\ScrollableContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Box.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\FlexScrollContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\MsgBoxContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\ModalWindow.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\CacheableContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\DrawableList.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\ScrollBase.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\ScrollList.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\ScrollWheelBase.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\ScrollWheel.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\scrollers\ScrollWheelWithSelectionStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\Buttons.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\AnimatedImageButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\AbstractButtonContainer.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\AnimatedImage.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\BoxWithBorderButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\BoxWithBorder.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\ClickButtonTrigger.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\IconButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\ImageButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\RepeatButtonTrigger.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\TextButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\TextArea.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\TypedText.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\TiledImageButtonStyle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\ToggleButtonTrigger.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\buttons\TouchButtonTrigger.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\BoxProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\AbstractDirectionProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\AbstractProgressIndicator.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\BarProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Bar.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\CircleProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\canvas\Circle.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\canvas\CanvasWidget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\canvas\CWRUtil.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\ImageProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\containers\progress_indicators\LineProgress.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\canvas\Line.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\ClickListener.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\DragListener.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\Draggable.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\DrawListener.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\FadeAnimator.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\SliderAnimator.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\SnapshotWidget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\Snapper.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\mixins\GeneralAnimator.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\NoTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\BlockTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\CoverTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\SlideTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\WipeTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\ExpandTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\ZoomTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\FadeTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\transitions\RollbackTransition.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\AnimationTextureMapper.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\TextureMapper.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\ButtonWithLabel.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\ButtonWithIcon.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\CircleKnob.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Gauge.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\Meter.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\PieChart.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\PixelDataWidget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\QRCodeWidget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\RadioButton.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\RadioButtonGroup.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\RepeatButton.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\ScalableImage.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\ToggleButton.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\TouchArea.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\GraphWrapAndClear.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\AbstractDataGraph.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\hal\Config.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\GraphWrapAndOverwrite.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\GraphElements.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\GraphLabels.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\graph\GraphScroll.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\RlottieImage.hpp \
  ..\..\..\qw_platform\qwos\module\touchx_external\rlottie\inc\rlottie_capi.h \
  ..\..\..\qw_platform\qwos\module\touchx_external\rlottie\inc\rlottiecommon.h \
  ..\..\..\qw_platform\qwos\module\touchx\touchgfx\include\touchgfx\widgets\VideoWidget.hpp \
  ..\..\..\qw_platform\qwos\inc\..\module\touchx_external\QwCanvasWidget.hpp \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\..\..\touchx_external\canvas\lv_gui_map.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\lvgl.h \
  ..\..\..\qw_platform\qwos\module\touchx\lv_engine\..\..\touchx_external\canvas\qw_gui.h \
  ..\..\..\qw_platform\qwos_app\GUI\..\..\qwos_app\GUI\PageManager\PageManagerDef.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\GUI\PageManager\PageCommand.h \
  ..\..\..\qw_platform\qwos_app\GUI\..\..\qwos_app\GUI\PageManager\PageType.h \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\GUICtrl\QwUtility\QwCtrlEvent.h \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\CtrlBase\Notification.hpp \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\CtrlBase\Parameters.hpp \
  ..\..\..\qw_platform\qwos\inc\..\module\gui\CtrlBase\ObserverDrawable.hpp \
  ..\..\Application\App\UI\ViewData\view_page_model.h \
  ..\..\Application\Lib_New\data_utility\inc\qw_general.h \
  ..\..\Application\App\common\basictype.h \
  ..\..\Application\Mem_Manager\memory_manager.h \
  ..\..\..\sifli\external\CMSIS\Include\cmsis_armclang.h \
  ..\..\..\qw_platform\qwos\inc\thread_pool.h \
  ..\..\..\qw_platform\qwos\inc\cmsis_os2.h \
  ..\..\Application\Lib_New\settings\cfg_header_def.h \
  ..\..\Application\Lib_New\settings\cfg_factory_reset.h \
  ..\..\Application\Lib_New\settings\qw_dev_cfg.h \
  ..\..\..\qw_platform\qwos\inc\qw_sensor_common.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ble_gap.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\nrf_svc.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\nrf_error.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ble_hci.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ble_ranges.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ble_types.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ble_err.h \
  ..\..\..\qw_platform\qwos\inc\qw_sensor_config.h \
  ..\..\..\qw_platform\qwos\inc\qw_sensor_internal.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\ant_bpwr.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\ant_parameters.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\common\nrf_sdh_ant.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\util\app_util.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\modules\compiler_abstraction.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\util\nordic_common.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\modules\nrf.h \
  ..\..\..\sifli\drivers\Include\bf0_hal.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_conf_hcpu.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_rcc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_def.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\register.h \
  ..\..\..\sifli\external\CMSIS\Include\core_cm33.h \
  ..\..\..\sifli\drivers\cmsis\Include\system_bf0_ap.h \
  ..\..\..\sifli\external\CMSIS\Include\core_mstar.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\mem_map.h \
  linker_scripts\custom_mem_map.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\ble_mac.h \
  ..\..\..\sifli\drivers\cmsis\Include\ble_phy.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\ble_rf_dig.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\hpsys_rcc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lpsys_rcc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\dmac.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\extdma.h \
  ..\..\..\sifli\drivers\cmsis\Include\usart.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\epic.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\spi.h \
  ..\..\..\sifli\drivers\cmsis\Include\gpt.h \
  ..\..\..\sifli\drivers\cmsis\Include\btim.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\mailbox.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\rtc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\psramc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\qspi.h \
  ..\..\..\sifli\drivers\cmsis\Include\nn_acc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\dsi_host.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\dsi_phy.h \
  ..\..\..\sifli\drivers\cmsis\Include\lptim.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\i2c.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\hpsys_cfg.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lpsys_cfg.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\efusec.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\i2s.h \
  ..\..\..\sifli\drivers\cmsis\Include\crc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lcd_if.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\sdmmc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\aes_acc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\gpio1.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\gpio2.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\hpsys_pinmux.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lpsys_pinmux.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\hpsys_aon.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lpsys_aon.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\pmuc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\gpadc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\sdadc.h \
  ..\..\..\sifli\drivers\cmsis\Include\tsen.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\trng.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\ptc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\ezip.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\patch.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\wdt.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\pdm.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\busmon.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\lpcomp.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\usbc_x.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\cache.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_gpio.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_dma.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_ext_dma.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_cortex.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_adc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\gpadc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_sdadc.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\sdadc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_lpcomp.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_crc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_qspi_ex.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_qspi.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\qspi.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_i2c.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_i2s.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_pcd.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_rtc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_rtc_ex.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_spi.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_tim.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_tim_ex.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_uart.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_wdt.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_ezip.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_epic.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_nn_acc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_lcdc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_dsi.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_sdhci.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_aes.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_efuse.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_mailbox.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_pinmux.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\bf0_pin_const.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_pmu.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_aon.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_rng.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_psram.h \
  ..\..\..\sifli\drivers\cmsis\sf32lb55x\psramc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_cache.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_busmon.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_ptc.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_pdm.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_patch.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_tsen.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_lcpu_config.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_lrc_cal.h \
  ..\..\..\sifli\drivers\Include\bf0_sys_cfg.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_hcd.h \
  ..\..\..\sifli\drivers\Include\bf0_hal_hlp.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\modules\compiler_abstraction.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\experimental_section_vars\nrf_section_iter.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\experimental_section_vars\nrf_section.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\util\nordic_common.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\util\nrf_assert.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\libraries\util\sdk_errors.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\softdevice\s332\headers\nrf_error.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_channel_config\ant_channel_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\sdk_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\ble_services_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\ant_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\igs_sensor_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\sdh_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\nrf_lib_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\config\sdh_prio_config.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_1.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_2.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_16.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_17.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_torque.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_18.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_page_19.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\pages\ant_bpwr_common_data.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_common\pages\ant_common_page_80.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_common\pages\ant_common_page_81.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_common\pages\ant_common_page_82.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\ant_bpwr_local.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_bpwr\ant_bpwr.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_fe\ant_fe.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_fe\ant_fe_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_di2\ant_di2.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_di2\pages\ant_di2_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_di2\pages\ant_di2_page_1.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_shft\ant_shft.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_shft\pages\ant_shft_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_shft\pages\ant_shft_page_1.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\ant_radar.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\pages\ant_radar_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\pages\ant_radar_page_device.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\pages\ant_radar_page_data.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_common\pages\ant_common_page_70.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\ant_radar_local.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_radar\ant_radar.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_light\ant_light.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_light\ant_light_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\ant_lev.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_1.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_2.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_34.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_3.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_4.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_5.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_lev\pages\ant_lev_page_16.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_common\ant_request_controller\ant_request_controller.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_rd\ant_rd.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_rd\pages\ant_rd_pages.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_rd\pages\ant_rd_page_0.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_rd\pages\ant_rd_page_1.h \
  ..\..\..\sifli\external\nRF5_SDK_16.0.0\components\ant\ant_profiles\ant_rd\pages\ant_rd_page_16.h \
  ..\..\Application\App\common\igs_dev_config.h \
  ..\..\Application\App\common\qw_macro_config.h \
  ..\..\Application\App\common\basictype.h \
  ..\..\Application\Lib_New\data_utility\inc\max_config.h \
  ..\..\..\qw_platform\qwos\inc\qw_gps.h \
  ..\..\..\qw_platform\qwos\inc\..\module\gps\gps_convert.h \
  ..\..\..\qw_platform\qwos\module\gps\minmea.h \
  ..\..\..\qw_platform\qwos\inc\qw_user_debug.h \
  ..\..\Application\Lib_New\settings\cfg_reset.h \
  ..\..\Application\Lib_New\settings\cfg_dev_info.h \
  ..\..\Application\Lib_New\settings\cfg_firmware_update.h \
  ..\..\Application\Lib_New\settings\cfg_ble.h \
  ..\..\Application\Lib_New\settings\cfg_time.h \
  ..\..\Application\service\datetime\service_datetime.h \
  ..\..\..\qw_platform\qwos\inc\qw_time_service.h \
  ..\..\..\qw_platform\qwos\inc\qw_time_api.h \
  ..\..\Application\Lib_New\settings\cfg_gps.h \
  ..\..\Application\Drivers\driver_api\gps_api.h \
  ..\..\Application\Lib_New\settings\cfg_custom.h \
  ..\..\Application\Lib_New\settings\cfg_focus.h \
  ..\..\Application\Lib_New\settings\cfg_power_save.h \
  ..\..\Application\Lib_New\settings\cfg_show_set.h \
  ..\..\Application\Lib_New\settings\cfg_alarm_clock.h \
  ..\..\Application\App\basic_app_module\alarm_clock_app\alarm_manager.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\alarm.h \
  ..\..\Application\Lib_New\settings\cfg_achievements.h \
  ..\..\..\qw_platform\qwos\inc\qw_data_type.h \
  ..\..\..\qw_platform\qwos\inc\..\module\sports_data\sports_data_type.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\sports_data\sports_data_show.h \
  ..\..\..\qw_platform\qwos_app\sports_data\..\..\qwos_app\sports_data\sports_data_get_string.h \
  ..\..\..\qw_platform\qwos\inc\..\module\sports_data\sports_data_type.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\sports_data\sports_data_chart.h \
  ..\..\..\qw_platform\qwos\inc\..\..\qwos_app\sports_data\sports_altitude_graph.h \
  ..\..\Application\Lib_New\settings\cfg_workout.h \
  ..\..\Application\Lib_New\settings\cfg_navigation.h \
  ..\..\Application\Lib_New\settings\cfg_access_set.h \
  ..\..\Application\Lib_New\settings\cfg_general_set.h \
  ..\..\Application\Lib_New\settings\cfg_lock.h \
  ..\..\Application\Lib_New\settings\cfg_unit.h \
  ..\..\Application\Lib_New\settings\cfg_mag_calib.h \
  ..\..\Application\Lib_New\settings\cfg_sound.h \
  ..\..\Application\Lib_New\settings\cfg_timer_clock.h \
  ..\..\Application\Lib_New\settings\cfg_vibration.h \
  ..\..\Application\Lib_New\settings\cfg_sport_set.h \
  ..\..\Application\Lib_New\settings\cfg_user_info.h \
  ..\..\Application\Lib_New\settings\cfg_ant_ble_dev.h \
  ..\..\Application\Lib_New\settings\cfg_sportspage.h \
  ..\..\Application\Lib_New\settings\cfg_tool_metronome.h \
  ..\..\..\qw_platform\qwos_app\GUI\PageManager\PageCommand.h \
  ..\..\Application\algo_service\algo_service_component\algo_service_sports\algo_service_sport_status.h \
  ..\..\Application\subscribe_service\subscribe_data_protocol.h \
  ..\..\Application\Lib_3rd\GoMore\SDK\GoMoreLibStruct.h \
  ..\..\Application\algo_service\algo_service_component\algo_service_gps\algo_service_gps_data_type.h \
  ..\..\Application\Lib_3rd\GoMore\lib_gm_common.h \
  ..\..\Application\algo_service\algo_service_component\algo_service_component_common.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\rtdevice.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\ringbuffer.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\completion.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\dataqueue.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\workqueue.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\waitqueue.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\pipe.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\poll.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\ipc\ringblk_buf.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\rtc.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\alarm.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\spi.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\serial.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\i2c.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\i2c_dev.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\mmcsd_core.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\mmcsd_host.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\mmcsd_card.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\mmcsd_cmd.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\sd.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\sdio.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\sdio_func_ids.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\watchdog.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\pin.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\hwtimer.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\adc.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\rt_drv_pwm.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\pm.h \
  ..\..\..\sifli\customer\boards\watch_wr02\board.h \
  ..\..\..\sifli\customer\boards\watch_wr02\bsp_board.h \
  ..\..\..\sifli\rtos\rtthread\bsp\sifli\drivers\drv_common.h \
  ..\..\..\sifli\rtos\rtthread\include\rthw.h \
  ..\..\..\sifli\rtos\rtthread\components\libc\compilers\armlibc\sys\time.h \
  ..\..\..\sifli\rtos\rtthread\components\libc\compilers\armlibc\sys\types.h \
  ..\..\..\sifli\rtos\rtthread\bsp\sifli\drivers\drv_flash.h \
  ..\..\Application\App\app_task.h ..\..\Application\App\system_utils.h \
  ..\..\..\qw_platform\qwos\inc\message_service.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\msg_services\msg_service_config.h \
  ..\..\..\sifli\external\CMSIS\RTOS2\Include\cmsis_os2.h \
  ..\..\..\qw_platform\qwos\inc\data_cache.h \
  ..\..\..\qw_platform\qwos\inc\.\qwos.h \
  ..\..\..\qw_platform\qwos\inc\.\qw_list.h \
  ..\..\..\qw_platform\qwos\inc\.\qw_mlist.h \
  ..\..\..\qw_platform\qwos\inc\data_cache.h \
  ..\..\..\qw_platform\qwos\inc\qw_list.h \
  ..\..\..\qw_platform\qwos\inc\..\middleware\data_cache\data_queue.h \
  ..\..\..\qw_platform\qwos\inc\..\middleware\data_cache\lwrb.h \
  ..\..\..\qw_platform\qwos\inc\..\middleware\data_cache\qw_hash.h \
  ..\..\..\qw_platform\qwos\inc\..\middleware\data_cache\qw_lrucache.h \
  ..\..\..\sifli\rtos\os_adaptor\inc\os_adaptor_rtthread.h \
  ..\..\..\sifli\middleware\include\sf_type.h \
  ..\..\..\sifli\rtos\os_adaptor\src\os_adaptor_rtthread_internal.h \
  ..\..\..\sifli\rtos\os_adaptor\inc\os_adaptor.h \
  ..\..\..\sifli\rtos\os_adaptor\inc\os_adaptor_rtthread.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\msg_services\message_manager.h \
  ..\..\..\qw_platform\qwos\framework\msg_services\msg_service_config.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\msg_services\data_reuse.h \
  ..\..\..\qw_platform\qwos\inc\pm_manager.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\pm_manager\pm_notify_app.h \
  ..\..\..\qw_platform\qwos\inc\little_notify_list.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\pm_manager\pm_process.h \
  ..\..\..\qw_platform\qwos\framework\pm_manager\pm_notify_app.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\pm_manager\pm_thread_sync.h \
  ..\..\..\qw_platform\qwos\inc\..\framework\pm_manager\power_domain.h \
  ..\..\..\qw_platform\qwos\inc\qw_section.h \
  ..\..\..\sifli\rtos\rtthread\components\dfs\include\dfs_fs.h \
  ..\..\..\sifli\rtos\rtthread\components\dfs\include\dfs.h \
  ..\..\..\sifli\rtos\rtthread\components\drivers\include\drivers\mmcsd_core.h \
  ..\..\..\sifli\rtos\rtthread\components\dfs\include\dfs_posix.h \
  ..\..\..\sifli\rtos\rtthread\components\dfs\include\dfs_file.h \
  ..\..\..\sifli\drivers\Multicore_Drivers\System\multicore_time.h \
  ..\..\..\sifli\drivers\Multicore_Drivers\DataServer\multicore_dataserver.h \
  ..\..\Application\App\radio\application\ble_test_module\button_debug_cmd.h \
  ..\..\Application\App\touchx_gui\touchx_embed\key_queue.h
