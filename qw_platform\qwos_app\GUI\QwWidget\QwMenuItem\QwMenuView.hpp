/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwMenuView.hpp
@Time    :   2024/12/13 09:47:32
*
**************************************************************************/

#pragma once
#include "qw_os_gui.h"
#include "../../qwos_app/GUI/PageBase.h"
#include "QwMenuItem/QwMenuTypes.h"
#include "QwNavigationBar/QwNavigationBar.h"
#include "Image/images.h"
#include "QwGUITheme.h"

constexpr auto APP_ITEM_WIDTH = MENU_CARD_WIDTH;
constexpr auto APP_ITEM_HEIGHT = MENU_CARD_HEIGHT;
constexpr auto APP_ITEM_GAP = MENU_CARD_GAP;
constexpr auto APP_ITEM_RADIUS = MENU_CARD_RADIUS;
constexpr auto APP_MENU__MAX = 0xFF;

typedef enum
{
    MENU_ITEM_TYPE_NORMAL = 0,
    MENU_ITEM_TYPE_APP,
    MENU_ITEM_TYPE_MAX,
}MENU_ITEM_TYPE;

template <typename ITEM>
class QwMenuView : public PageView
{
protected:
	int total_;
	int menu_height_; //菜单高度

	Box bg_;
	QwMenuFocus<ITEM> list_;
	QwNavigationBar nav_;
	// QwTopStatus top_;

	Callback<QwMenuView, ITEM*, int16_t, bool> create_list_item_;
	Callback<QwMenuView, ITEM*, int16_t, bool> destory_list_item_;
	Callback<QwMenuView, int16_t, Rect&> list_item_rect_;
	Callback<QwMenuView, int16_t, bool> list_item_select_;
    Callback<QwMenuView, ITEM*, int16_t, CTRL_TYPE&> list_item_type_;

	void clear_all()
	{
		getRootContainer().removeAll();
		getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	}

public:
	QwMenuView(PageManager* manager) :
		total_(0),
		menu_height_(MENU_CARD_HEIGHT),
		PageView(manager),
		create_list_item_(this, &QwMenuView::create_list_item_func),
		destory_list_item_(this, &QwMenuView::destory_list_item_func),
		list_item_rect_(this, &QwMenuView::list_item_rect_func),
		list_item_select_(this, &QwMenuView::list_item_select_func),
		list_item_type_(this, &QwMenuView::list_item_type_func)
	{
		nav_.set_reset();
		nav_.setup();
		nav_.set_type(QWNAVIBAR_TYPE::QNB_TOP_TITLE);
		nav_.get_top_status()->set_show_type(TOP_STATUS_TYPE_DEFAULT);
		nav_.get_top_status()->set_time_style(get_time_style());
	}

	virtual ~QwMenuView() {}

	// virtual void set_navi_visible(bool state, int type = TOP_STATUS_TYPE_DEFAULT, void* info = nullptr)
	// {
	// 	if(type == TOP_STATUS_TYPE_IMAGE && info == nullptr)
	// 	{
	// 		assert(false && "[QwMenuView]:set_top_Visible type is TOP_STATUS_TYPE_IMAGE and info is null");
	// 	}
	// 	else if(type == TOP_STATUS_TYPE_IMAGE && info != nullptr)
	// 	{
	// 		top_.set_icon_image(info);
	// 	}

	// 	top_.setVisible(state);
	// 	top_.set_show_type(type);
	// }

	void set_menu_type(MENU_ITEM_TYPE type)
	{
		if(type == MENU_ITEM_TYPE_NORMAL)
		{
			menu_height_ = MENU_CARD_HEIGHT;
		}
		else
		{
			menu_height_ = APP_CARD_HEIGHT;
		}
	}

	virtual void show_menu(int items_num, int foucus_index, const char *title, WideTextAction title_action = WIDE_TEXT_WORDWRAP_DOT)
	{
		clear_all();

		add(bg_);
		add(list_);

		bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
		bg_.setColor(lv_color_black());

    	Bitmap bitmap1 = Bitmap((const void*)app_select_item_img_bg_dir);
		Bitmap bitmap2 = Bitmap(&label_img_enter);

		QwMenuFocus_Func<ITEM> cb_t;
		cb_t.create = &create_list_item_;
		cb_t.destory = &destory_list_item_;
		cb_t.get_rect = &list_item_rect_;
		cb_t.select_item = &list_item_select_;
		cb_t.sub_items_type = &list_item_type_;

		total_ = items_num;
		list_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
		list_.set_radius(24);
		list_.set_focus_item_enable(false);
		list_.set_focus_bar_enable(true);
		list_.set_list_loop_enable(false);
		list_.set_focus_color(0x00000000);
		list_.set_scroll_radius(0);
		list_.set_focus_bar_image(bitmap1);
		list_.set_center_image(bitmap2);
        list_.set_scroll_bar_enable(true);

		if(nav_.isVisible())
		{
			if(nav_.get_type() == QWNAVIBAR_TYPE::QNB_TOP_TITLE)
			{
				nav_.set_title_typed_dynamic_text(title);
                nav_.set_tittle_show_way(title_action);
			}
			nav_.on_notify();
			list_.set_menu_navi_bar(&nav_);
		}

		list_.setup(items_num, foucus_index < items_num ? foucus_index : (items_num - 1), title, &cb_t);

	}

	virtual int get_select_app()
	{
		ITEM* p_focus_item = list_.get_focus_item();

		if (p_focus_item != nullptr)
		{
			return (int)p_focus_item->get_user_data();
		}
		else
		{
			return 0xFF;
		}
	}

	virtual void item_update_show(int index, ITEM_UPDATE_TYPES update_type, void* info)
	{
		ITEM* item = nullptr;
		item = list_.get_list_item(index);
		if (item)
		{
			item->update_show(update_type, info);
		}
		item = list_.get_list_item_from_focus(index);
		if (item)
		{
			item->update_show(update_type, info);
		}
	}

	virtual void set_item_info(item_info_t* info, int index) = 0;
    virtual void set_item_notify(ITEM* context, int index) = 0;
    virtual void create_list_item_func(ITEM* context, int16_t index, bool focus)
	{
		item_info_t item_info = { ITEM_TYPES::ITEM_TEXT, index};
		if (index < total_)
		{
			set_item_info(&item_info, index);
			context->setWidthHeight(APP_ITEM_WIDTH, menu_height_);
			context->setAlign(ALIGN_IN_TM, 0, (menu_height_ + APP_ITEM_GAP) * index);
			//context->setAlign(ALIGN_IN_LM, (APP_ITEM_WIDTH + APP_ITEM_GAP) * index, 0);
			context->setup((void*)&item_info);
			// context->focus(focus);
            set_item_notify(context, index);
		}
	}
	virtual void destory_list_item_func(ITEM* context, int16_t index, bool focus)
	{
		context->quit();
	}
	virtual void list_item_rect_func(int16_t index, Rect& rc_out)
	{
		rc_out = Rect((HAL::DISPLAY_WIDTH - APP_ITEM_WIDTH) / 2,
			(menu_height_ + APP_ITEM_GAP) * index, APP_ITEM_WIDTH, menu_height_);
		/*rc_out = Rect((APP_ITEM_WIDTH + APP_ITEM_GAP) * index, (HAL::DISPLAY_HEIGHT - menu_height_) / 2,
			APP_ITEM_WIDTH, menu_height_);*/
	}
	virtual void list_item_select_func(int16_t index, bool focus)
	{
		ITEM* item = nullptr;
		item = list_.get_list_item(index);
        if (item)
        {
		    item->focus(focus);
        }
        item = list_.get_list_item_from_focus(index);
		if (item)
		{
			item->focus(focus);
		}
	}
	virtual void list_item_type_func(ITEM* context, int16_t index, CTRL_TYPE& type)
	{
        type = (CTRL_TYPE) context->set_type();
	}

};
