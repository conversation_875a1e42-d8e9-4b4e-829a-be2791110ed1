/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   PersonalProfileView.cpp
@Time    :   2024/12/25 10:55:34
* 
**************************************************************************/		
#include "PersonalProfileView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"

typedef enum
{
	HRM_MAX_,//跑步最大心率			
	HRM_LACTIC_,//跑步乳酸阈值
	RIDE_FTP_,//骑行FTP
	USER_INFO_,//用户信息
	EDIT_PERSONAL_PROFILE_MAX_,
}EDIT_PERSONAL_PROFILE_ITEM;

static const char* g_display_edit_personal_profile_text[] = {
    "_running_maximum_heart_rate",//跑步最大心率	
    "_running_lactate_threshold_heart_rate",//跑步乳酸阈值
    "_cycling_ftp",//骑行FTP
    "_user_information",//用户信息
};
TM_DECLARE(g_display_edit_personal_profile_text)

static const char g_edit_personal_profile_menu_title[] = "_personal_information";
TM_KEY(g_edit_personal_profile_menu_title)

static const char g_display_user_info_auto_detection_text[] = "_auto_detection_1";
TM_KEY(g_display_user_info_auto_detection_text)

const qw_menu_info g_edit_personal_profile_menu_info[] = {
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_edit_personal_profile_text[HRM_MAX_]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_edit_personal_profile_text[HRM_LACTIC_]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_edit_personal_profile_text[RIDE_FTP_]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_display_edit_personal_profile_text[USER_INFO_]},
};

PersonalProfileView::PersonalProfileView(PageManager* manager) :
	QwMenuView(manager),
	p_hrm_max_(nullptr),p_hrm_lactic_(nullptr),p_ftp_ride_(nullptr),
	p_item_select_(nullptr),p_set_item_select_(nullptr),
	on_set_item_select_(this, &PersonalProfileView::on_click_set_item_select),
	update_hrm_max_(this, &PersonalProfileView::update_hrm_max),
	update_hrm_lactic_(this, &PersonalProfileView::update_hrm_lactic),
	update_ftp_ride_(this, &PersonalProfileView::update_ftp_ride),
	select_item_pos_(this, &PersonalProfileView::select_item_pos),
	update_item_select_(this, &PersonalProfileView::update_item_select)
{

}

PersonalProfileView::~PersonalProfileView()
{

}

void PersonalProfileView::setup()
{
	uint8_t focus_index = *p_item_select_->get_val(0);
	QwMenuView::show_menu(EDIT_PERSONAL_PROFILE_MAX_, focus_index, _TM(g_edit_personal_profile_menu_title));
}

void PersonalProfileView::quit()
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void PersonalProfileView::jump(const char* page_name)
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void PersonalProfileView::handleTickEvent()
{
	QwMenuView::list_.handleTickEvent();
}

void PersonalProfileView::handleKeyEvent(uint8_t c)
{
    QwMenuView::list_.handleKeyEvent(c);
	if (c == KEY_CLK_START)
	{
		int select_index = QwMenuView::list_.get_focus_item_index();
		switch_to_app(select_index, 0, 0);
	}
	else if (c == KEY_CLK_BACK)
	{
        manager_->push("SystemSettingsMenu");
	}
}

void PersonalProfileView::handleClickEvent(const ClickEvent& evt)
{
	QwMenuView::list_.handleClickEvent(evt);
}

void PersonalProfileView::handleDragEvent(const DragEvent& evt)
{
	QwMenuView::list_.handleDragEvent(evt);
}

void PersonalProfileView::handleGestureEvent(const GestureEvent& evt)
{
	QwMenuView::list_.handleGestureEvent(evt);

	if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL
        && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
    {
        manager_->push("SystemSettingsMenu");
    }
}

// Notification Callback function
void PersonalProfileView::set_on_set_item_select(Notification<uint8_t>* command)
{
	p_set_item_select_ = command;
}

void PersonalProfileView::on_click_set_item_select(uint8_t t1)
{
	if (p_set_item_select_ != nullptr)
	{
		p_set_item_select_->notify(t1);
	}
}

// ObserverDrawable Callback function
void PersonalProfileView::set_update_hrm_max(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_hrm_max_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_hrm_max_);
	}
}

void PersonalProfileView::update_hrm_max(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void PersonalProfileView::set_update_hrm_lactic(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_hrm_lactic_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_hrm_lactic_);
	}
}

void PersonalProfileView::update_hrm_lactic(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void PersonalProfileView::set_update_ftp_ride(ObserverDrawable<Drawable, uint16_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_ftp_ride_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_ftp_ride_);
	}
}

void PersonalProfileView::update_ftp_ride(Drawable* ctrl, Parameters<uint16_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
void PersonalProfileView::set_update_item_select(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_item_select_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_item_select_);
	}
}

void PersonalProfileView::update_item_select(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
// custom function
void PersonalProfileView::set_item_info(item_info_t* info, int index)
{
	if (info == nullptr || index >= EDIT_PERSONAL_PROFILE_MAX_)
    {
    	assert(false && "[PersonalProfileView:8001]set_item_info index is out");
		return;
    }

    info->item_index = index;
    info->type = (ITEM_TYPES)g_edit_personal_profile_menu_info[index].type;
    memset(info->title, 0, sizeof(info->title));
    memcpy(info->title, _TM(g_edit_personal_profile_menu_info[index].name), strlen(_TM(g_edit_personal_profile_menu_info[index].name)));
	memset(info->subtitle, 0, sizeof(info->subtitle));


	switch (index)	
	{
		case HRM_MAX_:
			sprintf(info->subtitle, "%s %dbpm", _TM(g_display_user_info_auto_detection_text), *p_hrm_max_->get_val(0));
			break;
		case HRM_LACTIC_:
			sprintf(info->subtitle, "%s %dbpm", _TM(g_display_user_info_auto_detection_text), *p_hrm_lactic_->get_val(0));
			break;
		case RIDE_FTP_:
			sprintf(info->subtitle, "%s %dw", _TM(g_display_user_info_auto_detection_text), *p_ftp_ride_->get_val(0));
			break;
		default:
			break;
	}
	
	info->item_text_info.text_align = 1;
}

void PersonalProfileView::set_item_notify(QwMenuItem* item, int index)
{
	item->set_select_pos_handle(select_item_pos_);
}

void PersonalProfileView::select_item_pos(void* item, int x, int y)
{
	if (item == nullptr)
	{
		assert("[PersonalProfileView:8001] item is nullptr");
		return;
	}
	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	if (item_ == nullptr)
	{
		assert("[PersonalProfileView:8001] item_ not is QwMenuItem");
		return;
	}
    // 6.获取子菜单在当前menu中的index
    int select_index = (int)item_->get_user_data();
	switch_to_app(select_index, x, y);
}

void PersonalProfileView::switch_to_app(int select_index, int x, int y)
{
	if ((select_index < 0) || (select_index >= EDIT_PERSONAL_PROFILE_MAX_))
	{
		return;
	}
	manager_->page_command("EditPersonalProfile", 0, &select_index);
	manager_->push("EditPersonalProfile");
}
