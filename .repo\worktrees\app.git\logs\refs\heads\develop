0000000000000000000000000000000000000000 99c1fd31f0314b061d27b4bf817a9093bee8f810 yanxuqiang <<EMAIL>> 1731998851 +0800
99c1fd31f0314b061d27b4bf817a9093bee8f810 831f086841720f633a3077aa9b01adc25ebf0689 yanxuqiang <<EMAIL>> 1732074060 +0800	pull --rebase: Fast-forward
831f086841720f633a3077aa9b01adc25ebf0689 fbb9d3032c3fd0583fcacee6e75c190004832401 yanxuqiang <<EMAIL>> 1732081625 +0800	commit: Dial: 添加表盘相关应用层逻辑
fbb9d3032c3fd0583fcacee6e75c190004832401 5f46477751d2747dd64fcca136b781310a1bd4cd yanxuqiang <<EMAIL>> 1732081656 +0800	commit (amend): Dial: 添加表盘相关应用层逻辑
5f46477751d2747dd64fcca136b781310a1bd4cd 10f722e4c55f0e14adcb42fcff4bea86145339c5 yanxuqiang <<EMAIL>> 1732090868 +0800	pull --rebase: Fast-forward
10f722e4c55f0e14adcb42fcff4bea86145339c5 f5ee448c5656d1de424c09fa4083767687c4c93e yanxuqiang <<EMAIL>> 1732674052 +0800	pull --rebase: Fast-forward
f5ee448c5656d1de424c09fa4083767687c4c93e d95cf0d6cfc051f264bb2ab1aa93f675b10538a1 yanxuqiang <<EMAIL>> 1732692694 +0800	pull --rebase: Fast-forward
d95cf0d6cfc051f264bb2ab1aa93f675b10538a1 d391ae303e1ca1de0c5aa19cc97a02bad4672040 yanxuqiang <<EMAIL>> 1732693917 +0800	commit: [GRID]: 新增网格页面数据订阅/解订阅/获取数据接口
d391ae303e1ca1de0c5aa19cc97a02bad4672040 6ca03cc240fdecc60d9eb0db4f7e4a3a7a135ffe yanxuqiang <<EMAIL>> 1732693927 +0800	commit (amend): [GRID]: 新增网格页面数据订阅/解订阅/获取数据接口
6ca03cc240fdecc60d9eb0db4f7e4a3a7a135ffe aca6f1c6a16fcb950b9cd2293e573d462b08b5ce yanxuqiang <<EMAIL>> 1732694035 +0800	commit: [GRID]: 新增网格页面数据订阅/解订阅/获取数据接口
aca6f1c6a16fcb950b9cd2293e573d462b08b5ce 2001bf54d4061d268bafdb08ff9234115096aec5 yanxuqiang <<EMAIL>> 1732694043 +0800	commit (amend): [GRID]: 新增网格页面数据订阅/解订阅/获取数据接口
2001bf54d4061d268bafdb08ff9234115096aec5 933c8dbb001e72d51fe747aa29b59218fb842b0b yanxuqiang <<EMAIL>> 1732694183 +0800	pull --rebase: Fast-forward
933c8dbb001e72d51fe747aa29b59218fb842b0b cc629730abd975b7058d0b8dcdb0e0b4e871b5c6 yanxuqiang <<EMAIL>> 1733105655 +0800	pull --rebase: Fast-forward
cc629730abd975b7058d0b8dcdb0e0b4e871b5c6 23250d74c3ae9605f804adcaae241e92fb70222e yanxuqiang <<EMAIL>> 1733138658 +0800	pull --rebase: Fast-forward
23250d74c3ae9605f804adcaae241e92fb70222e 864b369219c184e51411b6659052e6c50fb896eb yanxuqiang <<EMAIL>> 1733139158 +0800	commit: GUI: 优化进入卡片逻辑，进入卡片之后才获取周数据，优化活动小时数获取数据接口
864b369219c184e51411b6659052e6c50fb896eb 4ccd998fdabe4fd79a2a05e33e2a96ac9e2b2ce8 yanxuqiang <<EMAIL>> 1733204059 +0800	pull --rebase: Fast-forward
4ccd998fdabe4fd79a2a05e33e2a96ac9e2b2ce8 9a72052e5cff05c5146b78a69dfc47f22b64e229 yanxuqiang <<EMAIL>> 1733204997 +0800	commit: GRID: 运动网格页面添加速度、平均速度、距离、总里程相关callback
9a72052e5cff05c5146b78a69dfc47f22b64e229 41653544a5009b67ef1fa4135b7473dc5879f812 yanxuqiang <<EMAIL>> 1733211935 +0800	rebase (finish): refs/heads/develop onto 41653544a5009b67ef1fa4135b7473dc5879f812
41653544a5009b67ef1fa4135b7473dc5879f812 e69d2735a84d16dfa331887657ce3e097d27be3b yanxuqiang <<EMAIL>> 1733212012 +0800	commit: GUI: 修改日数据和周数据原有控件
e69d2735a84d16dfa331887657ce3e097d27be3b 420eeb388cc6546b6dd76979a82a8f183afd8788 yanxuqiang <<EMAIL>> 1733214887 +0800	commit: DIAL: 修改真机表盘js文件，每秒拿一次表盘数据
420eeb388cc6546b6dd76979a82a8f183afd8788 264bcbd8a1c17d7cb4d1479b6a3651c7e5ce74d2 yanxuqiang <<EMAIL>> 1733231213 +0800	pull --rebase (finish): refs/heads/develop onto 83aa110bf8a92a3fd5e96056275dd201d41b7ece
264bcbd8a1c17d7cb4d1479b6a3651c7e5ce74d2 b9bb8c77e2e072b057f2bafd03c788233e660552 yanxuqiang <<EMAIL>> 1733276653 +0800	pull --rebase (finish): refs/heads/develop onto adbb6f8e1ba3c28c08fe963e3efc9f116e5feb8a
b9bb8c77e2e072b057f2bafd03c788233e660552 abe6f9dd0b585d2cfbc4fcce2cfc0188f93db361 yanxuqiang <<EMAIL>> 1733278094 +0800	commit: DIAL: 移除原sifli 相关quickjs库文件，使用qw_platfor quickjs库文件
abe6f9dd0b585d2cfbc4fcce2cfc0188f93db361 f86c3814f2cfad089611ddc3b64ef292329dfe77 yanxuqiang <<EMAIL>> 1733278202 +0800	commit (amend): DIAL: 移除原sifli 相关quickjs库文件，使用qw_platfo
f86c3814f2cfad089611ddc3b64ef292329dfe77 b9bb8c77e2e072b057f2bafd03c788233e660552 yanxuqiang <<EMAIL>> 1733278404 +0800	reset: moving to HEAD^
b9bb8c77e2e072b057f2bafd03c788233e660552 8ff6108703b15103dd13b430aa4aef66e2a25f32 yanxuqiang <<EMAIL>> 1733278503 +0800	pull --rebase (finish): refs/heads/develop onto 8ff6108703b15103dd13b430aa4aef66e2a25f32
8ff6108703b15103dd13b430aa4aef66e2a25f32 f6c9f375bea219b996847f5f71107f09ce135b05 yanxuqiang <<EMAIL>> 1733278597 +0800	commit: DIAL: 表盘
f6c9f375bea219b996847f5f71107f09ce135b05 9b96e1ac2eb3469f0bb3de50e638ea1e1cd0fdc8 yanxuqiang <<EMAIL>> 1733282059 +0800	pull --rebase (finish): refs/heads/develop onto 9b96e1ac2eb3469f0bb3de50e638ea1e1cd0fdc8
9b96e1ac2eb3469f0bb3de50e638ea1e1cd0fdc8 14e0b9d3929a630893c72df1145f59523d64afbe yanxuqiang <<EMAIL>> 1733292436 +0800	pull --rebase: Fast-forward
14e0b9d3929a630893c72df1145f59523d64afbe c4639ecce03562b206d8e471c8b084fae0034deb yanxuqiang <<EMAIL>> 1733293764 +0800	pull --rebase: Fast-forward
c4639ecce03562b206d8e471c8b084fae0034deb 8f43ad892bb5a0669d90b95ee14886ca3f640b38 yanxuqiang <<EMAIL>> 1733556645 +0800	reset: moving to 8f43ad892bb5a0669d90b95ee14886ca3f640b38
8f43ad892bb5a0669d90b95ee14886ca3f640b38 1b55ad075275fa4aaa059038a2b86f17cbc5be30 yanxuqiang <<EMAIL>> 1733556654 +0800	pull --rebase: Fast-forward
1b55ad075275fa4aaa059038a2b86f17cbc5be30 002c0154fd693d3e6c9ae1bbe17507d6d94f8047 yanxuqiang <<EMAIL>> 1733709616 +0800	commit: TopStatus: 替换状态栏GPS关闭状态图片
002c0154fd693d3e6c9ae1bbe17507d6d94f8047 e9beb73189b77400d221f0c9cbb4d2974fd6db13 yanxuqiang <<EMAIL>> 1734620142 +0800	pull --rebase (finish): refs/heads/develop onto e9beb73189b77400d221f0c9cbb4d2974fd6db13
e9beb73189b77400d221f0c9cbb4d2974fd6db13 fa015c96f899763002d101b27041dbb9b12e0b0a yanxuqiang <<EMAIL>> 1734686043 +0800	pull --rebase: Fast-forward
fa015c96f899763002d101b27041dbb9b12e0b0a 4236167c2ced7eaa99e81d35595835c00dba6348 yanxuqiang <<EMAIL>> 1734765698 +0800	pull --rebase: Fast-forward
4236167c2ced7eaa99e81d35595835c00dba6348 65a9cd035eccddf4676652a6db919f0b6630c4c5 yanxuqiang <<EMAIL>> 1734766850 +0800	commit: GUI: 新增心率卡片点测逻辑
65a9cd035eccddf4676652a6db919f0b6630c4c5 cae0236cedf4045c190c679a0a4d011f2b0db2e8 yanxuqiang <<EMAIL>> 1734933356 +0800	pull --rebase (finish): refs/heads/develop onto cae0236cedf4045c190c679a0a4d011f2b0db2e8
cae0236cedf4045c190c679a0a4d011f2b0db2e8 c099fe4b1713d507d17e6f282a38c0ab27cf4550 yanxuqiang <<EMAIL>> 1744290286 +0800	pull --rebase: Fast-forward
c099fe4b1713d507d17e6f282a38c0ab27cf4550 419fb4fb704fba81db32befd00d4db3629294788 yanxuqiang <<EMAIL>> 1745392887 +0800	pull --rebase: Fast-forward
419fb4fb704fba81db32befd00d4db3629294788 543bdf316b35ff57d150fe66b00942d83fb2b02b yanxuqiang <<EMAIL>> 1745394758 +0800	commit: GUI: 优化表盘JS脚本
543bdf316b35ff57d150fe66b00942d83fb2b02b 419fb4fb704fba81db32befd00d4db3629294788 yanxuqiang <<EMAIL>> 1746498342 +0800	reset: moving to 419fb4fb704fba81db32befd00d4db3629294788
419fb4fb704fba81db32befd00d4db3629294788 9e90d828937ca3860da2df2c797c0937a7b1ee33 yanxuqiang <<EMAIL>> 1746498414 +0800	pull --rebase: Fast-forward
9e90d828937ca3860da2df2c797c0937a7b1ee33 0f852968e59209d3824d71e299be45e2ef9b2651 yanxuqiang <<EMAIL>> 1746498756 +0800	commit: 所有表格横纵坐标字体透明度改为百分之八十
0f852968e59209d3824d71e299be45e2ef9b2651 6f3a293d0693f16ccf09bdfb6d2e169fd3455ec1 yanxuqiang <<EMAIL>> 1746503072 +0800	pull --rebase (finish): refs/heads/develop onto 6f3a293d0693f16ccf09bdfb6d2e169fd3455ec1
6f3a293d0693f16ccf09bdfb6d2e169fd3455ec1 2648d70b7b8d38ec43112625b70dbb4a4a2589c0 yanxuqiang <<EMAIL>> 1746503103 +0800	commit: GUI: 优化心率血氧压力表格显示
2648d70b7b8d38ec43112625b70dbb4a4a2589c0 7460ce1106294bef5c65b4aba35a127508b5f51a yanxuqiang <<EMAIL>> 1746503727 +0800	commit (amend): GUI: 优化心率血氧压力表格显示
7460ce1106294bef5c65b4aba35a127508b5f51a fcb8d2f059eea27646a5fbd2117491ae87744b17 yanxuqiang <<EMAIL>> 1746581286 +0800	pull --rebase (finish): refs/heads/develop onto fcb8d2f059eea27646a5fbd2117491ae87744b17
fcb8d2f059eea27646a5fbd2117491ae87744b17 6d7900b6c600d598518bb6b36ee2ce75409b27c1 yanxuqiang <<EMAIL>> 1746581726 +0800	commit: GUI: 表盘编辑颜色新增循环功切换能，分页器新增隐藏功能
6d7900b6c600d598518bb6b36ee2ce75409b27c1 e4a8936321d5aa11bc64c867f1b0fec87cbb1dad yanxuqiang <<EMAIL>> 1746689219 +0800	pull --rebase (finish): refs/heads/develop onto e4a8936321d5aa11bc64c867f1b0fec87cbb1dad
e4a8936321d5aa11bc64c867f1b0fec87cbb1dad 0a23aefd9828971f81c480bdd0d63ff1e2b8f828 yanxuqiang <<EMAIL>> 1746754263 +0800	pull --rebase: Fast-forward
0a23aefd9828971f81c480bdd0d63ff1e2b8f828 a478c40793ca35ded9ea4d2770080d3de069043c yanxuqiang <<EMAIL>> 1746754531 +0800	commit: GUI: 新增表盘图片设置控件，新增一块1.5M缓存buffer,加速表盘加载速度
a478c40793ca35ded9ea4d2770080d3de069043c e3d731a24ad3a2797d87104582e6f410da330a64 yanxuqiang <<EMAIL>> 1746754547 +0800	commit (amend): GUI: 新增表盘图片设置控件，新增一块1.5M缓存buffer,加速表盘加载速度
e3d731a24ad3a2797d87104582e6f410da330a64 e885391fa4bb5b9af268cfad85c2f2465086b829 yanxuqiang <<EMAIL>> 1746757721 +0800	pull --rebase (finish): refs/heads/develop onto e885391fa4bb5b9af268cfad85c2f2465086b829
e885391fa4bb5b9af268cfad85c2f2465086b829 afa8c379d8bc9b1d0c5a5a308928a6131342905a yanxuqiang <<EMAIL>> 1746757826 +0800	commit: GUI: 修复健康柱状表格显示异常问题，优化表盘缩略图电量显示异常问题
afa8c379d8bc9b1d0c5a5a308928a6131342905a eac76986569572bc4e6f25e2b1ba3cdacf78ab2b yanxuqiang <<EMAIL>> 1746758660 +0800	commit (amend): GUI: 修复健康柱状表格显示异常问题，优化表盘缩略图电量显示异常问题
eac76986569572bc4e6f25e2b1ba3cdacf78ab2b 1cb6c2546c07ab890083cd8fa55003f2488fd8a2 yanxuqiang <<EMAIL>> 1747619029 +0800	pull --rebase (finish): refs/heads/develop onto 1cb6c2546c07ab890083cd8fa55003f2488fd8a2
1cb6c2546c07ab890083cd8fa55003f2488fd8a2 acd7c611dbbacee5fb69fbfcbe7b904d45131fe0 yanxuqiang <<EMAIL>> 1747791994 +0800	pull --rebase: Fast-forward
acd7c611dbbacee5fb69fbfcbe7b904d45131fe0 8ec20a4302134854108b78595c9a9d751bd029e0 yanxuqiang <<EMAIL>> 1747792202 +0800	commit: GUI: 优化运动动结算页，海拔，气压表格数据逻辑，提高帧率
8ec20a4302134854108b78595c9a9d751bd029e0 37af6443ca627b37bade3a8ff7f67822fd82161d yanxuqiang <<EMAIL>> 1747820775 +0800	pull --rebase (finish): refs/heads/develop onto 37af6443ca627b37bade3a8ff7f67822fd82161d
37af6443ca627b37bade3a8ff7f67822fd82161d 88fa1a85656bb89ba5cca4294178c2bd02d643c5 yanxuqiang <<EMAIL>> 1747820902 +0800	commit: GUI: 开发者模式新增心率图表数据倒灌功能
88fa1a85656bb89ba5cca4294178c2bd02d643c5 1734a51b7b81f8ded3344ab6e33ec76ba1e56423 yanxuqiang <<EMAIL>> 1747821059 +0800	commit: GUI: 解决表盘编辑相关bug
1734a51b7b81f8ded3344ab6e33ec76ba1e56423 016b45f101bff712eaf4e3d423b8e9a9b49cd44e yanxuqiang <<EMAIL>> 1747821141 +0800	commit (amend): GUI: 解决表盘编辑相关bug
016b45f101bff712eaf4e3d423b8e9a9b49cd44e e473b6eec982dcf6ac1f6177f722ed357984e01b yanxuqiang <<EMAIL>> 1747884261 +0800	pull --rebase (finish): refs/heads/develop onto e473b6eec982dcf6ac1f6177f722ed357984e01b
e473b6eec982dcf6ac1f6177f722ed357984e01b 3b0c3ea36e35062e02cb3e3363c33e2e30c214a6 yanxuqiang <<EMAIL>> 1747884337 +0800	commit: GUI: 新增压力、血氧图表倒灌功能
3b0c3ea36e35062e02cb3e3363c33e2e30c214a6 f4f9d1523250d6435c21c09a700d02014fb31774 yanxuqiang <<EMAIL>> 1747904103 +0800	pull --rebase (finish): refs/heads/develop onto f4f9d1523250d6435c21c09a700d02014fb31774
f4f9d1523250d6435c21c09a700d02014fb31774 cdf10ec096d61bd5384dbb953eab3c11c89badc8 yanxuqiang <<EMAIL>> 1747904156 +0800	commit: GUI: 开发者模式新增步数，卡路里，活动小时数，强度活动时长数据倒灌功能
cdf10ec096d61bd5384dbb953eab3c11c89badc8 773c05f5bd777474f2fb3af57c0a165e11ae6eb4 yanxuqiang <<EMAIL>> 1747987158 +0800	pull --rebase (finish): refs/heads/develop onto 773c05f5bd777474f2fb3af57c0a165e11ae6eb4
773c05f5bd777474f2fb3af57c0a165e11ae6eb4 cd82edc3c472bb7a60c494d348ca228f9050d755 yanxuqiang <<EMAIL>> 1747987205 +0800	commit: ALGO: 优化血氧算法检测逻辑
cd82edc3c472bb7a60c494d348ca228f9050d755 a12ad7df8592ffcfd51e33539ac49fd2af918210 yanxuqiang <<EMAIL>> 1748057308 +0800	pull --rebase: Fast-forward
a12ad7df8592ffcfd51e33539ac49fd2af918210 d6200e6748d314a4bb7d9fe5a9eabcbc10d54df2 yanxuqiang <<EMAIL>> 1748057346 +0800	commit: GUI: 解决心率图表横线颜色不正确问题
d6200e6748d314a4bb7d9fe5a9eabcbc10d54df2 fa6ad0c3513d3acf7421a62e0ff86eeb97d3f9a3 yanxuqiang <<EMAIL>> 1748241212 +0800	pull --rebase (finish): refs/heads/develop onto fa6ad0c3513d3acf7421a62e0ff86eeb97d3f9a3
fa6ad0c3513d3acf7421a62e0ff86eeb97d3f9a3 5947b7feb52fbd8ec27c24724c2da8dccd9a73e0 yanxuqiang <<EMAIL>> 1748241265 +0800	commit: GUI: 修复表盘旋转旋钮页码不显示问题，优化页码显示逻辑
5947b7feb52fbd8ec27c24724c2da8dccd9a73e0 c90508d83d136d3765593ea92aaf8c438786be2a yanxuqiang <<EMAIL>> 1748248051 +0800	commit: GUI: 解决表盘缩略图显示异常问题
c90508d83d136d3765593ea92aaf8c438786be2a 5947b7feb52fbd8ec27c24724c2da8dccd9a73e0 yanxuqiang <<EMAIL>> 1748248081 +0800	reset: moving to 5947b7feb52fbd8ec27c24724c2da8dccd9a73e0
5947b7feb52fbd8ec27c24724c2da8dccd9a73e0 a3d1988597bad47bbb7479914446b6c9f3a6fb2c yanxuqiang <<EMAIL>> 1748248102 +0800	pull --rebase (finish): refs/heads/develop onto a3d1988597bad47bbb7479914446b6c9f3a6fb2c
a3d1988597bad47bbb7479914446b6c9f3a6fb2c c51c67c7a5d9470448625d06e786d511256961aa yanxuqiang <<EMAIL>> 1748248116 +0800	commit: GUI: 解决表盘缩略图显示异常问题
c51c67c7a5d9470448625d06e786d511256961aa 70839274a35515d9b5aa40c538f4b017e7b12350 yanxuqiang <<EMAIL>> 1748334964 +0800	pull --rebase (finish): refs/heads/develop onto 70839274a35515d9b5aa40c538f4b017e7b12350
70839274a35515d9b5aa40c538f4b017e7b12350 c794559c1144681ebb1726f5eedee0f4d73451e0 yanxuqiang <<EMAIL>> 1748335154 +0800	commit: GUI: 表盘编辑数据展示页面优化，根据语言来排序，中文根据拼英，英文根据首字母
c794559c1144681ebb1726f5eedee0f4d73451e0 64e584a90883b76f8781da317462b607bd4e22f3 yanxuqiang <<EMAIL>> 1748336535 +0800	commit (amend): GUI: 表盘编辑数据展示页面优化，根据语言来排序，中文根据拼英，英文根据首字母
64e584a90883b76f8781da317462b607bd4e22f3 071ef56d9935b4676884dd51298112b7c14e1e34 yanxuqiang <<EMAIL>> 1748338042 +0800	commit (amend): GUI: 表盘编辑数据展示页面优化，根据语言来排序，中文根据拼英，英文根据首字母
071ef56d9935b4676884dd51298112b7c14e1e34 77624283f1b1978ead4650d52769be91af5f4704 yanxuqiang <<EMAIL>> 1748350751 +0800	pull --rebase (finish): refs/heads/develop onto 77624283f1b1978ead4650d52769be91af5f4704
77624283f1b1978ead4650d52769be91af5f4704 b65f3410875db64d2be6d731f4bab5d555fde53b yanxuqiang <<EMAIL>> 1748350794 +0800	commit: GUI: 解决心率，血氧，压力，点测时无动画问题
b65f3410875db64d2be6d731f4bab5d555fde53b 7b866f3bf6fe462bbdad122d04a3e3ee36a67650 yanxuqiang <<EMAIL>> 1748483115 +0800	pull --rebase (finish): refs/heads/develop onto 7b866f3bf6fe462bbdad122d04a3e3ee36a67650
7b866f3bf6fe462bbdad122d04a3e3ee36a67650 a027b44becf5b6a6c8676028f3dd592d3471f924 yanxuqiang <<EMAIL>> 1748483436 +0800	commit: GUI: 1.新增表盘loading页面 2.GUI线程新增表盘同步事件 3.优化表盘新增删除操作
a027b44becf5b6a6c8676028f3dd592d3471f924 4e15b88f73f36e0c5dc060be88caf6215767fee5 yanxuqiang <<EMAIL>> 1748483455 +0800	commit (amend): GUI: 1.新增表盘loading页面 2.GUI线程新增表盘同步事件 3.优化表盘新增删除操作
4e15b88f73f36e0c5dc060be88caf6215767fee5 a0715ad4376696413ae1f6deedac4aca216afa77 yanxuqiang <<EMAIL>> 1748599015 +0800	rebase (finish): refs/heads/develop onto a0715ad4376696413ae1f6deedac4aca216afa77
a0715ad4376696413ae1f6deedac4aca216afa77 d2219f5aecdf2aaca29c2e94898618c8c00dcc3e yanxuqiang <<EMAIL>> 1748599040 +0800	commit: GUI: 优化工具中心手势斜向上滑动时回弹问题
d2219f5aecdf2aaca29c2e94898618c8c00dcc3e a6db68ba45af277b6de863ec0a61197ee1982a9f yanxuqiang <<EMAIL>> 1748599048 +0800	commit (amend): GUI: 优化工具中心手势斜向上滑动时回弹问题
a6db68ba45af277b6de863ec0a61197ee1982a9f 54a74ab139b9eff4ec9a6cc54a9c373bfe278378 yanxuqiang <<EMAIL>> 1749633681 +0800	pull --rebase: Fast-forward
54a74ab139b9eff4ec9a6cc54a9c373bfe278378 464834ed0f613a8a9d15a830b69ac7b255df5d68 yanxuqiang <<EMAIL>> 1749633716 +0800	commit: GUI: 运动结算页适配新swip控件
464834ed0f613a8a9d15a830b69ac7b255df5d68 5131d4128abb6e75af1981c9e1accc3e5ae75e14 yanxuqiang <<EMAIL>> 1749633733 +0800	commit (amend): GUI: 运动结算页适配新swip控件
5131d4128abb6e75af1981c9e1accc3e5ae75e14 2b13b5f2453058fe43b9a487deea88aa4c2dc5e5 yanxuqiang <<EMAIL>> 1749694956 +0800	pull --rebase (finish): refs/heads/develop onto 2b13b5f2453058fe43b9a487deea88aa4c2dc5e5
2b13b5f2453058fe43b9a487deea88aa4c2dc5e5 8bae73cb7530c41ae04e3bbd972fceedb13e4907 yanxuqiang <<EMAIL>> 1749695048 +0800	commit: GUI: 修复卡路里步数上限不正确问题
8bae73cb7530c41ae04e3bbd972fceedb13e4907 9e8322f51e6a30df84c61b954997b641c4fc300d yanxuqiang <<EMAIL>> 1749696678 +0800	commit: GUI: 优化心率血氧压力hrv七天数据图表显示问题，中间没数据则断开
9e8322f51e6a30df84c61b954997b641c4fc300d 6c1f1fbd64da8952cf8c6ab87f2779a970445f4c yanxuqiang <<EMAIL>> 1749697447 +0800	commit: GUI: 优化睡眠颜色值
6c1f1fbd64da8952cf8c6ab87f2779a970445f4c cd6b4fa3e8372feba497d00729e000526defe1f0 yanxuqiang <<EMAIL>> 1749799550 +0800	pull --rebase (finish): refs/heads/develop onto cd6b4fa3e8372feba497d00729e000526defe1f0
