/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SummaryDetailContainer.h
@Time    :   2025/01/09 19:07:10
* 
**************************************************************************/

#pragma once
#include "QwAppScrollList/QwAppScrollList.h"
#include "Summary/Summary/SummaryModel.h"
#include "QwVerticalSwipe/QwVerticalSwipeContainer.h"

class SummaryDetailContainer : public QwSwipePageBase
{
private:

    // Drawable
    Container con_;
	QwAppScrollList content_;
    Box background_;
	Image sport_icon_;
	TextArea fit_type_;
    QwItemSummary data_[MAX_SUMMARY_NUMBER];

    bool already_pushed_;
    int sport_img_icon_index_;

protected:

public:
	SummaryDetailContainer();
	virtual ~SummaryDetailContainer
    ();

    // PageView override
    void setup();
    
    // Screen override
    void handleKeyEvent(uint8_t c) override;
    void handleTickEvent() override;
    void handleClickEvent(const ClickEvent& evt) override;
    void handleDragEvent(const DragEvent& evt) override;
    void handleGestureEvent(const GestureEvent& evt) override;
    
    // Notification Callback

    // ObserverDrawable Callback
    void update_sport_type(Drawable* ctrl, Parameters<SPORTTYPE>* data, int idx);

    void update_summary_data(Drawable* ctrl, Parameters<QwItemSummary::INFO>* data, int idx);

    void set_sport_img_icon_index(int index);
};