/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwVerticalSwipeContainer.h
@Time    :   2025/01/03 15:18:52
* 
**************************************************************************/

/**
 * @file touchgfx/containers/SwipeContainerPy.hpp
 *
 * Declares the touchgfx::SwipeContainerPy class.
 */
#ifndef TOUCHGFX_VERTICALSWIPECONTAINERPY_H
#define TOUCHGFX_VERTICALSWIPECONTAINERPY_H

#include "QwGridItem/GridBase/QwAppCtrlInc.h"
#include <touchgfx/mixins/DragListener.hpp>
#include <touchgfx/Callback.hpp>

namespace touchgfx
{
#define SWIP_FOCUS_WATCHES_UP_KEY 4
#define SWIP_FOCUS_WATCHES_DOWN_KEY 5
#define SEIP_DYNAMIC_CACHING_NUM 3
/**
 * A QwVerticalSwipeContainer is a Container with a vertical laid out list of identically sized Drawables.
 * The bottom of the QwVerticalSwipeContainer shows a page indicator to indicate the position in the vertical
 * list of items in the QwVerticalSwipeContainer.
 *
 * @see ListLayout
 */
class QwSwipePageBase : public Container
{
private:

    bool used_drag_;
    bool used_gesture_;
    bool used_key_;

public:
    QwSwipePageBase() :
        used_drag_(false),
        used_gesture_(false),
        used_key_(false)
    {};
    ~QwSwipePageBase() {};

    void handleTickEvent();
    virtual void handleClickEvent(const ClickEvent& event) {};
    virtual void handleDragEvent(const DragEvent& event);
    virtual void handleGestureEvent(const GestureEvent& event);
    virtual void handleKeyEvent(uint8_t key);

    void set_used_drag(bool state){used_drag_ = state; };
    void set_used_gesture(bool state){used_gesture_ = state; };
    void set_used_key(bool state){used_key_ = state; };

    bool get_used_drag() {return used_drag_; };
    bool get_used_gesture() {return used_gesture_; };
    bool get_used_key() {return used_key_; };

};

class QwVerticalSwipeContainer : public Container
{
public:
    QwVerticalSwipeContainer();
    virtual ~QwVerticalSwipeContainer();

    virtual void handleTickEvent();
    virtual void handleClickEvent(const ClickEvent& event);
    virtual void handleDragEvent(const DragEvent& event);
    virtual void handleGestureEvent(const GestureEvent& event);
    virtual void handleKeyEvent(uint8_t key);

    QwSwipePageBase* getCurrentQwSwipePageBase();

    QwSwipePageBase* getQwSwipePageBaseFromIndex(int index);

    /**
     * Adds a page to the container.
     *
     * @param [in] page The page to add.
     *
     * @note All pages must have the same width and height.
     */
    virtual void add(Drawable& page);

    /**
     * Removes the page from the container.
     *
     * @param [in] page The page to remove.
     *
     * @note This is safe to call even if page is not a page (in which case nothing happens).
     */
    virtual void remove(Drawable& page);

    /**
     * Set the swipe cutoff which indicates how far you should drag a page before it results in
     * a page change.
     *
     * @param  cutoff The cutoff in pixels.
     */
    virtual void setSwipeCutoff(uint16_t cutoff);

    /**
     * Sets the x and y position of the page indicator.
     *
     * @param  x The x coordinate.
     * @param  y The y coordinate.
     *
     * @see setPageIndicatorXYWithCenteredX, setPageIndicatorCenteredX
     */
    void setPageIndicatorXY(int16_t x, int16_t y);

    /**
     * Sets the x and y position of the page indicator. The value specified as x will be the
     * center coordinate of the page indicators.
     *
     * @param  x The center x coordinate.
     * @param  y The y coordinate.
     *
     * @see setPageIndicatorCenteredX, setPageIndicatorXY
     *
     * @note This method should not be used until all pages have been added, the
     *       setPageIndicatorBitmaps() has been called and the page indicator therefore has the
     *       correct width.
     */
    void setPageIndicatorXYWithCenteredX(int16_t x, int16_t y);

    /**
     * Sets the page indicator centered inside the SwipeContainerPy without changing the y
     * position.
     *
     * @see setPageIndicatorXYWithCenteredX, setPageIndicatorXY
     *
     * @note This method should not be used until all pages have been added, the
     *       setPageIndicatorBitmaps() has been called and the page indicator therefore has the
     *       correct width.
     */
    void setPageIndicatorCenteredX();

    /**
     * Sets the x position of the page indicator without changing the y position. The value
     * specified as x will be the center coordinate of the page indicators.
     *
     * @param  x The center x coordinate.
     *
     * @see setPageIndicatorXYWithCenteredX, setPageIndicatorXY
     *
     * @note This method should not be used until all pages have been added, the
     *       setPageIndicatorBitmaps() has been called and the page indicator therefore has the
     *       correct width.
     */
    void setPageIndicatorCenteredX(int16_t x);

    /**
     * Sets the bitmaps that are used by the page indicator. The bitmap for the normal page is
     * repeated side-by-side and the bitmap for a highlighted page is put in the proper position.
     *
     * @param  normalPage      The normal page.
     * @param  highlightedPage The highlighted page.
     */
    void setPageIndicatorBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage);

    /**
     * When dragging either one of the end pages a part of the background will become visible
     * until the user stop dragging and the end page swipes back to its position. The width of
     * this area is set by this method.
     *
     * @param  width The width in pixels.
     */
    void setEndSwipeElasticWidth(uint16_t width);

    /**
     * Gets number of pages.
     *
     * @return The number of pages.
     */
    uint8_t getNumberOfPages()
    {
        return dynamicCachingEnabled ? totalPages : pageIndicator.getNumberOfPages();
    }

    /**
     * Sets the selected page.
     *
     * @param  pageIndex Zero-based index of the page. Range from 0 to numberOfPages-1.
     *
     * @see getSelectedPage
     */
    void setSelectedPage(uint8_t pageIndex);

    /**
     * Gets the currently selected page.
     *
     * @return Zero-based index of the current page. Rage from 0 to numberOfPages-1.
     *
     * @see setSelectedPage
     */
    uint8_t getSelectedPage() const;

    void setPageIndicatorNumber(uint8_t num);

    void AutoAnimateUp();
    void AutoAnimateDown();

    // Dynamic page management
    void setDynamicCaching(bool enable, uint8_t totalPages = 0);
    void setPageUpdateCallback(GenericCallback<QwSwipePageBase*, int16_t>& callback);
    bool isDynamicCachingEnabled() const { return dynamicCachingEnabled; }
    // 添加定位new操作符重载
    void* operator new(size_t size, void* ptr) {
        return ptr;
    }
private:
    static const int16_t DRAG_CANCEL_THRESHOLD = 3;

    enum States
    {
        ANIMATE_SWIPE_CANCELLED_UP,
        ANIMATE_SWIPE_CANCELLED_DOWN,
        ANIMATE_UP,
        ANIMATE_DOWN,
        ANIMATE_SWIPE_OUT_UP,
        ANIMATE_SWIPE_OUT_DOWN,
        NO_ANIMATION
    } currentState;

    uint8_t animationCounter;
    uint16_t swipeCutoff;
    int16_t dragX;
    int16_t animateDistance;
    int16_t startX;
    uint16_t endElasticWidth;
    int32_t tick_ = 0;
    bool pageIndicator_show = false;
    int pressed_page_index_ = 0;

    ListLayout pages;
    QwPageIndicator pageIndicator;

    // Dynamic caching members
    bool dynamicCachingEnabled;
    uint8_t totalPages;
    int16_t currentPageIndex_;
    QwSwipePageBase cachedPages_[SEIP_DYNAMIC_CACHING_NUM];
    GenericCallback<QwSwipePageBase*, int16_t>* pageUpdateCallback;
    // 循环缓存管理
    int16_t pageMapping[SEIP_DYNAMIC_CACHING_NUM];  // 记录每个缓存对象当前显示的页面索引

    void adjustPages();

    // Dynamic caching methods
    void initializeDynamicCache();
    void cleanupDynamicCache();
    void updateCachedPages();
    QwSwipePageBase* getCachedPage(int16_t pageIndex);
    void updatePageContent(QwSwipePageBase* page, int16_t pageIndex);
    void calculateRequiredPages(int16_t currentPage, int16_t& startPage, int16_t& endPage);

    // 滑动时不缓存，停止时重新缓存
    void rebuildCacheAfterScroll();

    // 页面元素重新添加方法，动态缓存专用
    void refreshPagesContainer();
    void updateCacheMap();

    void animateSwipeCancelledUp();
    void animateSwipeCancelledDown();
    void animateUp();
    void animateDown();
    void animateSwipeOutUp();
    void animateSwipeOutDown();
};

} // namespace touchgfx

#endif // TOUCHGFX_VERTICALSWIPECONTAINERPY_H
