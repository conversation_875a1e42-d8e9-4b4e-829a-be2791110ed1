/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SportsMenuView.cpp
@Time    :   2024/12/13 16:15:19
*
**************************************************************************/
#include "SportsMenuView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "../../qwos_app/GUI/Translate/QwDataKeyText.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "GUI/QwGUIImage.h"
#include "countdown_timer_app/countdown_timer_app.h"
#include "navi_srv_module/navi_srv_module.h"

static const char g_sport_list_view_title[] = "_sports_list";
TM_KEY(g_sport_list_view_title)

static const qw_iamge_menu_info g_active_sport_name[] = {
    {(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[0]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[1]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[2]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[3]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[4]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[5]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[6]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[7]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[8]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "", g_sport_img_72[9]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[10]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[11]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[12]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[13]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[14]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[15]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[16]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[17]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[18]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[19]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[20]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[21]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[22]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[23]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[24]},
	{(const int)ITEM_TYPES::ITEM_IMAGE_TEXT, "",  g_sport_img_72[25]},
	// {"HIIT", nullptr},
	// {"Rowing boat", nullptr},
	// {"Pulp board", nullptr},
	// {"Fitness", nullptr},
	// {"Snowboarding", nullptr},
	// {"Snowboarding2", nullptr},
	// {"Cross - Country Skiing", nullptr},
	// {"Yoga", nullptr},
	// {"Pilates", nullptr},
	// {"Outdoor frisbee", nullptr},
};







SportsMenuView::SportsMenuView(PageManager* manager) :
	QwMenuView(manager),p_return_page_(nullptr),p_set_return_page_(nullptr),
	p_sports_select_(nullptr),p_playground_track_info_(nullptr),p_pool_swimming_info_(nullptr),
	p_sports_count_(nullptr),p_sports_custom_(nullptr),p_item_select_(nullptr),
	p_refresh_flag_(nullptr),p_set_refresh_flag_(nullptr),
	on_sports_select_(this, &SportsMenuView::on_click_sports_select),
	update_playground_track_info_(this, &SportsMenuView::update_playground_track_info),
	update_pool_swimming_info_(this, &SportsMenuView::update_pool_swimming_info),
	update_sports_count_(this, &SportsMenuView::update_sports_count),
	update_sports_custom_(this, &SportsMenuView::update_sports_custom),
	update_item_select_(this, &SportsMenuView::update_item_select),
	custom_on_sports_select_(this, &SportsMenuView::custom_on_sports_select),
	p_sports_type_(nullptr),p_set_sports_type_(nullptr),
	on_set_sports_type_(this, &SportsMenuView::on_click_set_sports_type),
	update_sports_type_(this, &SportsMenuView::update_sports_type),
	on_set_return_page_(this, &SportsMenuView::on_click_set_return_page),
	update_return_page_(this, &SportsMenuView::update_return_page),
	on_set_refresh_flag_(this, &SportsMenuView::on_click_set_refresh_flag),
	update_refresh_flag_(this, &SportsMenuView::update_refresh_flag)
{

}

SportsMenuView::~SportsMenuView()
{

}

void SportsMenuView::setup()
{
	QwMenuView::show_menu(*p_sports_count_->get_val(0), *p_item_select_->get_val(0), _TM(g_sport_list_view_title));
	QwMenuView::list_.set_list_loop_enable(true);
    QwMenuView::list_.set_scroll_bar_enable(false);
}

void SportsMenuView::quit()
{

}

void SportsMenuView::handleTickEvent()
{
	QwMenuView::list_.handleTickEvent();

}

void SportsMenuView::handleKeyEvent(uint8_t c)
{
    QwMenuView::list_.handleKeyEvent(c);
    if (c == KEY_CLK_BACK)
    {
        if (p_return_page_ != nullptr && *p_return_page_->get_val(0) != nullptr)
        {
            const char* page = *p_return_page_->get_val(0);
			if(strcmp(page,"Launcher")==0)
			{
				navi_srv_set_prepare_status(NAVI_PREPARE_STATUS_CLOSE);
			}
            manager_->push(page);
            on_click_set_return_page(nullptr);
        }
        else
        {
			navi_srv_set_prepare_status(NAVI_PREPARE_STATUS_CLOSE);
            manager_->push("Launcher");
        }
    }
    else if (c == KEY_CLK_START)
    {
        int select_index = get_select_app();
        switch_to_app(select_index);
    }
}

void SportsMenuView::handleClickEvent(const ClickEvent& evt)
{
	QwMenuView::list_.handleClickEvent(evt);

}

void SportsMenuView::handleDragEvent(const DragEvent& evt)
{
	QwMenuView::list_.handleDragEvent(evt);

}

void SportsMenuView::handleGestureEvent(const GestureEvent& evt)
{
	QwMenuView::list_.handleGestureEvent(evt);

    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL
        && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
    {
        if (p_return_page_ != nullptr && *p_return_page_->get_val(0) != nullptr)
        {
            const char* page = *p_return_page_->get_val(0);
			if(strcmp(page,"Launcher")==0)
			{
				navi_srv_set_prepare_status(NAVI_PREPARE_STATUS_CLOSE);
			}
            manager_->push(page);
            on_click_set_return_page(nullptr);
        }
        else
        {
			navi_srv_set_prepare_status(NAVI_PREPARE_STATUS_CLOSE);
            manager_->push("Launcher");
        }
    }
}

// Notification Callback function
void SportsMenuView::set_on_sports_select(Notification<uint8_t>* command)
{
	p_sports_select_ = command;
}

void SportsMenuView::on_click_sports_select(uint8_t pressed)
{
	if (p_sports_select_ != nullptr)
	{
		p_sports_select_->notify(pressed);
	}
}
void SportsMenuView::set_on_set_sports_type(Notification<uint8_t>* command)
{
	p_set_sports_type_ = command;
}

void SportsMenuView::on_click_set_sports_type(uint8_t t1)
{
	if (p_set_sports_type_ != nullptr)
	{
		p_set_sports_type_->notify(t1);
	}
}
void SportsMenuView::set_on_set_return_page(Notification<char*>* command)
{
	p_set_return_page_ = command;
}

void SportsMenuView::on_click_set_return_page(char* t1)
{
	if (p_set_return_page_ != nullptr)
	{
		p_set_return_page_->notify(t1);
	}
}
void SportsMenuView::set_on_set_refresh_flag(Notification<uint8_t>* command)
{
	p_set_refresh_flag_ = command;
}

void SportsMenuView::on_click_set_refresh_flag(uint8_t t1)
{
	if (p_set_refresh_flag_ != nullptr)
	{
		p_set_refresh_flag_->notify(t1);
	}
}
// ObserverDrawable Callback function
void SportsMenuView::set_update_playground_track_info(ObserverDrawable<Drawable, int, 1>* observer)
{
	if (observer != nullptr)
	{
		p_playground_track_info_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_playground_track_info_);
	}
}

void SportsMenuView::update_playground_track_info(Drawable* ctrl, Parameters<int>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SportsMenuView::set_update_pool_swimming_info(ObserverDrawable<Drawable, int, 1>* observer)
{
	if (observer != nullptr)
	{
		p_pool_swimming_info_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_pool_swimming_info_);
	}
}

void SportsMenuView::update_pool_swimming_info(Drawable* ctrl, Parameters<int>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SportsMenuView::set_update_sports_count(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_sports_count_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_sports_count_);
	}
}

void SportsMenuView::update_sports_count(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void SportsMenuView::set_update_sports_custom(ObserverDrawable<Drawable, uint8_t, SPORTS_MENU_SPORTS_NUM>* observer)
{
	if (observer != nullptr)
	{
		p_sports_custom_ = observer;
		for (int i = 0; i < SPORTS_MENU_SPORTS_NUM; i++)
		{
			observer->bind_ctrl(i, QwMenuView::list_);
		}
		observer->bind_notify(update_sports_custom_);
	}
}

void SportsMenuView::update_sports_custom(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr && idx < SPORTS_MENU_SPORTS_NUM)
	{

	}
}

void SportsMenuView::set_update_item_select(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_item_select_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_item_select_);
	}
}

void SportsMenuView::update_item_select(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
void SportsMenuView::set_update_sports_type(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_sports_type_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_sports_type_);
	}
}

void SportsMenuView::update_sports_type(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
void SportsMenuView::set_update_return_page(ObserverDrawable<Drawable, char*, 1>* observer)
{
	if (observer != nullptr)
	{
		p_return_page_ = observer;
        observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_return_page_);
	}
}

void SportsMenuView::update_return_page(Drawable* ctrl, Parameters<char*>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
// custom function
void SportsMenuView::set_item_info(item_info_t* info, int index)
{
    if (info == nullptr || index >= *p_sports_count_->get_val(0))
    {
    	assert(false && "[TestFocusMenuView:8001]set_item_info index is out");
		return;
    }

    info->item_index = index;
	index = *p_sports_custom_->get_val(index);
    info->type = (ITEM_TYPES)g_active_sport_name[index].type;
    memset(info->title, 0, sizeof(info->title));
    memcpy(info->title, get_sports_name_by_type(index), strlen(get_sports_name_by_type(index)));
    memset(info->subtitle, 0, sizeof(info->subtitle));

    //info->item_text_info.text_align = 1;
    info->img_path = g_active_sport_name[index].image;
}

void SportsMenuView::set_item_notify(QwMenuItem* item, int index)
{
   item->set_select_handle(custom_on_sports_select_);
}

void SportsMenuView::custom_on_sports_select(void* item)
{
	if (item == nullptr)
	{
		assert("[SportsMenuView:8001]custom_on_sports_select item is nullptr");
		return;
	}

	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	// QwMenuItem* item_;
	if (item_ == nullptr)
	{
		assert("[SportsMenuView:8001]custom_on_sports_select item_ not is QwMenuItem");
		return;
	}

	int select_index = (int)item_->get_user_data();

	switch_to_app(select_index);
}

void SportsMenuView::switch_to_app(int index)
{
	on_click_sports_select(index);
	int type = *p_sports_custom_->get_val(index);
	if (type == SPORTSTYPE_PLAYGROUND)
    {
        if (*p_playground_track_info_->get_val(0) == -1)
        {
	        int last_page = 0;
            manager_->page_command("TrackSetting", (int)TrackSetting_CMD::SET_LAST_PAGE, &last_page);
            manager_->push("TrackSetting");

        }
        else
        {
            manager_->page_command("SportStart", (int)SportStart_CMD::SET_READY_SPORT, NULL);
	        manager_->push("SportStart");
        }
    }
	else if( type == SPORTSTYPE_POOL_SWIMMING)
	{
		if(*p_pool_swimming_info_->get_val(0) == -1)
		{
			manager_->page_command("SwimLaneSetting", (int)TrackSetting_CMD::SET_LAST_PAGE, (void*)"SportsMenu");
            manager_->push("SwimLaneSetting");
		}
		else
		{
            manager_->page_command("SportStart", (int)SportStart_CMD::SET_READY_SPORT, NULL);
	        manager_->push("SportStart");
		}
	}
    else
    {
        manager_->page_command("SportStart", (int)SportStart_CMD::SET_READY_SPORT, NULL);
	    manager_->push("SportStart");
    }
}

void SportsMenuView::set_update_refresh_flag(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_refresh_flag_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_refresh_flag_);
	}
}

void SportsMenuView::update_refresh_flag(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
        if(list_.getParent() && data->get_val() != 0)
        {
            list_.restore_initial_state();
			setup();
            p_set_refresh_flag_->notify(0);
        }
	}
}
