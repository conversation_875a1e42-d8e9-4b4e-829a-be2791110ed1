/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   HistoryCard.cpp
@Time    :   2024/12/16 17:36:25
<AUTHOR>   lxin
* 
**************************************************************************/
#include "HistoryCard.h"
#include "Image/images.h"
#include "../module/gui/Translate/Translate.h"
#include "qw_fit_api.h"
#include "qw_time_service.h"
#include "file_manage.h"
#include "QwGUIImage.h"

static const char *g_history_card_text[] = {
    "_history",
    "_no_records",
};
TM_DECLARE(g_history_card_text)

HistoryCard::HistoryCard() : 
    AppCardIf()
{
}

HistoryCard::~HistoryCard()
{

}

void HistoryCard::setup(Container& base, void *data)
{
    base.add(card_name_);
    base.add(card_icon_);
    base.add(activity_icon_);
    base.add(fit_info_);
    base.add(fit_info_unit_);

    card_icon_.setBitmap(Bitmap(&app_historical_record));
    card_icon_.setAlign(ALIGN_IN_LM, 40);

    card_name_.setTextFont(&PUBLIC_NO_32_R_FONT);
    card_name_.setTextAlignment(LEFT);
    card_name_.setColor(lv_color_white());
    card_name_.setLabelAlpha(LV_OPA_TRANSP);

    fit_info_.setTextFont(&NUMBER_S_FONT);
    fit_info_.setTextAlignment(LEFT);
    fit_info_.setColor(lv_color_white());
    fit_info_.setLabelAlpha(LV_OPA_TRANSP);
    
    fit_info_unit_.setTextFont(&PUBLIC_NO_32_R_FONT);
    fit_info_unit_.setTextAlignment(LEFT);
    fit_info_unit_.setColor(lv_color_white());
    fit_info_unit_.setLabelAlpha(LV_OPA_TRANSP);
    fit_info_unit_.setAlpha(LV_OPA_60);
    
    update_daily_data(data);
}

void HistoryCard::update_daily_data(void *data)
{
    fit_brief_t fit_info;

    if (fit_activity_get_file_brief(0, &fit_info) == FIT_OK)
    {
        activity_icon_.setVisible(true);
        if (fit_info.sport < FIT_SPORTS_HIIT && fit_info.sport >= 0)
        {
            activity_icon_.setBitmap(Bitmap(g_sport_img_44[fit_info.sport]));
        }
        else
        {
            activity_icon_.setBitmap(Bitmap(&sport_bike_44));
        }
        activity_icon_.setAlign(ALIGN_IN_LT, 144, 16);

        char fnamestr[50] = { 0 };
        qw_tm_t sysTime = { 0 };
        service_fittime_2_rtctime(fit_info.fit_time, &sysTime);
        
        // 处理12小时制
        int hour_12 = sysTime.tm_hour;
        const char* suffix = "";
        
        if (get_time_style() == TIMTSTYLE_TIME12)
        {
            suffix = sysTime.tm_hour >= 12 ? " PM" : " AM";
            hour_12 = sysTime.tm_hour >= 12 ? sysTime.tm_hour - 12 : sysTime.tm_hour;
            if (hour_12 == 0) hour_12 = 12;
        }
        
        // 根据日期格式设置日期部分，但不包含年份
        switch(get_real_date_style()) {
            case DATESTYLE_MDY:
            case DATESTYLE_YMD:
                // 月/日 格式
                if (get_time_style() == TIMTSTYLE_TIME12) {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d%s", sysTime.tm_mon + 1, sysTime.tm_mday, 
                            hour_12, sysTime.tm_min, suffix);
                } else {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d", sysTime.tm_mon + 1, sysTime.tm_mday, 
                            sysTime.tm_hour, sysTime.tm_min);
                }
                break;
                
            case DATESTYLE_DMY:
                // 日/月 格式
                if (get_time_style() == TIMTSTYLE_TIME12) {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d%s", sysTime.tm_mday, sysTime.tm_mon + 1  , 
                            hour_12, sysTime.tm_min, suffix);
                } else {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d", sysTime.tm_mday, sysTime.tm_mon + 1, 
                            sysTime.tm_hour, sysTime.tm_min);
                }
                break;
                
            default:
                // 默认使用月/日格式
                if (get_time_style() == TIMTSTYLE_TIME12) {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d%s", sysTime.tm_mon + 1, sysTime.tm_mday, 
                            hour_12, sysTime.tm_min, suffix);
                } else {
                    sprintf(fnamestr, "%02d/%02d %02d:%02d", sysTime.tm_mon + 1, sysTime.tm_mday, 
                            sysTime.tm_hour, sysTime.tm_min);
                }
                break;
        }

        card_name_.setTypedDynamicText(fnamestr);
        card_name_.resizeToCurrentTextWithAlignment();
        card_name_.setAlignTo(activity_icon_, ALIGN_OUT_RM, 2, - 2);

        sports_data_show_t temp_data;
        if (fit_info.show_key == DATATYPE_DIST_CUR)
        {
            get_dist_string(&temp_data, &fit_info.brief_data, get_unit_distance());
        }
        else
        {
            get_calories_string(&temp_data, &fit_info.brief_data, true);
        }

        fit_info_.setTextFont(&NUMBER_S_FONT);
        fit_info_.setTypedDynamicText(temp_data.data_full);
        fit_info_.resizeToCurrentTextWithAlignment();
        fit_info_.setAlignTo(activity_icon_, ALIGN_OUT_BL, 0, 12);
        
        fit_info_unit_.setVisible(true);
        fit_info_unit_.setTypedDynamicText(temp_data.unit);
        fit_info_unit_.resizeToCurrentTextWithAlignment();
        fit_info_unit_.setAlignTo(fit_info_, ALIGN_OUT_RB, 0, 6);
    }
    else
    {
        activity_icon_.setVisible(false);
        fit_info_unit_.setVisible(false);

        card_name_.setTypedDynamicText(_TM(g_history_card_text[0]));
        card_name_.resizeToCurrentTextWithAlignment();
        card_name_.setAlign(ALIGN_IN_LT, 144, 16);

        fit_info_.setTextFont(&PUBLIC_NO_32_R_FONT);
        fit_info_.setTypedDynamicText(_TM(g_history_card_text[1]));
        fit_info_.resizeToCurrentTextWithAlignment();
        fit_info_.setAlignTo(card_name_, ALIGN_OUT_BL, 0, 8);
        fit_info_.setAlpha(LV_OPA_80);
    }
}

void HistoryCard::quit(Container& base)
{
	base.removeAll();
}
