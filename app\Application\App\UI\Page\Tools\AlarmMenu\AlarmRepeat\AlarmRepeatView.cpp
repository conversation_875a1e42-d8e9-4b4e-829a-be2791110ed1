/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File    :   AlarmRepeatView.cpp
*@Time    :   2024/12/25 10:55:34
* 
**************************************************************************/

#include "AlarmRepeatView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "cfg_header_def.h"
#ifndef SIMULATOR
#include "alarm_manager.h"
#endif

typedef enum
{
    ALARM_REPEAT_ONCE,//仅一次
    ALARM_REPEAT_EVERYDAY,//每天
    ALARM_REPEAT_CUSTOM,//自定义
    ALARM_REPEAT_MAX,//最大
}ALARM_REPEAT_ITEM;

static const char* g_alarm_repeat_text[] =
{
	"_only_once",//仅一次
	"_daily",//每天
	"_custom",//自定义
};
TM_DECLARE(g_alarm_repeat_text)

static const char g_alarm_repeat_title[] = "_repeat";
TM_KEY(g_alarm_repeat_title)

static const char g_alarm_setting_text[] = "_time_settings";
TM_KEY(g_alarm_setting_text)

const qw_menu_info g_alarm_repeat_menu_info[] = {
	{(const int)ITEM_TYPES::ITEM_RADIO, g_alarm_repeat_text[ALARM_REPEAT_ONCE]},
	{(const int)ITEM_TYPES::ITEM_RADIO, g_alarm_repeat_text[ALARM_REPEAT_EVERYDAY]},
	{(const int)ITEM_TYPES::ITEM_TEXT, g_alarm_repeat_text[ALARM_REPEAT_CUSTOM]},
};

AlarmRepeatView::AlarmRepeatView(PageManager* manager) :
	QwMenuView(manager),
	p_time_stamp_(nullptr),p_flag_(nullptr),p_set_time_stamp_(nullptr),
	p_set_flag_(nullptr),p_cmd_(nullptr),p_set_cmd_(nullptr),p_set_id_(nullptr),
	p_id_(nullptr),p_item_select_(nullptr),p_set_item_select_(nullptr),
	on_set_time_stamp_(this, &AlarmRepeatView::on_click_set_time_stamp),
	on_set_flag_(this, &AlarmRepeatView::on_click_set_flag),
	on_set_id_(this, &AlarmRepeatView::on_click_set_id),
	on_set_cmd_(this, &AlarmRepeatView::on_click_set_cmd),
	on_set_item_select_(this, &AlarmRepeatView::on_click_set_item_select),
	update_time_stamp_(this, &AlarmRepeatView::update_time_stamp),
	update_flag_(this, &AlarmRepeatView::update_flag),
	select_item_pos_(this, &AlarmRepeatView::select_item_pos),
	update_id_(this, &AlarmRepeatView::update_id),
	update_cmd_(this, &AlarmRepeatView::update_cmd),
	times_input_create_(this, &AlarmRepeatView::times_input_create_item),
    times_input_confirm_(this, &AlarmRepeatView::times_input_confirm),
    times_input_cancel_(this, &AlarmRepeatView::times_input_cancel),
    times_input_12_create_(this, &AlarmRepeatView::times_input_12_create_item),
    times_input_12_confirm_(this, &AlarmRepeatView::times_input_12_confirm),
    times_input_12_cancel_(this, &AlarmRepeatView::times_input_12_cancel),
	update_item_select_(this, &AlarmRepeatView::update_item_select)
{

}

AlarmRepeatView::~AlarmRepeatView()
{

}

void AlarmRepeatView::setup()
{
	time_style_ = get_time_style();
	uint8_t focus_index = *p_item_select_->get_val(0);
	QwMenuView::show_menu(ALARM_REPEAT_MAX, focus_index, _TM(g_alarm_repeat_title));
}

void AlarmRepeatView::quit()
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void AlarmRepeatView::jump(const char* page_name)
{
	on_click_set_item_select(QwMenuView::list_.get_focus_item_index());
}

void AlarmRepeatView::handleTickEvent()
{
	if (times_input_.is_slide_in())
    {
        times_input_.handleTickEvent();
    }
    else if(times_input_12_.is_slide_in())
    {
        times_input_12_.handleTickEvent();
    }
	else
	{
		QwMenuView::list_.handleTickEvent();
	}
}

void AlarmRepeatView::handleKeyEvent(uint8_t c)
{
	if (times_input_.is_slide_in())
    {
        times_input_.handleKeyEvent(c);
    }
    else if(times_input_12_.is_slide_in())
    {
        times_input_12_.handleKeyEvent(c);
    }
    else
	{
		QwMenuView::list_.handleKeyEvent(c);
		if (c == KEY_CLK_START)
		{
			int select_index = QwMenuView::list_.get_focus_item_index();
			switch_to_app(select_index);
		}
		else if (c == KEY_CLK_BACK)
		{
			if (*p_cmd_->get_val(0) == ADD_ALARM_REPEAT)
			{
				if(time_style_ == TIMTSTYLE_TIME12)
				{
					show_times_12_input();
				}
				else
				{
					show_times_input();
				}
			}
			else
			{
				manager_->push("AlarmMenu");
			}
		}
	}
}

void AlarmRepeatView::handleClickEvent(const ClickEvent& evt)
{
	if (times_input_.is_slide_in())
    {
        times_input_.handleClickEvent(evt);
    }
    else if(times_input_12_.is_slide_in())
    {
        times_input_12_.handleClickEvent(evt);
    }
    else
    {
        QwMenuView::list_.handleClickEvent(evt);
    }
}

void AlarmRepeatView::handleDragEvent(const DragEvent& evt)
{
	if (times_input_.is_slide_in())
    {
        times_input_.handleDragEvent(evt);
    }
    else if(times_input_12_.is_slide_in())
    {
        times_input_12_.handleDragEvent(evt);
    }
	else
	{
		QwMenuView::list_.handleDragEvent(evt);
	}
}

void AlarmRepeatView::handleGestureEvent(const GestureEvent& evt)
{
	if (times_input_.is_slide_in())
    {
        times_input_.handleGestureEvent(evt);
    }
    else if(times_input_12_.is_slide_in())
    {
        times_input_12_.handleGestureEvent(evt);
    }
	else
	{
		QwMenuView::list_.handleGestureEvent(evt);

		if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL
			&& evt.getVelocity() > GESTURE_EXIT_ACCURACY)
		{
			if (*p_cmd_->get_val(0) == ADD_ALARM_REPEAT)
			{
				if(time_style_ == TIMTSTYLE_TIME12)
				{
					show_times_12_input();
				}
				else
				{
					show_times_input();
				}
			}
			else
			{
				manager_->push("AlarmMenu");
			}
		}
	}
}

// Notification Callback function
void AlarmRepeatView::set_on_set_time_stamp(Notification<uint32_t>* command)
{
	p_set_time_stamp_ = command;
}

void AlarmRepeatView::on_click_set_time_stamp(uint32_t t1)
{
	if (p_set_time_stamp_ != nullptr)
	{
		p_set_time_stamp_->notify(t1);
	}
}

void AlarmRepeatView::set_on_set_flag(Notification<uint8_t>* command)
{
	p_set_flag_ = command;
}

void AlarmRepeatView::on_click_set_flag(uint8_t t1)
{
	if (p_set_flag_ != nullptr)
	{
		p_set_flag_->notify(t1);
	}
}

void AlarmRepeatView::set_on_set_id(Notification<uint8_t>* command)
{
	p_set_id_ = command;
}

void AlarmRepeatView::on_click_set_id(uint8_t t1)
{
	if (p_set_id_ != nullptr)
	{
		p_set_id_->notify(t1);
	}
}

void AlarmRepeatView::set_on_set_cmd(Notification<uint8_t>* command)
{
	p_set_cmd_ = command;
}

void AlarmRepeatView::on_click_set_cmd(uint8_t t1)
{
	if (p_set_cmd_ != nullptr)
	{
		p_set_cmd_->notify(t1);
	}
}

void AlarmRepeatView::set_on_set_item_select(Notification<uint8_t>* command)
{
	p_set_item_select_ = command;
}

void AlarmRepeatView::on_click_set_item_select(uint8_t t1)
{
	if (p_set_item_select_ != nullptr)
	{
		p_set_item_select_->notify(t1);
	}
}

// ObserverDrawable Callback function
void AlarmRepeatView::set_update_time_stamp(ObserverDrawable<Drawable, uint32_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_time_stamp_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_time_stamp_);
	}
}

void AlarmRepeatView::update_time_stamp(Drawable* ctrl, Parameters<uint32_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void AlarmRepeatView::set_update_flag(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_flag_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_flag_);
	}
}

void AlarmRepeatView::update_flag(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{
        bool t = false;
		uint8_t select_index = ALARM_REPEAT_ONCE;
		switch (data->get_val())
		{
			case ONCE:
				select_index = ALARM_REPEAT_ONCE;
				break;
			
			case EVERYDAY:
				select_index = ALARM_REPEAT_EVERYDAY;
				break;
			default:
				select_index = ALARM_REPEAT_MAX;
				break;
		}
		for(uint8_t i = 0; i < ALARM_REPEAT_CUSTOM; i++)
		{
			if(select_index == i)
			{
				t = true;
				item_update_show(i, ITEM_UPDATE_TYPES::ITEM_UPDATE_RIGHT_IMAGE, &t);
			}
			else
			{
				t = false;
				item_update_show(i, ITEM_UPDATE_TYPES::ITEM_UPDATE_RIGHT_IMAGE, &t);
			}
		}
	}
}

void AlarmRepeatView::set_update_id(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_id_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_id_);
	}
}

void AlarmRepeatView::update_id(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

void AlarmRepeatView::set_update_cmd(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_cmd_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_cmd_);
	}
}

void AlarmRepeatView::update_cmd(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}
void AlarmRepeatView::set_update_item_select(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	if (observer != nullptr)
	{
		p_item_select_ = observer;
		observer->bind_ctrl(0, QwMenuView::list_);
		observer->bind_notify(update_item_select_);
	}
}

void AlarmRepeatView::update_item_select(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	if (ctrl != nullptr && data != nullptr)
	{

	}
}

// custom function
void AlarmRepeatView::set_item_info(item_info_t* info, int index)
{
	int select_index = 0;
	if (info == nullptr || index >= ALARM_REPEAT_MAX)
    {
    	assert(false && "[AlarmRepeatView:8001]set_item_info index is out");
		return;
    }

    info->item_index = index;
	info->type = (ITEM_TYPES)g_alarm_repeat_menu_info[index].type;
    memset(info->title, 0, sizeof(info->title));
    memcpy(info->title, _TM(g_alarm_repeat_menu_info[index].name), strlen(_TM(g_alarm_repeat_menu_info[index].name)));
	memset(info->subtitle, 0, sizeof(info->subtitle));


	switch (*p_flag_->get_val(0))
	{
		case ONCE:
			select_index = ALARM_REPEAT_ONCE;
			break;
		case EVERYDAY:
			select_index = ALARM_REPEAT_EVERYDAY;
			break;
		default:
			select_index = ALARM_REPEAT_MAX;
			break;
	}

	if (select_index == index)
	{
		info->is_selected = true;
	}
	
	info->item_text_info.text_align = 1;
}

void AlarmRepeatView::set_item_notify(QwMenuItem* item, int index)
{
	item->set_select_pos_handle(select_item_pos_);
}

void AlarmRepeatView::select_item_pos(void* item, int x, int y)
{
	if (item == nullptr)
	{
		assert("[AlarmRepeatView:8001] item is nullptr");
		return;
	}
	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	if (item_ == nullptr)
	{
		assert("[AlarmRepeatView:8001] item_ not is QwMenuItem");
		return;
	}
    // 6.获取子菜单在当前menu中的index
    int select_index = (int)item_->get_user_data();
	if (select_index < ALARM_REPEAT_CUSTOM)
	{
		if (x < 346 || x > 442)//超出范围
		{
			return;
		}
	}
	switch_to_app(select_index);
}

void AlarmRepeatView::switch_to_app(int select_index)
{
	bool t = false;
	uint32_t ret = 0;

	if (select_index >= ALARM_REPEAT_MAX)
	{
		assert(false && "[AlarmRepeatView:8001]switch_to_app index is out");
		return;
	}


	if (select_index == ALARM_REPEAT_CUSTOM)//自定义
	{
		alarm_clock_type_t alarm;
		memset(&alarm, 0, sizeof(alarm_clock_type_t));
		if (*p_cmd_->get_val(0) == ADD_ALARM_REPEAT)//添加闹钟
		{
			uint32_t trig_point = *p_time_stamp_->get_val(0);
			manager_->page_command("AlarmWeekly", 0, &trig_point);//添加闹钟
			manager_->push("AlarmWeekly");
		}
		else//编辑闹钟
		{
			alarm.id = *p_id_->get_val(0);
			alarm.trig_point = *p_time_stamp_->get_val(0);
			manager_->page_command("AlarmWeekly", 1, &alarm);//编辑闹钟
			manager_->push("AlarmWeekly");
		}
	}
	else
	{
		if(select_index == ALARM_REPEAT_ONCE)//单次
		{
			on_click_set_flag(ONCE);
		}
		else if (select_index == ALARM_REPEAT_EVERYDAY)//每天
		{
			on_click_set_flag(EVERYDAY);
		}
		if (*p_cmd_->get_val(0) == ADD_ALARM_REPEAT)//添加闹钟
		{
			uint8_t id = 0;
#ifndef SIMULATOR			
			id = alarm_manager_create(*p_time_stamp_->get_val(0), *p_flag_->get_val(0), alarm_callback);//
#endif			
			ret = add_alarm_to_config(*p_time_stamp_->get_val(0), *p_flag_->get_val(0), true, id);		
		}
		else//编辑闹钟
		{
			ret = set_alarm_repeat(*p_id_->get_val(0), *p_flag_->get_val(0));
			if (ret == 0)
			{
				// rt_kprintf("set alarm success\n");
						//修改闹钟重复
#ifndef SIMULATOR							
				uint8_t alarm_id = 0;
				alarm_id = get_alarm_id_by_index(*p_id_->get_val(0));
				alarm_manager_modify(alarm_id, *p_time_stamp_->get_val(0), *p_flag_->get_val(0), false);
#endif
			}
		}
		manager_->page_command("AlarmMenu", 0, &ret);
		manager_->push("AlarmMenu");
	}
}	

void AlarmRepeatView::show_times_input()
{
	times_input_.set_focus_section(1);  // 将焦点设置到分钟（索引1）
    times_input_.set_time_input_type(INPUT_TIME_DRAW);
    times_input_.set_time_input_title(_TM(g_alarm_setting_text));
    times_input_.set_time_default(*p_time_stamp_->get_val(0));
    times_input_.set_confirm_handle(times_input_confirm_);
    times_input_.set_cancel_handle(times_input_cancel_);
    times_input_.show_input(true, ANIMAT_TYPE::EAST, this->getRootContainer());
    times_input_.setVisible(true);
}

// 创建一个输入项
void AlarmRepeatView::times_input_create_item(Drawable *item, QwInput_item_info *info)
{
    // 如果item和info都不为空
    if (item != nullptr && info != nullptr)
    {
        // TODO: 添加代码
    }
}

void AlarmRepeatView::times_input_confirm()
{
    int value = times_input_.get_input_val();
	// on_click_set_time_stamp(value);
    manager_->push("AlarmRepeat");
}

void AlarmRepeatView::times_input_cancel()
{
    QwMenuView::list_.stop_list_animate();
    QwMenuView::list_.set_focus_to_item(QwMenuView::list_.get_focus_item_index(), false);
    times_input_.hide_input(true, ANIMAT_TYPE::WEST, this->getRootContainer());
	manager_->push("AlarmMenu");
}

void AlarmRepeatView::show_times_12_input()
{
	times_input_12_.set_focus_section(2);  // 将焦点设置到分钟（索引2）
    times_input_12_.set_time_input_type(INPUT_TIME_DRAW_12);
    times_input_12_.set_time_input_title(_TM(g_alarm_setting_text));
    times_input_12_.set_time_default(*p_time_stamp_->get_val(0));
    times_input_12_.set_confirm_handle(times_input_12_confirm_);
    times_input_12_.set_cancel_handle(times_input_12_cancel_);
    times_input_12_.show_input(true, ANIMAT_TYPE::EAST, this->getRootContainer());
    times_input_12_.setVisible(true);
}

void AlarmRepeatView::times_input_12_create_item(Drawable *item, QwInput_item_info *info)
{
    if (item != nullptr && info != nullptr)
    {
    }
}

void AlarmRepeatView::times_input_12_confirm()
{
    int value = times_input_12_.get_input_val();
	// on_click_set_time_stamp(value);
	manager_->push("AlarmRepeat");
}

void AlarmRepeatView::times_input_12_cancel()
{
    QwMenuView::list_.stop_list_animate();
    QwMenuView::list_.set_focus_to_item(QwMenuView::list_.get_focus_item_index(), false);
    times_input_12_.hide_input(true, ANIMAT_TYPE::WEST, this->getRootContainer());
	manager_->push("AlarmMenu");
}
