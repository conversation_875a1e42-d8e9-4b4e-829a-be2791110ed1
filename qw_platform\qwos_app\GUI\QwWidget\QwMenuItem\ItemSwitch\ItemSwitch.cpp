/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ItemSwitch.cpp
@Time    :   2024/12/10 19:27:42
* 
**************************************************************************/

#include "limits.h"
#include "stdint.h"
#include "ItemSwitch.h"

ItemSwitch::ItemSwitch() :
	index_(INT_MAX),
	focus_(false)
{};

void ItemSwitch::setup(void* info)
{
    if (info == NULL)
	{
		assert("[ItemSwitch:8001]setup info is nullptr");
		return;
	}

	item_info_t* item_info = static_cast<item_info_t*>(info);
	index_ = item_info->item_index;

	add(text_con_);
	text_con_.setWidthHeight(getWidth(), getHeight());

	text_con_.add(text_title_);
	text_con_.add(text_subtitle_);
	text_title_.setPosition(0, 0, MENU_TITLE_WIDTH, MENU_TITLE_HIGHT);
	text_title_.setTextFont(&MENU_TITLE_FONT);
	text_title_.setTextAlignment(LEFT);
	text_title_.setTextVerticalAlignment(ALIGN_Y_MID);
	text_title_.setLabelAlpha(LV_OPA_TRANSP);
	text_title_.setColor(lv_color_hex(MENU_TITLE_COLOR));
	text_title_.setAlpha(MENU_TITLE_COLOR_ALPHA);
	if (strlen(item_info->title) == NULL)
	{
		text_title_.setHeight(0);
	}
	else
	{
		text_title_.setTypedDynamicText(item_info->title);
	}

	text_subtitle_.setPosition(0, 0, MENU_TITLE_WIDTH, MENU_SUB_TEXT_HIGHT);
	text_subtitle_.setTextFont(&MENU_SUB_TEXT_FONT);
	text_subtitle_.setTextAlignment(LEFT);
	text_subtitle_.setLabelAlpha(LV_OPA_TRANSP);
	text_subtitle_.setColor(lv_color_hex(MENU_SUB_TEXT_COLOR));
	text_subtitle_.setVisible(true);
	if (strlen(item_info->subtitle) == NULL)
	{
		text_subtitle_.setHeight(0);
	}
	else
	{
		text_subtitle_.setTypedDynamicText(item_info->subtitle);
	}
	text_subtitle_.setAlpha(MENU_SUB_TEXT_COLOR_ALPHA);

	text_con_.setWidth(text_title_.getWidth() > text_subtitle_.getWidth() ? text_title_.getWidth() : text_subtitle_.getWidth());
	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight() + (text_subtitle_.getHeight() > 0 ? TITLE_TO_SUBTEXT_GAP : 0));
	text_con_.setAlign(ALIGN_IN_LM, LEFT_TO_TITLE_GAP);
	text_title_.setAlign(ALIGN_IN_LT);
	text_subtitle_.setAlign(ALIGN_IN_LB);

	text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.resizeHeightToCurrentText();
    text_title_.resizeHeightToCurrentText();


	add(switch_);
	switch_.setWidthHeight(64, 64);
    switch_.setAlign(ALIGN_IN_RM, IMAGE_TO_RIGHT_GAP);
    Bitmap bitmap_open(&toggle_button_open);
    Bitmap bitmap_close(&toggle_button_close);

	if (item_info->is_selected)
	{
        switch_.setBitmap(bitmap_open);
	}
	else
	{
        switch_.setBitmap(bitmap_close);
	}
	switch_.invalidate();
	//gfx_printf("focus_type_:%d\n", index_);
};

void ItemSwitch::quit()
{
    focus_update(false);
	text_con_.removeAll();
	removeAll();
};

void ItemSwitch::focus_update(bool focus)
{
	if (focus_ != focus)
	{
		focus_ = focus;
		if (focus_)
		{
			text_title_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
			text_subtitle_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
		}
		else
		{
			text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
			text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		}
        text_title_.resizeHeightToCurrentText();
        text_subtitle_.resizeHeightToCurrentText();
	}
};

void* ItemSwitch::get_user_data()
{
	return (void*)index_;
}

int ItemSwitch::set_type()
{
    return (int) CTRL_TYPE::TYPE_ONWER_DRAW;
}

void ItemSwitch::update_right_image(void* info)
{
	if (info == nullptr)
	{
		assert("[ItemSwitch:8001]update_right_image info is nullptr");
		return;
	}

	bool* state = static_cast<bool*>(info);
	if(*state)
    {
        Bitmap bitmap_open(&toggle_button_open);
        switch_.setBitmap(bitmap_open);
	}
	else
	{
        Bitmap bitmap_close(&toggle_button_close);
        switch_.setBitmap(bitmap_close);
	}
    switch_.invalidate();
}

void ItemSwitch::update_sub_title(void* info)
{
	if (info == nullptr)
	{
		assert("[ItemSwitch:8001]update_sub_title info is nullptr");
		return;
	}

	char* text = static_cast<char*>(info);
	if(strlen(text) == NULL)
	{
		text_subtitle_.setTypedDynamicText("");
		text_subtitle_.invalidate();
		text_subtitle_.setHeight(0);
	}
	else
	{
		text_subtitle_.setTypedDynamicText(text);
		text_subtitle_.invalidate();
		text_subtitle_.resizeHeightToCurrentText();
	}

	text_con_.setWidth(text_title_.getWidth() > text_subtitle_.getWidth() ? text_title_.getWidth() : text_subtitle_.getWidth());
	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight() + (text_subtitle_.getHeight() > 0 ? TITLE_TO_SUBTEXT_GAP : 0));
	text_con_.setAlign(ALIGN_IN_LM, LEFT_TO_TITLE_GAP);
	text_title_.setAlign(ALIGN_IN_LT);
	text_subtitle_.setAlign(ALIGN_IN_LB);

    if (focus_)
    {
        text_title_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
        text_subtitle_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
    }
    else
    {
        text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
        text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    }
    text_title_.resizeHeightToCurrentText();
    text_subtitle_.resizeHeightToCurrentText();
}

