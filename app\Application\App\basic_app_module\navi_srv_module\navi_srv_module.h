/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   navi_srv_module.h
@Time    :   2024/12/23 15:21:27
<AUTHOR>   nullptr
*
**************************************************************************/

#ifndef __NAVI_SRV_MODULE_
#define __NAVI_SRV_MODULE_
#include <stdbool.h>
#include <stdint.h>
#include <rtthread.h>
#include "fit_def.h"
#include "navi_file_manager.h"
#include "navi_port.h"
#include "fit_file_manager.h"
#include "qw_dev_cfg.h"

#ifdef __cplusplus
extern "C"
{
#endif

#define NAVI_SRV_ALT_DATA_MAX_COUNT 300
#define NAVI_SRV_WayPoint_DATA_MAX_COUNT 300

    //导航的准备状态：关闭、准备、导航中
    typedef enum
    {
        NAVI_PREPARE_STATUS_CLOSE = 0,
        NAVI_PREPARE_STATUS_READY,//导航方向设置页面点击正向反向之前
        NAVI_PREPARE_STATUS_RUNNING,//运动开始页面--导航结束页面
    } NAVI_PREPARE_STATUS;

    typedef enum
    {
        NAVI_ENTER_CARD,//应用卡片
        NAVI_ENTER_SPORT_STOP,//运动暂停页
        NAVI_ENTER_SPORT_SET,//运动启动页
        NAVI_ENTER_MAC,

    }NAVI_SRV_ENTER_WAY;

    typedef struct
    {
        bool had_preview;//是否预览过
        char file_name[128];//文件名
        char dis[20];//距离 含单位
        char alt[20];//高度 含单位
    }navi_last_preview_t;//导航最近一次预览的数据

    typedef struct
    {
        NAVI_PREPARE_STATUS status;
        NAVI_SRV_ENTER_WAY
            enter_navi_menus_mode;   //进入导航菜单页面的方式 0：应用卡片，1：运动暂停页
        uint8_t preview_file_index;//导航预览选中的文件index
        uint8_t preview_file_type;//导航预览选中的文件类型 0路线 1 记录
        navi_last_preview_t last_info;//导航最近一次预览过的文件的数据
    } navi_srv_t;

    //获取导航准备状态
    NAVI_PREPARE_STATUS navi_srv_get_prepare_status(void);
    //设置导航准备状态
    void navi_srv_set_prepare_status(NAVI_PREPARE_STATUS status);

    //导航状态前进
    //void navi_srv_advance_status(NAVI_PREPARE_STATUS status);

    //导航状态后退
    //void navi_srv_retreat_status(NAVI_PREPARE_STATUS status);

    //设置最近一次预览过的文件的数据
    void navi_srv_set_last_preview_info(navi_last_preview_t *info);

    //获取最近一次预览过的文件的数据
    bool navi_srv_get_last_preview_info(navi_last_preview_t *info);


    //设置进入导航菜单页面的方式（0：应用卡片，1：运动暂停页）
    void navi_srv_set_enter_mode(NAVI_SRV_ENTER_WAY mode);

    //获取进入导航菜单页面的方式（0：应用卡片，1：运动暂停页）
    NAVI_SRV_ENTER_WAY navi_srv_get_enter_mode(void);

    //设置导航预览选中的文件index，文件类型
    void navi_srv_set_preview_file_index(uint8_t index, uint8_t type);

    //获取导航预览选中的文件的index，文件类型
    void navi_srv_get_preview_file_index(uint8_t *index, uint8_t *type);

    //获取导航预览选中的文件的名称
    void navi_srv_get_preview_file_name(char *file_name);

    //获取导航预览选中的文件的名称（完整路径）
    void navi_srv_get_preview_file_path(char *file_path);

    //过滤文件的后缀名
    void navi_srv_filter_file_ext(char name[]);

    //转化高度的缓存数据到导航预览页中的高度数据 传出数据点（默认）
    void navi_srv_convert_height_data(float **data, uint32_t* count);

    //转化路点的缓存数据到导航预览页中的路点数据 传出数据点（默认）
    void navi_srv_convert_waypoint_data(record_simple_t** p_out, uint32_t *count);

    //更新转向提示信息，并调用弹窗
    /**
     * @brief 处理导航中的各种转向提示
     *
     * @param draw_data 导航进度
     * @note 这里仅作提示的判断与ui唤起，关于转向等状态的后续逻辑处理不关注
     */
    void navi_srv_update_turning_info(const navi_progress_t* draw_data);

    /**
     * @brief 导航开始与到达提醒
     *
     * @param start_or_arrive true:开始导航，false:到达目的地
     */
    void navi_srv_start_arrive_remind(bool start_or_arrive);
    //导航偏航与恢复提醒
    // void navi_srv_deviate_recover_remind(void);

    //导航爬升提醒 暂无需求
    void navi_srv_climb_remind(void);


    //判断某个运动类型是否为导航支持的运动
    bool navi_srv_is_support_sport_type(SPORTTYPE type);

#ifdef __cplusplus
}
#endif
#endif
