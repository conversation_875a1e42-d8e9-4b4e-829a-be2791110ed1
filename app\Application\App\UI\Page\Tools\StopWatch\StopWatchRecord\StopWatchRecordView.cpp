/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   StopWatchRecordView.cpp
@Time    :   2025-02-17 13:48:18
*
**************************************************************************/

#include "StopWatchRecordView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "GUI/QwGUIKey.h"
#include "Image/images.h"
#include "qw_time_util.h"
#include "stop_watch_app/stop_watch_app.h"
const char _top_text_[] = "_stopwatch";
TM_KEY(_top_text_)

constexpr auto ITEM_CARD_HEIGHT = 116;  //每个条目的高度

StopWatchRecordView::StopWatchRecordView(PageManager *manager)
    : PageView(manager)
    , p_sw_state_(nullptr)
    , p_sw_time_(nullptr)
    , p_sw_record_(nullptr)
    , p_sw_on_state_(nullptr)
    , p_sw_on_time_(nullptr)
    , p_sw_on_record_(nullptr)
    , update_sw_state_(this, &StopWatchRecordView::update_sw_state)
    , update_sw_time_(this, &StopWatchRecordView::update_sw_time)
    , update_sw_record_(this, &StopWatchRecordView::update_sw_record)
	, fab_click_evt_(this, &StopWatchRecordView::handleKeyEvent)
{}

StopWatchRecordView::~StopWatchRecordView()
{}

void StopWatchRecordView::setup()
{
    add(view_);
    view_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);

    view_.add(top_text);
    view_.add(time_text);
    view_.add(content_);
    view_.add(fab_);

    top_text.setColor(lv_color_white());
    top_text.setWidthHeight(258, 44);
    top_text.setTextFont(&PUBLIC_NO_32_R_FONT);
    top_text.setLabelAlpha(LV_OPA_TRANSP);
    top_text.setTypedDynamicText(_TM(_top_text_));
    top_text.setTextAlignment(CENTER);
    top_text.resizeToCurrentTextWithAlignment();
    top_text.setAlign(ALIGN_IN_TM, 0, 34);

    char str_[40] = {0};
    sprintf(str_, "00:00.00");
    time_text.setWidthHeight(466, 72);
    time_text.setTextFont(&NUMBER_NO_72_FONT);
    time_text.setTextAlignment(CENTER);
    time_text.setColor(lv_color_white());
    time_text.setLabelAlpha(LV_OPA_TRANSP);
    time_text.setTypedDynamicText(str_);
    time_text.resizeToCurrentTextWithAlignment();
    time_text.setAlign(ALIGN_IN_TM, 0, 78);

	fab_.set_fab_click_handle(fab_click_evt_);
    fab_.setup(FABTN_START::START);

    content_.setWidthHeight(466, 466 - 160);
    content_.enableHorizontalScroll(false);
    content_.enableVerticalScroll(true);
    content_.setScrollbarsVisible(true);
    content_.set_scroll_bar_enable(false);
	content_.setAlign(ALIGN_IN_TM, 0, 160);
}

void StopWatchRecordView::quit()
{}

void StopWatchRecordView::handleTickEvent()
{
	content_.handleTickEvent();
	fab_.handleTickEvent();
    if (stop_watch_is_full())
    {
        fab_.update(FABTN_START::START_DISABLE, FABTN_BACK::END);
    }
}

void StopWatchRecordView::handleKeyEvent(uint8_t c)
{
	content_.handleKeyEvent(c);
    uint8_t state = *p_sw_state_->get_val(0);
    if (state == 0)//IDLE
    {
        if(c == KEY_HOLD_POWER)
        {
            manager_->push("Launcher");
        }
        else if(c == KEY_HOLD_BACK)
        {
            manager_->push("ToolsMenu");
        }
    }
    else if (state == 1)   //RUN
    {
        if (c == KEY_CLK_START)
        {
            stop_watch_btn_start_pause();
        }
        else if (c == KEY_CLK_BACK)
        {
            stop_watch_btn_lap();//记录
        }
        else if(c == KEY_HOLD_POWER)
        {
            manager_->push("Launcher");
        }
        else if(c == KEY_HOLD_BACK)
        {
            manager_->push("ToolsMenu");
        }
    }
    else if (state == 2)//PAUSEs
    {
        if (c == KEY_CLK_START)
        {
            if (!stop_watch_is_full())
            {
                stop_watch_btn_start_pause();
            }
        }
        else if (c == KEY_CLK_BACK)
        {
            stop_watch_btn_reset();
            manager_->push("StopWatchShow");
        }
        else if(c == KEY_HOLD_POWER)
        {
            manager_->push("Launcher");
        }
        else if(c == KEY_HOLD_BACK)
        {
            manager_->push("ToolsMenu");
        }
    }
}

void StopWatchRecordView::handleClickEvent(const ClickEvent &evt)
{
	fab_.handleClickEvent(evt);
	content_.handleClickEvent(evt);
}

void StopWatchRecordView::handleDragEvent(const DragEvent &evt)
{
	content_.handleDragEvent(evt);
}

void StopWatchRecordView::handleGestureEvent(const GestureEvent &evt)
{
	content_.handleGestureEvent(evt);
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
    {
        manager_->push("ToolsMenu");
    }
}

// Notification Callback function
void StopWatchRecordView::set_on_sw_on_state(Notification<uint8_t> *command)
{
    p_sw_on_state_ = command;
}

void StopWatchRecordView::set_on_sw_on_time(
    Notification<StopWatchRecordModel::sw_time_t> *command)
{
    p_sw_on_time_ = command;
}

void StopWatchRecordView::set_on_sw_on_record(
    Notification<StopWatchRecordModel::sw_record_t> *command)
{
    p_sw_on_record_ = command;
}

// ObserverDrawable Callback function
void StopWatchRecordView::set_update_sw_state(
    ObserverDrawable<Drawable, uint8_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_sw_state_ = observer;
        observer->bind_ctrl(0, fab_);
        observer->bind_notify(update_sw_state_);
    }
}

void StopWatchRecordView::update_sw_state(Drawable *ctrl, Parameters<uint8_t> *data,
                                          int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        uint8_t state = data->get_val();
        if (state == 0)   //IDLE
        {
            fab_.update(FABTN_START::START);
        }
        else if (state == 1)   //RUN
        {
            if (!stop_watch_is_lap_enable())
            {
                fab_.update(FABTN_START::PAUSE, FABTN_BACK::DISABLE);
            }
            else
            {
                fab_.update(FABTN_START::PAUSE, FABTN_BACK::LAP);
            }
        }
        else if (state == 2)   //PAUSE 更新图标
        {
            if (!stop_watch_is_full())
            {
                fab_.update(FABTN_START::START, FABTN_BACK::END);
            }
        }
    }
}

void StopWatchRecordView::set_update_sw_time(
    ObserverDrawable<Drawable, StopWatchRecordModel::sw_time_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_sw_time_ = observer;
        observer->bind_ctrl(0, time_text);
        observer->bind_notify(update_sw_time_);
    }
}

void StopWatchRecordView::update_sw_time(Drawable *ctrl,
                                         Parameters<StopWatchRecordModel::sw_time_t> *data,
                                         int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        StopWatchRecordModel::sw_time_t time = data->get_val();
        uint8_t hour = time.hour;
        uint8_t minute = time.minute;
        uint8_t second = time.second;
        uint8_t millisecond = time.millisecond;
        char str_[40] = {0};
        if (hour == 0)
        {
            sprintf(str_, "%02d:%02d.%02d", minute, second, millisecond);
        }
        else
        {
            if(hour + minute + second + millisecond >= 4 * 99)
            {
                sprintf(str_, "99:99:99.99");
            }
            else
            {
                sprintf(str_, "%02d:%02d:%02d.%02d", hour, minute, second, millisecond);
            }
        }
        time_text.setTextAlignment(CENTER);
        time_text.setTypedDynamicText(str_);
        time_text.resizeToCurrentTextWithAlignment();
        time_text.invalidate();
    }
}

void StopWatchRecordView::set_update_sw_record(
    ObserverDrawable<Drawable, StopWatchRecordModel::sw_record_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_sw_record_ = observer;
        observer->bind_ctrl(0, top_text);
        observer->bind_notify(update_sw_record_);
    }
}

void StopWatchRecordView::update_sw_record(
    Drawable *ctrl, Parameters<StopWatchRecordModel::sw_record_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        //刷新list
		StopWatchRecordModel::sw_record_t record = data->get_val();
		update_list_content(record);
    }
}

void StopWatchRecordView::update_list_content(StopWatchRecordModel::sw_record_t record)
{
    uint8_t num = record.index;

   if (!stop_watch_is_lap_enable())
    {
        //置灰计圈按钮
        fab_.update(FABTN_START::PAUSE, FABTN_BACK::DISABLE);
    }

    for (int i = 0; i < num; i++)
    {
        content_.add(item[i]);
        item[i].setPosition(0, ITEM_CARD_HEIGHT * (num - i - 1), 466, ITEM_CARD_HEIGHT);

        item[i].add(bg_[i]);

        bg_[i].setPosition(0, 0, 466, ITEM_CARD_HEIGHT);
        bg_[i].setColor(lv_color_black());
        bg_[i].setAlpha(LV_OPA_TRANSP);

		char inx[4];
		sprintf(inx,"%02d",i + 1);

		char cur_time[30];
		uint32_t lap_tick_time = stop_watch_lap_tick_time_get(i);
		uint8_t h = lap_tick_time / 3600000;
		lap_tick_time %= 3600000;
		uint8_t m = lap_tick_time / 60000;
		lap_tick_time %= 60000;
		uint8_t s = lap_tick_time / 1000;
		lap_tick_time %= 1000;
		uint8_t ms = lap_tick_time / 10;
        if (h > 0)
        {
            sprintf(cur_time, "%02d:%02d:%02d.%02d",h, m, s,ms);
        }
        else
        {
            sprintf(cur_time, "%02d:%02d.%02d", m, s, ms);
        }

        //计算i与i-1的时间差
        char sub_time[40];
        uint32_t sub_time_2 = 0;
        if(i!=0)
        {
            sub_time_2 = stop_watch_lap_tick_time_get(i)
                       - stop_watch_lap_tick_time_get(i - 1);
        }
        else
        {
            sub_time_2 = stop_watch_lap_tick_time_get(i);
        }
        uint8_t h_2 = sub_time_2 / 3600000;
        sub_time_2 %= 3600000;
        uint8_t m_2 = sub_time_2 / 60000;
        sub_time_2 %= 60000;
        uint8_t s_2 = sub_time_2 / 1000;
        sub_time_2 %= 1000;
        uint8_t ms_2 = sub_time_2 / 10;
        if (h > 0)
        {
            sprintf(sub_time, "+%02d:%02d:%02d.%02d", h_2, m_2, s_2, ms_2);
        }
        else
        {
            sprintf(sub_time, "+%02d:%02d.%02d", m_2, s_2, ms_2);
        }

        item[i].add(t_inx[i]);
        item[i].add(t_cur[i]);
        item[i].add(t_sub[i]);

		t_inx[i].setTextFont(&NUMBER_NO_40_FONT);
		t_inx[i].setTextAlignment(CENTER);
		t_inx[i].setColor(lv_color_white());
		t_inx[i].setLabelAlpha(LV_OPA_TRANSP);
		t_inx[i].setTypedDynamicText(inx);
		t_inx[i].setAlign(ALIGN_IN_LM, 64, 0);

		t_cur[i].setTextFont(&NUMBER_NO_48_FONT);
        t_cur[i].setTextAlignment(LEFT);
		t_cur[i].setColor(lv_color_white());
		t_cur[i].setLabelAlpha(LV_OPA_TRANSP);
		t_cur[i].setTypedDynamicText(cur_time);
        t_cur[i].setAlign(ALIGN_IN_LT, 148, 15);

        t_sub[i].setTextFont(&NUMBER_NO_32_FONT);
        t_sub[i].setTextAlignment(LEFT);
		t_sub[i].setColor(lv_color_white());
		t_sub[i].setLabelAlpha(LV_OPA_TRANSP);
		t_sub[i].setTypedDynamicText(sub_time);
        t_sub[i].setAlign(ALIGN_IN_LT, 148, 67);
    }
    content_.invalidate();
	content_.setup(this->getRootContainer());


}

// custom function
