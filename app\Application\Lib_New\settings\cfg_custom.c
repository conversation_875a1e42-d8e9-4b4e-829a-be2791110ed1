/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   cfg_custom.c
@Time    :   2025/01/07 11:32:04
*
**************************************************************************/

#include "cfg_custom.h"
#include "cfg_log.h"
#include "qw_general.h"
#include "kvdb.h"
#include "igs_dev_config.h"
#include <string.h>

static qw_dev_cfg_t* p_cfg = NULL;

//默认APP类型
static const uint8_t g_apps_default_[] =
{
    APPTYPE_DAILY,					//每日活动
    APPTYPE_HRM,                    //心率
    APPTYPE_HISTORY,                //历史记录
    APPTYPE_TRAINING_STATUS,        //训练适应性
    APPTYPE_SLEEP,                  //睡眠
    APPTYPE_HRV,                    //HRV评估
    APPTYPE_RUNNING,                //跑步能力
    APPTYPE_CYCLING,                //骑行能力
    APPTYPE_SCHEDULES,              //训练计划
	APPTYPE_TRAINING_COURSES,       //训练课程
    APPTYPE_NAVIGATION,             //导航
    APPTYPE_HISTORY_ACH,            //个人成就
    APPTYPE_WEATHER,                //天气
    APPTYPE_ALTITUDE,               //高度计
    APPTYPE_NOTIFICATION,           //智能通知
    APPTYPE_SETTING,                //系统设置
    APPTYPE_STEP_COUNT,             //步数
    APPTYPE_CALORY,                 //卡路里
    APPTYPE_BLOOD_OXYGEN,			//血氧饱和度
    APPTYPE_PRESSURE,				//压力
    APPTYPE_INTENSE_DURATION,		//强度活动时长
    APPTYPE_DURATION,				//活动小时数
    APPTYPE_COMPASS,				//电子罗盘
    APPTYPE_BAROMETER,              //气压计
};

//默认运动类型
static const uint8_t g_sports_default_[] =
{
	SPORTSTYPE_RUNNING,             //跑步
	SPORTSTYPE_TREADMILL,           //跑步机
	SPORTSTYPE_PLAYGROUND,          //操场跑步

    SPORTSTYPE_CYCLING,             //骑行
    SPORTSTYPE_INDOOR_CYCLING,      //室内骑行

	SPORTSTYPE_POOL_SWIMMING,       //泳池游泳
	SPORTSTYPE_OPEN_WATER_SWIMMING, //公共水域游泳

	SPORTSTYPE_STRENGTH_TRAINING,   //力量训练
	SPORTSTYPE_ELLIPTICAL_MACHINE,  //椭圆机

    SPORTSTYPE_HIKING,              //徒步

	SPORTSTYPE_JUMP_ROPE,           //跳绳
	SPORTSTYPE_TRIATHLON,           //铁人三项
    SPORTSTYPE_EXERCISE,            //锻炼


	SPORTSTYPE_TRAIL_RUNNING,      	//越野跑
	SPORTSTYPE_WALKING,             //步行
	SPORTSTYPE_INDOOR_RUNNING,      //室内跑步

	// SPORTSTYPE_MOUNTAIN_CYCLING,    //山地骑行
	SPORTSTYPE_COMMUTING,    		//骑车通勤
	SPORTSTYPE_TRIP_CYCLING, 		//长途骑行

	
	SPORTSTYPE_INDOOR_AEROBIC,      //室内有氧
	
	SPORTSTYPE_ROWING_MACHINE,      //划船机

	SPORTSTYPE_MOUNTAINEERING,      //登山
	
	SPORTSTYPE_SKIING,              //滑雪
	SPORTSTYPE_OUTDOOR_AEROBIC,     //户外有氧


	// SPORTSTYPE_COMPOUND_MOTION,     //复合运动
};

//默认APP类型
static const uint8_t g_tools_default_[] =
{
	CONSOLETYPE_FOCUS_MODE,         //勿扰模式
	CONSOLETYPE_SLEEP_MODE,         //睡眠模式
	CONSOLETYPE_LOCK,               //触屏锁定
	CONSOLETYPE_LOCKALL,            //手表锁定
	CONSOLETYPE_BRIGHTNESS,         //亮度
	CONSOLETYPE_SENSOR,             //传感器
	CONSOLETYPE_CAMERA,             //相机控制
	CONSOLETYPE_STOP_WATCH,         //秒表
	CONSOLETYPE_TIMER,              //计时器
	CONSOLETYPE_ALARMCLOCK,         //闹钟
	CONSOLETYPE_FLASHLIGHT,         //手电筒
	CONSOLETYPE_FIND_MY_PHONE,      //查找手机
	CONSOLETYPE_HRM,                //心率推送
	CONSOLETYPE_MUSIC,              //音乐控制
	CONSOLETYPE_ALIPAY,             //支付宝
	CONSOLETYPE_RAISE_AWAKE,        //抬腕亮屏
	CONSOLETYPE_ALWAYS_ON,          //常亮模式
	CONSOLETYPE_BATTERY_SAVE,       //省电模式
	CONSOLETYPE_SETTING,            //系统设置
	CONSOLETYPE_METRONOME,          //节拍器
	CONSOLETYPE_BREATH_TRAINING,    //呼吸训练
};

static bool cfg_custom_encode(uint8_t* encode_data, uint32_t encode_data_len,
	uint8_t* encode_buf, uint32_t encode_buf_size, uint32_t* encode_ret_size)
{
    memcpy(encode_buf, encode_data, encode_data_len);
	*encode_ret_size = encode_data_len;
	return true;
}

static bool check_cfg_custom_menu(cfg_menu_t* list, uint8_t cnt, uint8_t max)
{
    bool ret = true;

    if (list == NULL || cnt > CFG_CUSTOM_RSV_NUM || cnt == 0)
    {
        ret = false;
    }

    for (int i = 0; i < cnt; i++)
    {
        if (list[i].type > max)
        {
            ret = false;
            break;
        }
    }

    return ret;
}

static bool cfg_custom_decode(uint8_t* decode_data, uint32_t decode_data_len,
	uint8_t* decode_buf, uint32_t decode_buf_len)
{
    cfg_custom_t temp = {0};
    memcpy(&temp, decode_data, decode_data_len > sizeof(cfg_custom_t) ? sizeof(cfg_custom_t) : decode_data_len);
    // 参数校验
    // typedef struct
    // {
    //     uint8_t apps_count;
    //     uint8_t sports_count;
    //     uint8_t tools_count;

    //     cfg_menu_t apps_custom[CFG_CUSTOM_RSV_NUM];
    //     cfg_menu_t sports_custom[CFG_CUSTOM_RSV_NUM];
    //     cfg_menu_t tools_custom[CFG_CUSTOM_RSV_NUM];
    // } cfg_custom_t;

    if ((p_cfg->firmware_update.cfg_version != SYS_CONFIG_VERSION)
        || (decode_data_len != sizeof(cfg_custom_t)))
    {
        cfg_custom_default(&temp);
    }
    else
    {
        if (!check_cfg_custom_menu(temp.apps_custom, CFG_CUSTOM_RSV_NUM, APPTYPE__MAX))
        {
            // 重置为默认配置
            int index = 0;
            // 首先按照默认数组的顺序填充默认启用的类型
            for (int i = 0; i < sizeof(g_apps_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
            {
                temp.apps_custom[index].type = g_apps_default_[i];
                temp.apps_custom[index].status = 1; // 默认启用
                index++;
            }
            temp.apps_count = index; // 更新count为启用的数量
            
            // 填充剩余未包含在默认数组中的类型
            for (int type = 0; type < APPTYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
            {
                // 检查该类型是否已经在默认数组中
                bool already_added = false;
                for (int j = 0; j < sizeof(g_apps_default_) / sizeof(uint8_t); j++)
                {
                    if (g_apps_default_[j] == type)
                    {
                        already_added = true;
                        break;
                    }
                }
                
                // 如果未添加，则添加到列表中
                if (!already_added)
                {
                    temp.apps_custom[index].type = type;
                    temp.apps_custom[index].status = 0; // 默认禁用
                    index++;
                }
            }
        }
        else
        {
            // 如果数据有效，更新apps_count为启用的条目数量
            int active_count = 0;
            for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                if (temp.apps_custom[i].type < APPTYPE__MAX && temp.apps_custom[i].status == 1)
                {
                    active_count++;
                }
            }
            temp.apps_count = active_count;
        }
        
        if (!check_cfg_custom_menu(temp.sports_custom, CFG_CUSTOM_RSV_NUM, SPORTSTYPE__MAX))
        {
            // 重置为默认配置
            int index = 0;
            // 首先按照默认数组的顺序填充默认启用的类型
            for (int i = 0; i < sizeof(g_sports_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
            {
                temp.sports_custom[index].type = g_sports_default_[i];
                temp.sports_custom[index].status = 1; // 默认启用
                index++;
            }
            temp.sports_count = index; // 更新count为启用的数量
            
            // 填充剩余未包含在默认数组中的类型
            for (int type = 0; type < SPORTSTYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
            {
                // 检查该类型是否已经在默认数组中
                bool already_added = false;
                for (int j = 0; j < sizeof(g_sports_default_) / sizeof(uint8_t); j++)
                {
                    if (g_sports_default_[j] == type)
                    {
                        already_added = true;
                        break;
                    }
                }
                
                // 如果未添加，则添加到列表中
                if (!already_added)
                {
                    temp.sports_custom[index].type = type;
                    temp.sports_custom[index].status = 0; // 默认禁用
                    index++;
                }
            }
        }
        else
        {
            // 如果数据有效，更新sports_count为启用的条目数量
            int active_count = 0;
            for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                if (temp.sports_custom[i].type < SPORTSTYPE__MAX && temp.sports_custom[i].status == 1)
                {
                    active_count++;
                }
            }
            temp.sports_count = active_count;
        }
        
        if (!check_cfg_custom_menu(temp.tools_custom, CFG_CUSTOM_RSV_NUM, CONSOLETYPE__MAX))
        {
            // 重置为默认配置
            int index = 0;
            // 首先按照默认数组的顺序填充默认启用的类型
            for (int i = 0; i < sizeof(g_tools_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
            {
                temp.tools_custom[index].type = g_tools_default_[i];
                temp.tools_custom[index].status = 1; // 默认启用
                index++;
            }
            temp.tools_count = index; // 更新count为启用的数量
            
            // 填充剩余未包含在默认数组中的类型
            for (int type = 0; type < CONSOLETYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
            {
                // 检查该类型是否已经在默认数组中
                bool already_added = false;
                for (int j = 0; j < sizeof(g_tools_default_) / sizeof(uint8_t); j++)
                {
                    if (g_tools_default_[j] == type)
                    {
                        already_added = true;
                        break;
                    }
                }
                
                // 如果未添加，则添加到列表中
                if (!already_added)
                {
                    temp.tools_custom[index].type = type;
                    temp.tools_custom[index].status = 0; // 默认禁用
                    index++;
                }
            }
        }
        else
        {
            // 如果数据有效，更新tools_count为启用的条目数量
            int active_count = 0;
            for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                if (temp.tools_custom[i].type < CONSOLETYPE__MAX && temp.tools_custom[i].status == 1)
                {
                    active_count++;
                }
            }
            temp.tools_count = active_count;
        }
    }

    memcpy(decode_buf, &temp, sizeof(cfg_custom_t));
    return true;
}

static bool cfg_custom_default_config(void* value, uint32_t value_size)
{
    if (value == NULL || value_size == 0) {
        return false;
    }
    cfg_custom_default(&p_cfg->custom);
    memcpy(value, s_cfg_store_table[enum_cfg_custom].dat, value_size);
    return true;
}

static const kv_node_config cfg_custom_config = {
    .encode = cfg_custom_encode,
    .decode = cfg_custom_decode,
    .default_config = cfg_custom_default_config,
};

static int cfg_custom_init(void* handler)
{
    p_cfg = get_cfg();

    void* data_ptr = NULL;
    const char* key = s_cfg_store_table[enum_cfg_custom].name;
    void *data_addr = s_cfg_store_table[enum_cfg_custom].dat;
    uint32_t size = s_cfg_store_table[enum_cfg_custom].len * s_cfg_store_table[enum_cfg_custom].num;

    kvlist_node_open(handler, key, &cfg_custom_config, size, data_addr);
    return 0;
}
INIT_FS_KV_EXPORT(cfg_custom_init);

/**
 * @brief 初始化
 * @param cfg 参数指针
 */
void cfg_custom_default(cfg_custom_t* cfg)
{
    if (cfg != NULL)
    {
        memset(cfg, 0, sizeof(cfg_custom_t));

        // 初始化所有可能的应用类型
        int index = 0;
        
        // 首先按照默认数组的顺序填充默认启用的类型
        for (int i = 0; i < sizeof(g_apps_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
        {
            cfg->apps_custom[index].type = g_apps_default_[i];
            cfg->apps_custom[index].status = 1; // 默认启用
            index++;
        }
        cfg->apps_count = index; // apps_count表示status为1的条目数量
        
        // 然后填充剩余未包含在默认数组中的类型
        for (int type = 0; type < APPTYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
        {
            // 检查该类型是否已经在默认数组中
            bool already_added = false;
            for (int j = 0; j < sizeof(g_apps_default_) / sizeof(uint8_t); j++)
            {
                if (g_apps_default_[j] == type)
                {
                    already_added = true;
                    break;
                }
            }
            
            // 如果未添加，则添加到列表中
            if (!already_added)
            {
                cfg->apps_custom[index].type = type;
                cfg->apps_custom[index].status = 0; // 默认禁用
                index++;
            }
        }

        // 将剩余位置填充为APPTYPE__MAX
        while (index < CFG_CUSTOM_RSV_NUM)
        {
            cfg->apps_custom[index].type = APPTYPE__MAX;
            cfg->apps_custom[index].status = 0;
            index++;
        }

        // 初始化所有可能的运动类型
        index = 0;
        
        // 首先按照默认数组的顺序填充默认启用的类型
        for (int i = 0; i < sizeof(g_sports_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
        {
            cfg->sports_custom[index].type = g_sports_default_[i];
            cfg->sports_custom[index].status = 1; // 默认启用
            index++;
        }
        cfg->sports_count = index; // sports_count表示status为1的条目数量
        
        // 然后填充剩余未包含在默认数组中的类型
        for (int type = 0; type < SPORTSTYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
        {
            // 检查该类型是否已经在默认数组中
            bool already_added = false;
            for (int j = 0; j < sizeof(g_sports_default_) / sizeof(uint8_t); j++)
            {
                if (g_sports_default_[j] == type)
                {
                    already_added = true;
                    break;
                }
            }
            
            // 如果未添加，则添加到列表中
            if (!already_added)
            {
                cfg->sports_custom[index].type = type;
                cfg->sports_custom[index].status = 0; // 默认禁用
                index++;
            }
        }

        // 将剩余位置填充为SPORTSTYPE__MAX
        while (index < CFG_CUSTOM_RSV_NUM)
        {
            cfg->sports_custom[index].type = SPORTSTYPE__MAX;
            cfg->sports_custom[index].status = 0;
            index++;
        }

        // 初始化所有可能的工具类型
        index = 0;
        
        // 首先按照默认数组的顺序填充默认启用的类型
        for (int i = 0; i < sizeof(g_tools_default_) / sizeof(uint8_t) && index < CFG_CUSTOM_RSV_NUM; i++)
        {
            cfg->tools_custom[index].type = g_tools_default_[i];
            cfg->tools_custom[index].status = 1; // 默认启用
            index++;
        }
        cfg->tools_count = index; // tools_count表示status为1的条目数量
        
        // 然后填充剩余未包含在默认数组中的类型
        for (int type = 0; type < CONSOLETYPE__MAX && index < CFG_CUSTOM_RSV_NUM; type++)
        {
            // 检查该类型是否已经在默认数组中
            bool already_added = false;
            for (int j = 0; j < sizeof(g_tools_default_) / sizeof(uint8_t); j++)
            {
                if (g_tools_default_[j] == type)
                {
                    already_added = true;
                    break;
                }
            }
            
            // 如果未添加，则添加到列表中
            if (!already_added)
            {
                cfg->tools_custom[index].type = type;
                cfg->tools_custom[index].status = 0; // 默认禁用
                index++;
            }
        }

        // 将剩余位置填充为CONSOLETYPE__MAX
        while (index < CFG_CUSTOM_RSV_NUM)
        {
            cfg->tools_custom[index].type = CONSOLETYPE__MAX;
            cfg->tools_custom[index].status = 0;
            index++;
        }

        CFG_SETTING_LOG_I("cfg_custom_t reset default, version:%d->%d",
            p_cfg->firmware_update.cfg_version, SYS_CONFIG_VERSION);
    }
}

/**
 * @brief 获取app卡片个数
 * @return app卡片个数
 */
uint8_t get_custom_app_count(void)
{
    // 返回status为1（启用）的条目数量
    return p_cfg->custom.apps_count;
}

/**
 * @brief 设置app卡片个数
 * @param cnt app卡片个数 小于CFG_CUSTOM_RSV_NUM
 * @param list app卡片列表 个数为cnt
 */
void set_custom_apps(uint8_t cnt, cfg_menu_t* list)
{
    if (list == NULL || cnt > CFG_CUSTOM_RSV_NUM || cnt == 0)
    {
        return;
    }

    for (int i = 0; i < cnt; i++)
    {
        if (list[i].type >= APPTYPE__MAX)
        {
            return;
        }
    }

    // 清空所有现有配置
    memset(p_cfg->custom.apps_custom, 0, sizeof(cfg_menu_t) * CFG_CUSTOM_RSV_NUM);
    
    // 直接复制传入的配置
    memcpy(p_cfg->custom.apps_custom, list, sizeof(cfg_menu_t) * cnt);
    
    // 剩余的部分填充为无效类型
    for (int i = cnt; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        p_cfg->custom.apps_custom[i].type = APPTYPE__MAX;
        p_cfg->custom.apps_custom[i].status = 0;
    }

    // 计算启用的条目数量
    int active_count = 0;
    for (int i = 0; i < cnt; i++)
    {
        if (list[i].status == 1)
        {
            active_count++;
        }
    }
    
    p_cfg->custom.apps_count = active_count;
}

/**
 * @brief 获取app卡片
 * @return app卡片
 */
APPTYPE get_custom_app_context(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.apps_custom[i].type < APPTYPE__MAX && p_cfg->custom.apps_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.apps_custom[i].type;
            }
            active_count++;
        }
    }
    
    return APPTYPE__MAX;
}

/**
 * @brief 获取app卡片状态
 * @return app卡片状态
 */
uint8_t get_custom_app_status(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.apps_custom[i].type < APPTYPE__MAX && p_cfg->custom.apps_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.apps_custom[i].status;
            }
            active_count++;
        }
    }
    
    return 0;
}

/**
 * @brief 获取运动种类个数
 * @return 运动种类个数
 */
uint8_t get_custom_sports_count(void)
{
    // 返回status为1（启用）的条目数量
    return p_cfg->custom.sports_count;
}

/**
 * @brief 设置运动种类个数
 * @param cnt 运动种类个数 小于CFG_CUSTOM_RSV_NUM
 * @param list 运动种类列表 个数为cnt
 */
void set_custom_sports(uint8_t cnt, cfg_menu_t* list)
{
    if (list == NULL || cnt > CFG_CUSTOM_RSV_NUM || cnt == 0)
    {
        return;
    }

    for (int i = 0; i < cnt; i++)
    {
        if (list[i].type >= SPORTSTYPE__MAX)
        {
            return;
        }
    }

    // 清空所有现有配置
    memset(p_cfg->custom.sports_custom, 0, sizeof(cfg_menu_t) * CFG_CUSTOM_RSV_NUM);
    
    // 直接复制传入的配置
    memcpy(p_cfg->custom.sports_custom, list, sizeof(cfg_menu_t) * cnt);
    
    // 剩余的部分填充为无效类型
    for (int i = cnt; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        p_cfg->custom.sports_custom[i].type = SPORTSTYPE__MAX;
        p_cfg->custom.sports_custom[i].status = 0;
    }

    // 计算启用的条目数量
    int active_count = 0;
    for (int i = 0; i < cnt; i++)
    {
        if (list[i].status == 1)
        {
            active_count++;
        }
    }
    
    p_cfg->custom.sports_count = active_count;
}

/**
 * @brief 获取运动种类个数
 * @return 运动种类个数
 */
SPORTTYPE get_custom_sports_context(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.sports_custom[i].type < SPORTSTYPE__MAX && p_cfg->custom.sports_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.sports_custom[i].type;
            }
            active_count++;
        }
    }
    
    return SPORTSTYPE__MAX;
}

/**
 * @brief 获取运动种类状态
 * @return 运动种类状态
 */
uint8_t get_custom_sports_status(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.sports_custom[i].type < SPORTSTYPE__MAX && p_cfg->custom.sports_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.sports_custom[i].status;
            }
            active_count++;
        }
    }
    
    return 0;
}

/**
 * @brief 获取工具中心种类个数
 * @return 工具中心种类个数
 */
uint8_t get_custom_tools_count(void)
{
    // 返回status为1（启用）的条目数量
    return p_cfg->custom.tools_count;
}

/**
 * @brief 设置工具中心种类个数
 * @param cnt 工具中心种类个数 小于CFG_CUSTOM_RSV_NUM
 * @param list 工具中心种类列表 个数为cnt
 */
void set_custom_tools(uint8_t cnt, cfg_menu_t* list)
{
    if (list == NULL || cnt > CFG_CUSTOM_RSV_NUM || cnt == 0)
    {
        return;
    }

    for (int i = 0; i < cnt; i++)
    {
        if (list[i].type >= CONSOLETYPE__MAX)
        {
            return;
        }
    }

    // 清空所有现有配置
    memset(p_cfg->custom.tools_custom, 0, sizeof(cfg_menu_t) * CFG_CUSTOM_RSV_NUM);
    
    // 直接复制传入的配置
    memcpy(p_cfg->custom.tools_custom, list, sizeof(cfg_menu_t) * cnt);
    
    // 剩余的部分填充为无效类型
    for (int i = cnt; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        p_cfg->custom.tools_custom[i].type = CONSOLETYPE__MAX;
        p_cfg->custom.tools_custom[i].status = 0;
    }

    // 计算启用的条目数量
    int active_count = 0;
    for (int i = 0; i < cnt; i++)
    {
        if (list[i].status == 1)
        {
            active_count++;
        }
    }
    
    p_cfg->custom.tools_count = active_count;
}

/**
 * @brief 获取工具中心种类个数
 * @return 工具中心种类个数
 */
CONSOLETYPE get_custom_tools_context(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.tools_custom[i].type < CONSOLETYPE__MAX && p_cfg->custom.tools_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.tools_custom[i].type;
            }
            active_count++;
        }
    }
    
    return CONSOLETYPE__MAX;
}

/**
 * @brief 获取工具中心种类状态
 * @return 工具中心种类状态
 */
uint8_t get_custom_tools_status(uint8_t idx)
{
    // 查找第idx个启用状态为1的条目
    int active_count = 0;
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.tools_custom[i].type < CONSOLETYPE__MAX && p_cfg->custom.tools_custom[i].status == 1)
        {
            if (active_count == idx)
            {
                return p_cfg->custom.tools_custom[i].status;
            }
            active_count++;
        }
    }
    
    return 0;
}

/**
 * @brief 检查应用菜单项是否有效
 * @param idx 菜单项索引
 * @param type 返回菜单项类型
 * @param status 返回菜单项状态
 * @return 如果有效返回true，否则返回false
 */
bool get_custom_app_item(uint8_t idx, uint8_t* type, uint8_t* status)
{
    if (idx >= CFG_CUSTOM_RSV_NUM || !type || !status)
    {
        return false;
    }
    
    if (p_cfg->custom.apps_custom[idx].type < APPTYPE__MAX)
    {
        *type = p_cfg->custom.apps_custom[idx].type;
        *status = p_cfg->custom.apps_custom[idx].status;
        return true;
    }
    
    return false;
}

/**
 * @brief 检查运动菜单项是否有效
 * @param idx 菜单项索引
 * @param type 返回菜单项类型
 * @param status 返回菜单项状态
 * @return 如果有效返回true，否则返回false
 */
bool get_custom_sports_item(uint8_t idx, uint8_t* type, uint8_t* status)
{
    if (idx >= CFG_CUSTOM_RSV_NUM || !type || !status)
    {
        return false;
    }
    
    if (p_cfg->custom.sports_custom[idx].type < SPORTSTYPE__MAX)
    {
        *type = p_cfg->custom.sports_custom[idx].type;
        *status = p_cfg->custom.sports_custom[idx].status;
        return true;
    }
    
    return false;
}

/**
 * @brief 检查工具菜单项是否有效
 * @param idx 菜单项索引
 * @param type 返回菜单项类型
 * @param status 返回菜单项状态
 * @return 如果有效返回true，否则返回false
 */
bool get_custom_tools_item(uint8_t idx, uint8_t* type, uint8_t* status)
{
    if (idx >= CFG_CUSTOM_RSV_NUM || !type || !status)
    {
        return false;
    }
    
    if (p_cfg->custom.tools_custom[idx].type < CONSOLETYPE__MAX)
    {
        *type = p_cfg->custom.tools_custom[idx].type;
        *status = p_cfg->custom.tools_custom[idx].status;
        return true;
    }
    
    return false;
}

/**
 * @brief 检查指定的应用类型是否启用
 * @param app_type 应用类型 (APPTYPE)
 * @return 如果启用返回true，否则返回false
 */
bool is_custom_app_enabled(APPTYPE app_type)
{
    if (p_cfg == NULL || app_type >= APPTYPE__MAX)
    {
        return false;
    }
    
    // 遍历apps_custom数组，查找匹配的类型
    for (int i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
    {
        if (p_cfg->custom.apps_custom[i].type == app_type)
        {
            // 找到匹配的类型，返回其状态
            return (p_cfg->custom.apps_custom[i].status == 1);
        }
    }
    
    // 未找到匹配的类型，默认返回false
    return false;
}

