/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   battery_srv.c
@Time    :   2024/12/27 16:12:00
*
**************************************************************************/

#include "battery_srv.h"
#include "backlight_module/backlight_module.h"
#include "gui_event_service.h"
#include "igs_global.h"
#include "system_event_service.h"
#ifdef IGS_DEV
#include "qw_system_params.h"
#include "qw_sensor.h"
#include "qw_sensor_common_config.h"
#include "qw_time_service.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "factory_pb.h"
#endif
#include "cfg_header_def.h"
#include "qw_log.h"
#include "qw_timer.h"


#define BATTERY_LVL LOG_LVL_DBG
#define BATTERY_TAG "BATTERY"

#if (BATTERY_LVL >= LOG_LVL_DBG) && !defined(SIMULATOR)
#define BATTERY_LOG_D(...) QW_LOG_D(BATTERY_TAG, __VA_ARGS__)
#else
#define BATTERY_LOG_D(...)
#endif

struct battery_srv_param_t
{
    uint8_t battery_level;
    bool battery_status;
    bool had_pop_level_10;   //在本充电周期内是否弹出过10%电量提醒
    bool had_pop_level_20;   //在本充电周期内是否弹出过20%电量提醒
};

typedef enum
{
    BATTERY_SRY_RUN_MANUAL,//手动模式
    BATTERY_SRY_RUN_BALLBACK,//回调模式
}battery_srv_run_type;

static battery_srv_run_type battery_srv_run_state = BATTERY_SRY_RUN_MANUAL;

struct battery_srv_param_t battery_srv_param = {100, false, false, false};

static void battery_level_change_evt(void);
static void battery_state_change_evt(void);
static void battery_level_remind_no_charge(void);

static void battery_state_change_evt(void)
{
    bool new_state = battery_get_status();
    //充电状态改变提醒
    if (new_state || battery_srv_run_state == BATTERY_SRY_RUN_MANUAL)
    {
        battery_srv_param.had_pop_level_10 = false;
        battery_srv_param.had_pop_level_20 = false;
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CHARGE_REMINDER,NULL);
    }
    else if (!new_state)
    {
        //息屏时，拔掉充电器，应该亮屏
        set_backlight_msg_on(false);   // 调整强制背光
        backlight_open_app();
        //拔掉充电器后，重新走一遍电量提醒流程
        battery_level_remind_no_charge();
    }
}

//拔掉充电器后，重新走一遍电量提醒流程
static void battery_level_remind_no_charge(void)
{
    uint8_t level = battery_get_level();
    if (level <= 2)
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_OFF, NULL);
    }
    else if (level <= 10)
    {
        if (get_power_save_setting() == POWER_SAVE_NO)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_TEN, NULL);
            battery_srv_param.had_pop_level_10 = true;
        }
    }
    else if (level <= 20)
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_TWENTY, NULL);
        battery_srv_param.had_pop_level_20 = true;
    }
}

static void battery_level_change_evt(void)
{
    uint8_t new_level = battery_get_level();
    //关机倒计时提醒弹窗
    if (new_level <= 2 && !battery_get_status())
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_OFF, NULL);
    }
    //超低电量提醒弹窗：是否进入省电模式 充电前只弹出一次
    else if (new_level <= 10 && !battery_srv_param.had_pop_level_10
             && !battery_get_status())
    {
        if (get_power_save_setting() == POWER_SAVE_NO)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_TEN, NULL);
            battery_srv_param.had_pop_level_10 = true;
        }
    }
    //低电量提醒弹窗：低电量 充电前只弹出一次
    else if (new_level <= 20 && !battery_srv_param.had_pop_level_20
             && !battery_get_status())
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNDER_VOLTAGE_TWENTY, NULL);
        battery_srv_param.had_pop_level_20 = true;
    }
    //满电、息屏、充电中  自动亮屏，然后弹出充电提醒
    else if (new_level == 100 && battery_get_status()
             && (get_backlight_status() != BK_STATUS_ON))
    {
        //唤醒屏幕、弹出充电提醒
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CHARGE_REMINDER, NULL);
    }
}

#ifdef IGS_DEV
static void battery_cap_callback(const void *data, uint32_t len)
{
    if(battery_srv_run_state == BATTERY_SRY_RUN_MANUAL)
    {
        return;
    }
    struct sensor_cap battery_data = {0};
    battery_data = *(struct sensor_cap *) data;

    //触发充电状态改变事件
    if (battery_srv_param.battery_status != battery_data.status)
    {
        battery_srv_param.battery_status = battery_data.status;
        BATTERY_LOG_D("%s new battery status:%d\n", __func__,
                      battery_srv_param.battery_status);
        battery_state_change_evt();
    }

    //触发电量百分比改变事件
    if (battery_srv_param.battery_level != battery_data.rawdata[0])
    {
        //非充电状态下，电量变化只能减少
        if (battery_data.rawdata[0] <= battery_srv_param.battery_level
            && !battery_srv_param.battery_status)
        {
            battery_srv_param.battery_level = battery_data.rawdata[0];
#ifdef IGS_DEV
            factory_bat_data_get_cmd(battery_srv_param.battery_level);
#endif
            BATTERY_LOG_D("%s new battery level:%d\n", __func__,
                          battery_srv_param.battery_level);
            battery_level_change_evt();
        }
        //充电状态下，电量变化只能增加,只在充满电时触发电量变化事件
        else if (battery_srv_param.battery_status
                 && battery_data.rawdata[0] >= battery_srv_param.battery_level)
        {
            battery_srv_param.battery_level = battery_data.rawdata[0];
#ifdef IGS_DEV
            factory_bat_data_get_cmd(battery_srv_param.battery_level);
#endif
            BATTERY_LOG_D("%s new battery level:%d\n", __func__,
                          battery_srv_param.battery_level);
            if (battery_srv_param.battery_level == 100)
            {
                battery_level_change_evt();
            }
        }
    }
}
#endif

uint8_t battery_srv_init_start(void)
{
    int ret = 0;
#ifdef IGS_DEV
    optional_config_t config = {.sampling_rate = 0};
    ret = qw_dataserver_subscribe(CONFIG_QW_SENSOR_NAME_CAP, battery_cap_callback,
        &config);
    if (ret != ERRO_CODE_OK)
    {
        BATTERY_LOG_D("%s subscribe failed, ret:%d\n", __func__, ret);
    }
#endif
    return ret;
}

uint8_t battery_srv_uninit_stop(void)
{
#ifdef IGS_DEV
    qw_dataserver_unsubscribe(CONFIG_QW_SENSOR_NAME_CAP,
                              battery_cap_callback);   // 取消电量订阅
#endif
    BATTERY_LOG_D("%s uninit stop\n", __func__);
    return 0;
}

int battery_get_level(void)
{
    return battery_srv_param.battery_level;
}

bool battery_get_status(void)
{
    return battery_srv_param.battery_status;
}

void gui_notify_battery_run(void)
{
    if(battery_srv_run_state == BATTERY_SRY_RUN_BALLBACK)
    {
        return;
    }

    //开机后手动触发一次电量更新和充电状态更新
    //从共享内存中获取现在的状态
#ifdef BLE_UPGRADE_ENABLE
    set_system_params()->battery_level = 95;
#endif

#ifdef IGS_DEV
    battery_srv_param.battery_status = get_system_params()->charge_state;
    battery_srv_param.battery_level = get_system_params()->battery_level;
#else
    battery_srv_param.battery_status = 0;
    battery_srv_param.battery_level = 100;
#endif
    //触发响应事件
    if(battery_srv_param.battery_status)
    {
        battery_state_change_evt();
    }
    else
    {
         battery_level_change_evt();
    }

    //开始让订阅回调接管电量更新
    battery_srv_run_state = BATTERY_SRY_RUN_BALLBACK;
    battery_srv_init_start();
}

void battery_show_backlight_on(void)
{
    if(battery_get_status())
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CHARGE_REMINDER,NULL);
    }
}


///////////////////////////////////////////////////////////////////////////////////////////////////////////////
void test_simulate_battery_level(uint8_t level)
{
    battery_srv_param.battery_level = level;
    battery_level_change_evt();
}

static qw_timer timer_battery_simulate = {0};   // 定时器

static uint8_t sim_battery_level = 25;

static void sim_battery_timer_callback(void *p_context)
{
    if (sim_battery_level >= 1)
    {
        BATTERY_LOG_D("%s sim_level:%d\n", __func__, sim_battery_level);
        test_simulate_battery_level(sim_battery_level);
        sim_battery_level--;
    }
}

void test_simulate_battery_start(bool s)
{
    if (s)
    {
        BATTERY_LOG_D("%s start sim timer\n", __func__);
        sim_battery_level = 25;
        qw_timer_init(&timer_battery_simulate,
                      QW_TIMER_FLAG_PERIODIC | QW_TIMER_FLAG_SOFT_TIMER,
                      sim_battery_timer_callback);
        qw_timer_start(&timer_battery_simulate, 5000, NULL,
                       "timer_battery_simulate");   // 启动定时器
    }
    else
    {
        BATTERY_LOG_D("%s stop sim timer\n", __func__);
        qw_timer_stop(&timer_battery_simulate);   // 停止定时器
    }
}


#ifdef IGS_DEV

static void battery_ui_app_test(int argc, char **argv)
{
    if (argc < 2)
    {
        BATTERY_LOG_D("argc error");
    }

    if (strcmp(argv[1], "help") == 0)
    {
        BATTERY_LOG_D(
            "battery_ui_app_test: type val\n\t type:[1:battery_level,2:charge_state]\n\t "
            "val:[when type =2,val:0,not_charge; 1,in charge]\n");
    }
    else if (strcmp(argv[1], "1") == 0)
    {
        int level = argv[2] ? atoi(argv[2]) : 0;
        if (level > 100 || level < 0)
        {
            BATTERY_LOG_D("battery_ui_app_test: level val error [1,100]\n");
            return;
        }
        struct sensor_cap battery_data = {0};
        battery_data.status = battery_get_status();
        battery_data.rawdata[0] = level;
        battery_cap_callback(&battery_data, sizeof(battery_data));
        BATTERY_LOG_D("upadte: new level: %d,old status:%d\n", level, battery_data.status);
    }
    else if (strcmp(argv[1], "2") == 0)
    {
        int status = argv[2] ? atoi(argv[2]) : 3;
        if (status > 1 || status < 0)
        {
            BATTERY_LOG_D("battery_ui_app_test: status val error [0,1]\n");
            return;
        }
        struct sensor_cap battery_data = {0};
        battery_data.status = status;
        battery_data.rawdata[0] = battery_get_level();
        battery_cap_callback(&battery_data, sizeof(battery_data));
        BATTERY_LOG_D("upadte: new status: %d,old level:%d\n", status,
                      battery_data.rawdata[0]);
    }
    else
    {
        BATTERY_LOG_D(
            "battery_ui_app_test: type val\n\t type:[1:battery_level,2:charge_state]\n\t "
            "val:[when type =2,val:0,not_charge; 1,in charge]\n");
    }
}

// MSH_CMD_EXPORT(battery_ui_app_test, battery_ui_app_test);
#endif
