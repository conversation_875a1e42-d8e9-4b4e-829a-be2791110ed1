/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   general_file_operate.c
@Time    :   2025/03/05 17:03:13
* 
**************************************************************************/

#include "general_file_operate.h"
#include "general_file_operation.pb.h"
#include "pb.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "file_download.h"
#include "igs_dev_config.h"
#include "ff.h"
#include "file_download_pb_decode.h"
#include "ble_cmd_response.h"
#include "training_pb.h"
#include "route_plan.h"
// #include "agps_decode.h"
// #include "agps_response.h"
#include "qw_mlist.h"
#include "map_new/map_new.h"
#include "stage/stage.h"
#include "touchgfx_js/touchgfx_js_api.h"
#include "thread_pool.h"
#include "gps_recv.h"
#include "navi_file_manager.h"
// #include "view_data_cfg_cb.h"
#include "gui_event_service.h"
static gui_evt_service_page_command_t gui_page_command;    
#define NAVI_ROUTE_REFRESH 2

bool check_and_create_dir(const char* path)
{
    if (path == NULL) {
        return false;
    }
    QW_DIR dir;
    QW_FRESULT ret = qw_f_opendir(&dir, path);
    if (QW_OK != ret) {
        if (QW_ERROR == ret) {
            rt_kprintf("dir mkdir = 0 FR_NO_FILESYSTEM");
            return false;
        } else {
            ret = qw_f_mkdir(path);
            if (QW_OK != ret) {
                rt_kprintf("dir f_mkdir = %d", ret);
                return false;
            }
        }
    } else {
        qw_f_closedir(&dir);
    }

    return true;
}

typedef struct {
    uint32_t file_type;
    uint32_t file_size;
    uint32_t file_id;
    uint8_t file_name[FILE_DOWNLOAD_NAME_LENGTH];
    uint8_t file_extension[16];
    uint8_t file_md5[FILE_DOWNLOAD_MD5_LENGTH];
}general_file_operation_info;

static void* file_operate_handle = NULL;
static qw_mlist_t mlist_file;

static void file_funlink_id(general_file_operation_info* decode_data_src)
{
    char str_id[21] = { 0 };
    char file_name[80] = { 0 };
    char file_path[110] = { 0 };
    sprintf(str_id, "%u", decode_data_src->file_id);

    qw_mlist_t* mnode;

    qw_mlist_init(&mlist_file);
    switch (decode_data_src->file_type)
    {
    case file_operation_type_enum_FILE_TYPE_ROUTE_PLAN:
        fs_scan_files(&mlist_file, g_fileDirName[enum_mian_disk_courses], ".cnx", ".fit", ".gpx", ".tcx", 0); //扫描线路文件

        FOR_MLIST(mnode, &mlist_file)//遍历链表
        {
            memset(file_name, 0x00, 80);
            memset(file_path, 0x00, 110);
            if (mnode->data_size >= ROUTE_FILE_NAME_LENGTH_MAX)
            {
                memcpy(file_name, mnode->mnode_data, ROUTE_FILE_NAME_LENGTH_MAX - 1);      //复制文件名
            }
            else
            {
                memcpy(file_name, mnode->mnode_data, mnode->data_size);      //复制文件名
            }

            if (strstr(file_name, str_id))
            {
                sprintf(file_path, "%s/%s", g_fileDirName[enum_mian_disk_courses], (char*)file_name);
                f_unlink(file_path);
                break;
            }
        }
        break;
    case file_operation_type_enum_FILE_TYPE_TRAINING:
        fs_scan_files(&mlist_file, g_fileDirName[enum_mian_disk_workouts], ".fit", 0, 0, 0, 0); //扫描训练文件

        FOR_MLIST(mnode, &mlist_file)//遍历链表
        {
            memset(file_name, 0x00, 80);
            memset(file_path, 0x00, 110);
            if (mnode->data_size >= WORKOUT_FILE_NAME_LENGTH_MAX)
            {
                memcpy(file_name, mnode->mnode_data, WORKOUT_FILE_NAME_LENGTH_MAX - 1);      //复制文件名
            }
            else
            {
                memcpy(file_name, mnode->mnode_data, mnode->data_size);      //复制文件名
            }

            if (strstr(file_name, str_id))
            {
                sprintf(file_path, "%s/%s", g_fileDirName[enum_mian_disk_workouts], (char*)file_name);
                f_unlink(file_path);
                break;
            }
        }
        break;
    case file_operation_type_enum_FILE_TYPE_ROUTE_BOOK:
        #ifndef BLE_TEMP
        fs_scan_files(&mlist_file, ROADBOOKS_DIR, ".erb", 0, 0, 0, 0); //扫描训练文件
        #endif

        FOR_MLIST(mnode, &mlist_file)//遍历链表
        {
            memset(file_name, 0x00, 80);
            memset(file_path, 0x00, 110);
            if (mnode->data_size >= WORKOUT_FILE_NAME_LENGTH_MAX)
            {
                memcpy(file_name, mnode->mnode_data, WORKOUT_FILE_NAME_LENGTH_MAX - 1);      //复制文件名
            }
            else
            {
                memcpy(file_name, mnode->mnode_data, mnode->data_size);      //复制文件名
            }

            if (strstr(file_name, str_id))
            {
                #ifndef BLE_TEMP
                sprintf(file_path, "%s/%s", ROADBOOKS_DIR, (char*)file_name);
                #endif
                f_unlink(file_path);
                break;
            }
        }
        break;
    case file_operation_type_enum_FILE_TYPE_STAGE:
        break;
    case file_operation_type_enum_FILE_TYPE_POWERON_IMG:
        break;
    case file_operation_type_enum_FILE_TYPE_SCHEDULE:
        if (strcmp((const char*)decode_data_src->file_name, "Schedules") == 0)
        {
            f_unlink("0:/iGPSPORT/Schedule/Schedules.fit");
        }
        break;
    case file_operation_type_enum_FILE_TYPE_AGPS:
        fs_scan_files(&mlist_file, GPS_EPO_FILE, ".DAT", 0, 0, 0, 0); //扫描AGPS文件
        FOR_MLIST(mnode, &mlist_file)//遍历链表
        {
            memset(file_path, 0x00, sizeof(file_path));
            sprintf(file_path, "%s/%s.%s", GPS_EPO_FILE, decode_data_src->file_name, decode_data_src->file_extension);
            f_unlink(file_path);
        }
        break;
    default:
        break;
    }
    qw_mlist_uninit(&mlist_file); //释放链表
}

static bool genaral_file_download_pb_decode(const uint8_t* data, uint16_t size, general_file_operation_info* decode_data)
{
    bool status = false;
    general_file_operation file_download_msg = { 0 };

    file_download_msg.file_name.arg = decode_data->file_name;
    file_download_msg.file_name.funcs.decode = decode_string;

    file_download_msg.file_md5.arg = decode_data->file_md5;
    file_download_msg.file_md5.funcs.decode = decode_string;

    file_download_msg.file_extension.arg = decode_data->file_extension;
    file_download_msg.file_extension.funcs.decode = decode_string;

    pb_istream_t decode_stream = pb_istream_from_buffer(data, size);
    status = pb_decode(&decode_stream, general_file_operation_fields, &file_download_msg);
    if (status) {
        decode_data->file_type = file_download_msg.file_type;
        decode_data->file_size = file_download_msg.file_size;
        decode_data->file_id = file_download_msg.file_id;
    }
    return status;
}

static void genaral_file_rename(char* name_buf, general_file_operation_info* decode_data)
{
    switch (decode_data->file_type) {
    case file_operation_type_enum_FILE_TYPE_GENERAL:
        break;
    case file_operation_type_enum_FILE_TYPE_TRAINING:
        if (!strlen((char*)decode_data->file_name))
        {
            sprintf(name_buf, "%s/%u_%u.%s", g_fileDirName[enum_mian_disk_workouts]/*WORKOUTS_DIR*/, decode_data->file_id, decode_data->file_id, decode_data->file_extension);
        }
        else
        {
            uint8_t file_name[WORKOUT_NAME_LENGTH_MAX] = { 0 };
            memcpy(file_name, decode_data->file_name, WORKOUT_NAME_LENGTH_MAX - 1);
            sprintf(name_buf, "%s/%u_%s.%s", g_fileDirName[enum_mian_disk_workouts]/*WORKOUTS_DIR*/, decode_data->file_id, file_name, decode_data->file_extension);
        }
        break;
    case file_operation_type_enum_FILE_TYPE_ROUTE_PLAN:
        if (!strlen((char*)decode_data->file_name))
        {
            sprintf(name_buf, "%s/%u_%u.%s", g_fileDirName[enum_mian_disk_courses]/*COURSES_DIR*/, decode_data->file_id, decode_data->file_id, decode_data->file_extension);
        }
        else
        {
            uint8_t file_name[ROUTE_NAME_LENGTH_MAX] = { 0 };
            memcpy(file_name, decode_data->file_name, ROUTE_NAME_LENGTH_MAX - 1);
            sprintf(name_buf, "%s/%u_%s.%s", g_fileDirName[enum_mian_disk_courses]/*COURSES_DIR*/, decode_data->file_id, file_name, decode_data->file_extension);
        }
        break;
    case file_operation_type_enum_FILE_TYPE_MAP:
    {
        #ifndef BLE_TEMP
        sprintf(name_buf, "%s/%s.%s", MAPS_DIR, decode_data->file_name, decode_data->file_extension);
        #endif
    }
    break;
    case file_operation_type_enum_FILE_TYPE_THEME:
        sprintf(name_buf, "0:/%s.%s", decode_data->file_name, decode_data->file_extension);
        break;
    case file_operation_type_enum_FILE_TYPE_FIRMWARE:
        sprintf(name_buf, "0:/%s.%s", decode_data->file_name, decode_data->file_extension);
        rt_kprintf("file_name: %s\n", decode_data->file_name);
        break;
    case file_operation_type_enum_FILE_TYPE_LANGUAGE:
        sprintf(name_buf, "0:/%s.%s", decode_data->file_name, decode_data->file_extension);
        break;
    case file_operation_type_enum_FILE_TYPE_AGPS:
        sprintf(name_buf, "%s/%s.%s", GPS_EPO_FILE, decode_data->file_name, decode_data->file_extension);
        gnss_epo_type_is_download_set(true);
        break;
    case file_operation_type_enum_FILE_TYPE_ROUTE_BOOK:
        #ifndef BLE_TEMP
        sprintf(name_buf, "%s/%u_%s.%s", ROADBOOKS_DIR, decode_data->file_id, decode_data->file_name, decode_data->file_extension);
        #endif
        break;
    case file_operation_type_enum_FILE_TYPE_STAGE:
        sprintf(name_buf, "%s/%u_%s.%s", g_fileDirName[enum_mian_disk_segments]/*SEGMENTS_DIR*/, decode_data->file_id, decode_data->file_name, decode_data->file_extension);
        break;
    case file_operation_type_enum_FILE_TYPE_POWERON_IMG:
        sprintf(name_buf, "1:/Img/%s.%s", decode_data->file_name, decode_data->file_extension);
        break;
    case file_operation_type_enum_FILE_TYPE_SCHEDULE:
        if (strcmp((const char*)decode_data->file_name, "Schedules") == 0)
        {
            sprintf(name_buf, "%s/%s.%s", g_fileDirName[enum_mian_disk_schedule]/*SCHEDULE_DIR*/, decode_data->file_name, decode_data->file_extension);
        }
        else
        {
            check_and_create_dir(SCHEDULE_COURSE);
            sprintf(name_buf, "%s/%s.%s", SCHEDULE_COURSE, decode_data->file_name, decode_data->file_extension);
        }
        break;
    case file_operation_type_enum_FILE_TYPE_WATCH_DIAL:
        sprintf(name_buf, "0:/iGPSPORT/Dial/%s.%s", decode_data->file_name, decode_data->file_extension);
        set_app_operate_type(APP_ADD_DIAL);
        break;
    default:
        break;
    }
}

extern int create_unzip_folder(const char *zip_path, const char *dest_dir);

//路书解析函数
static bool route_book_task_process(const thread_pool_task* task_info)
{
    list_sync_process(NULL);
    return true;
}

static void route_book_task_finished(void *arg, bool res)
{
    ble_cmd_success_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_FILE_OPERATION, 0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_ADD, 0);
}

// 任务处理函数
static bool unzip_dial_task_process(const thread_pool_task* task_info)
{
    unzip_dial_task_arg_t* arg = (unzip_dial_task_arg_t*)task_info->arg;
    
    if (create_unzip_folder(arg->fd_name, "0:/iGPSPORT/Dial") == 0)
    {
        return true;
    }

    return false;
}

// 任务完成回调函数
void unzip_dial_task_finished(void *arg, bool res)
{
    // 处理任务完成后的清理工作
    if (res) {
        unzip_dial_task_arg_t* task_arg = (unzip_dial_task_arg_t*)arg;
        int file_id_num = atoi((const char *)task_arg->file_name);
        send_msg_to_gui_thread(file_id_num);
        f_unlink(task_arg->fd_name);
    }
}

uint8_t ble_genaral_file_store(uint8_t service_type, uint8_t op_type, const download_pb_dsc* pb_dsc, const uint8_t* data, uint16_t data_length)
{
    // #ifndef BLE_TEMP
    const char* p_data = (const char*)data;
    static general_file_operation_info decode_data_src = { 0 };
    static char fd_name[128] = { 0 };
    if (file_operate_handle == NULL) {
        memset(fd_name, 0x00, sizeof(fd_name));
        memset(&decode_data_src, 0x00, sizeof(general_file_operation_info));
        if (!genaral_file_download_pb_decode(pb_dsc->data, pb_dsc->pb_length, &decode_data_src)) {
            ble_cmd_err_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_FILE_OPERATION, 0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_ADD, 0);
            return true;
        }

        genaral_file_rename(fd_name, &decode_data_src);
        file_funlink_id(&decode_data_src);
        file_operate_handle = fd_create_open_file(fd_name, (char*)decode_data_src.file_name);
        if (!file_operate_handle) {
            ble_cmd_err_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_FILE_OPERATION, 0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_ADD, 0);
            return true;
        }
        fd_file_set_except_size(file_operate_handle, decode_data_src.file_size);
        fd_file_set_receive_md5(file_operate_handle, (const char*)decode_data_src.file_md5);

        if (decode_data_src.file_type == file_operation_type_enum_FILE_TYPE_AGPS) {
            f_chmod(fd_name, AM_HID, AM_HID);
        }
    }
    if (data_length) {
        fd_file_download_process(file_operate_handle, p_data, data_length);
    }

    if (fd_file_get_receive_size(file_operate_handle) == fd_file_get_except_size(file_operate_handle)) {
        if (decode_data_src.file_type == file_operation_type_enum_FILE_TYPE_ROUTE_PLAN) {
            thread_pool_add_task(route_book_task_process, NULL, route_book_task_finished, osPriorityHigh);
        }
        else {
            ble_cmd_success_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_FILE_OPERATION, 0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_ADD, 0);
        }
        fd_file_download_close(&file_operate_handle);
        if (decode_data_src.file_type == file_operation_type_enum_FILE_TYPE_LANGUAGE)
        {
            // #ifndef BLE_TEMP
            // submit_asset_update_to_srv(false);
            // #endif
        }
        switch (decode_data_src.file_type) 
        {
            case file_operation_type_enum_FILE_TYPE_TRAINING:
            case file_operation_type_enum_FILE_TYPE_ROUTE_PLAN:
            case file_operation_type_enum_FILE_TYPE_MAP:
            case file_operation_type_enum_FILE_TYPE_THEME:
            case file_operation_type_enum_FILE_TYPE_LANGUAGE:
            case file_operation_type_enum_FILE_TYPE_ROUTE_BOOK:
                if(decode_data_src.file_type == file_operation_type_enum_FILE_TYPE_ROUTE_PLAN){
                    #ifndef BLE_TEMP
                    view_api_set_navi_cache_status(false);
                    #endif
                }
                #ifndef BLE_TEMP
                gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
                #endif
                break;
            case file_operation_type_enum_FILE_TYPE_WATCH_DIAL:
            {
                // 解压文件
                unzip_dial_task_arg_t* task_arg = (unzip_dial_task_arg_t*)rt_malloc(sizeof(unzip_dial_task_arg_t));
                if (task_arg != NULL)
                {
                    strcpy(task_arg->fd_name, fd_name);
                    strcpy(task_arg->file_name, (const char *)decode_data_src.file_name);
                    thread_pool_add_task(unzip_dial_task_process, task_arg, unzip_dial_task_finished, osPriorityHigh);
                }
                break;
            }
            case file_operation_type_enum_FILE_TYPE_AGPS:
            {

                gnss_epo_type_is_download_set(false);
                break;
            }
        }
        return true;
    }
    // #endif
    return false;
}

void ble_genaral_store_abort(uint8_t service_type, uint8_t op_type)
{
    if (file_operate_handle) {
        fd_clean_abort_file_by_fd(&file_operate_handle);
    }
    gnss_epo_type_is_download_set(false);
}

void send_gui_page_command(char *page_name, uint8_t cmd)
{
    memset(&gui_page_command, 0x00, sizeof(gui_evt_service_page_command_t));
    gui_page_command.page = page_name;
    gui_page_command.cmd = cmd;
    gui_page_command.user_data = (void *)(uintptr_t)cmd;
    submit_gui_event(GUI_EVT_SERVICE_PAGE_COMMAND, 0, &gui_page_command);
}