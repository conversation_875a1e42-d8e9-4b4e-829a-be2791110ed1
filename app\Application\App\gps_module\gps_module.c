/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   gps_module.c
@Time    :   2025/02/15 13:51:32
*
**************************************************************************/

#include "gps_module.h"
#include "bf0_mbox_common.h"
#include "button_config.h"
#include "drv_gpio.h"
#include "qw_log.h"
#include "sensor_hub_app_service.h"
#include "subscribe_service.h"
#include "gps_module.h"
#include "bsp_pinmux.h"
#include "bf0_pm.h"
#define GPS_IRQ_TASK "gps_irq_bm"

static rt_sem_t gps_irq_sem = RT_NULL;   // 中断触发信号量
static bool gps_irq_flag = false;

static void gps_irq_task(void *param)
{
    while (1)
    {
        if (NULL != gps_irq_sem)
            rt_sem_take(gps_irq_sem, RT_WAITING_FOREVER);
    }
}

static void gps_wakeup_mcu_irq_callback()
{
    gps_wake_lh();
}

/************************************************************************
 *@function: gps_irq_init
 *@brief: GPS中断初始化函数，用于接收GPS中断信号。
 *@param: None
 *@return: None
*************************************************************************/
void gps_irq_init(void)
{
    if (gps_irq_flag)
        return;
    qw_gpio_set(GPS_WAKE_UP_MCU, GPIO_MODE_INPUT, PIN_NOPULL);
    HAL_PIN_Set(PAD_PB48, GPIO_B48, PIN_NOPULL, 0);
    HAL_PIN_SetMode(PAD_PB48, 0, PIN_DIGITAL_IO_PULLDOWN);
    // 注册外部中断
    rt_pin_attach_irq(GPS_WAKE_UP_MCU, PIN_IRQ_MODE_RISING_FALLING, gps_wakeup_mcu_irq_callback, (void *) (rt_uint32_t) GPS_WAKE_UP_MCU);
    rt_pin_irq_enable(GPS_WAKE_UP_MCU, PIN_IRQ_ENABLE);
    // 设置唤醒源
    pm_enable_pin_wakeup_alias(GPS_WAKE_UP_MCU, AON_PIN_MODE_DOUBLE_EDGE);

    gps_irq_flag = true;
}

/************************************************************************
 *@function: gps_irq_uninit
 *@brief: GPS中断去初始化函数，用于停止接收GPS中断信号。
 *@param: None
 *@return: None
*************************************************************************/
void gps_irq_uninit(void)
{
    if (!gps_irq_flag)
        return;

    // 取消唤醒源
    pm_disable_pin_wakeup_alias(GPS_WAKE_UP_MCU);
    // 去注册外部中断
    rt_pin_detach_irq(GPS_WAKE_UP_MCU);
    // 禁用外部中断
    rt_pin_irq_enable(GPS_WAKE_UP_MCU, PIN_IRQ_DISABLE);
    HAL_PIN_Set_Analog(PAD_PB48, 0);   //gps唤醒mcu
    gps_irq_flag = false;
}

int gps_irq_task_init(void)
{
    HAL_PIN_Set_Analog(PAD_PB48, 0);   //gps唤醒mcu初始化为高阻态
#if 0
    /*init sem*/
    if (gps_irq_sem == NULL)
    {
        gps_irq_sem = rt_sem_create("gps_irq_sem", 0, RT_IPC_FLAG_FIFO);
        RT_ASSERT(gps_irq_sem);
    }

    rt_thread_t gps_service_task = rt_thread_create(GPS_IRQ_TASK, gps_irq_task,
                                                    RT_NULL, 1024 * 2,
                                                    RT_THREAD_PRIORITY_DEVICE, 10);
    if (NULL != gps_service_task)
    {
        rt_thread_startup(gps_service_task);
    }
#endif
    return 0;
}
