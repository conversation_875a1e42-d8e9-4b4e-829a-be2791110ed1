﻿  QwVerticalSwipeContainer.cpp
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(941,39): error C2440: “初始化”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(941,39):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(980,39): error C2440: “初始化”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(980,39):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1016,38): error C2440: “return”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1016,38):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1148,17): error C2065: “tempPages”: 未声明的标识符
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1150,33): error C2065: “tempPages”: 未声明的标识符
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1151,33): error C2065: “tempPages”: 未声明的标识符
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1153,34): error C2065: “tempPages”: 未声明的标识符
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1156,37): error C2065: “tempPages”: 未声明的标识符
