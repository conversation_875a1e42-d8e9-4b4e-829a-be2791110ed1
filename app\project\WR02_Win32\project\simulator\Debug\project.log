﻿  PM_Base.cpp
  QwVerticalSwipeContainer.cpp
  ActivityDuration.cpp
  ActivityDurationDailyPageView.cpp
  ActivityDurationView.cpp
  ActivityDurationWeekPageView.cpp
  AlitmeterHistroyPage.cpp
  Altimeter.cpp
  AltimeterView.cpp
  Hrv.cpp
  HrvDailyPageView.cpp
  HrvView.cpp
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(941,39): error C2440: “初始化”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(941,39):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(980,39): error C2440: “初始化”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(980,39):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1016,38): error C2440: “return”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1016,38):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,27): error C2679: 二元“=”: 没有找到接受“nullptr”类型的右操作数的运算符(或没有可接受的转换)
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.h(64,1):
      可能是“touchgfx::QwSwipePageBase &touchgfx::QwSwipePageBase::operator =(const touchgfx::QwSwipePageBase &)”
          E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,27):
          “touchgfx::QwSwipePageBase &touchgfx::QwSwipePageBase::operator =(const touchgfx::QwSwipePageBase &)”: 无法将参数 2 从“nullptr”转换为“const touchgfx::QwSwipePageBase &”
              E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
              原因如下: 无法从“nullptr”转换为“const touchgfx::QwSwipePageBase”
              E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
              “touchgfx::QwSwipePageBase::QwSwipePageBase”: 没有重载函数可以转换所有参数类型
                  E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.h(64,1):
                  可能是“touchgfx::QwSwipePageBase::QwSwipePageBase(const touchgfx::QwSwipePageBase &)”
                      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
                      “touchgfx::QwSwipePageBase::QwSwipePageBase(const touchgfx::QwSwipePageBase &)”: 无法将参数 1 从“nullptr”转换为“const touchgfx::QwSwipePageBase &”
                          E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
                          原因如下: 无法从“nullptr”转换为“const touchgfx::QwSwipePageBase”
                          E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
                          转换要求第二个用户定义的转换运算符或构造函数
                  E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,31):
                  尝试匹配参数列表“(nullptr)”时
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1108,27):
      尝试匹配参数列表“(touchgfx::QwSwipePageBase, nullptr)”时
  
E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1136,42): error C2440: “=”: 无法从“touchgfx::QwSwipePageBase”转换为“touchgfx::QwSwipePageBase *”
      E:\greeit_Qwos\qw_platform\qwos_app\GUI\QwWidget\QwVerticalSwipe\QwVerticalSwipeContainer.cpp(1136,42):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
  HrvWeekPageView.cpp
  NaviAltitudeCon.cpp
  NavigationPreview.cpp
  NavigationPreviewView.cpp
  NaviTrackCon.cpp
  Pressure.cpp
  PressureDailyPageView.cpp
  PressureStageView.cpp
  PressureView.cpp
  PressureWeekPageView.cpp
  CaloriesCount.cpp
  CaloriesCountView.cpp
  CaloriesDailyPageView.cpp
  CaloriesWeekPageView.cpp
  HeartRate.cpp
  HeartRateDailyPageView.cpp
  HeartRateView.cpp
  HeartRateWeekPageView.cpp
  IntenseDuration.cpp
  IntenseDurationDailyPageView.cpp
  IntenseDurationView.cpp
  IntenseDurationWeekPageView.cpp
  RideAbility.cpp
  RideAbilityView.cpp
  RideLacticAcidPage.cpp
  RideOxygenPage.cpp
  RideRadarPage.cpp
  GradePredictionsPage.cpp
  LacticAcidPage.cpp
  OxygenUptakePage.cpp
  RunAbility.cpp
  RunAbilityView.cpp
  RunRadarPage.cpp
  SleepDailyPageView.cpp
  SleepInfo.cpp
  SleepInfoView.cpp
  SleepNapPageView.cpp
  SleepNoticePageView.cpp
  SleepStageView.cpp
  SleepWeekPageView.cpp
  SleepSpo2View.cpp
  Spo2.cpp
  Spo2DailyPageView.cpp
  Spo2View.cpp
  Spo2WeekPageView.cpp
  StepsCount.cpp
  StepsCountView.cpp
  StepsDailyPageView.cpp
  StepsWeekPageView.cpp
  TodayActivitys.cpp
  TodayActivitysDailyPageView.cpp
  TodayActivitysView.cpp
  HistroyLoadPage.cpp
  HistroyTrendPage.cpp
  PhysicalRecoveryPage.cpp
  TraingStatus.cpp
  TraingStatusPage.cpp
  TraingStatusView.cpp
  Weather.cpp
  WeatherHourPage.cpp
  WeatherNodataPage.cpp
  WeatherTodayPage.cpp
  WeatherView.cpp
  WeatherWeeklyPage.cpp
  FAT_GpsSignal.cpp
  FAT_GpsSignalView.cpp
  GpsSignalInfoCon.cpp
  GpsSignalSNRCon.cpp
  MenuCard.cpp
  MenuCardView.cpp
  SummaryChartContainer.cpp
E:\greeit_Qwos\app\Application\App\UI\Page\AppMenu\Weather\WeatherHourPage.cpp(20,24): warning C4305: “初始化”: 从“double”到“float”截断
  SummaryDetailContainer.cpp
  SummaryIntervalTrainCon.cpp
  SummaryLapContainer.cpp
  SummarySportContainer.cpp
  RecoveryTimeCon.cpp
  SummaryVo2max.cpp
  SummaryWorkoutRet.cpp
  SummaryZoneContainer.cpp
  Summary.cpp
  SummaryView.cpp
  FlashLight.cpp
  FlashLightView.cpp
