/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgBoxService.cpp
@Time    :   2024/12/13 16:08:06
*
**************************************************************************/

#include <stdint.h>
#include <stdbool.h>

#include "../module/gui/GUICtrl/QwMsgBox/QwMsgInfo.h"
#include "MsgBoxService.h"

QwMsgPara_t msg_para_list[enumPOPUP_MAX] =
{
#ifdef MSGBOX_TEST
    [enumPOPUP_TEST1] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_TEST2] = {MSG_PRIORITY_4, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_TEST3] = {MSG_PRIORITY_4, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_TEST4] = {MSG_PRIORITY_3, MSG_TYPE_HALF_TIP, SCOPE_ALL},
#endif
    [enumPOPUP_ALARM_CLOCK] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},                        
    [enumPOPUP_TIMER_CLOCK] = {MSG_PRIORITY_4, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_HEALTH_GOAL] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_HEALTH_SEDENTARY] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SPORT_REMIND] = {MSG_PRIORITY_6, MSG_TYPE_HALF_TIP, SCOPE_ALL},
    [enumPOPUP_GIVE_UP_SPORT] = {MSG_PRIORITY_3, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_POWER_SAVE] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_POWER_SAVE_UNLOCK] = {MSG_PRIORITY_1, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SPORTING_LAP] = {MSG_PRIORITY_5, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_CHARGE_REMINDER] = {MSG_PRIORITY_4, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_UNDER_VOLTAGE_TEN] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_UNDER_VOLTAGE_TWENTY] = {MSG_PRIORITY_4, MSG_TYPE_HALF_TIP, SCOPE_ALL},
    [enumPOPUP_UNDER_VOLTAGE_OFF] = {MSG_PRIORITY_0, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_WATCH_UNLOCK] = {MSG_PRIORITY_1, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SAVE_RECORD] = {MSG_PRIORITY_1, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_TOAST_TIP] = {MSG_PRIORITY_7, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_ALIPAY] = {MSG_PRIORITY_1, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_BPWR_CALIB] = {MSG_PRIORITY_6, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_BPWR_CALIB_RESULT] = {MSG_PRIORITY_2, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SPORT_OUT_COUNT_DOWN] = {MSG_PRIORITY_3, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SPORT_NO_GPS] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_REMOVE_SENSOR] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_RECONNECT_SENSOR] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_SENSOR_LOW_POWER] = {MSG_PRIORITY_6, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_SENSOR_CONN_REACHED_LIMIT] = {MSG_PRIORITY_7, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_ALTIMETER_SUCC] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_ALTIMETER_FAILED] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_ALTIMETER_ACL] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_HEART_RATE] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_HEART_RATE_NOTICE] = {MSG_PRIORITY_7, MSG_TYPE_FULL_ENTER, SCOPE_ALL},
    [enumPOPUP_AOD_TIP] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_DECODE_ERR] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_DND_TIP] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SLEEP_BED] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_RESET_TIP] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_DELETE_FIT] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_LOADING] = {MSG_PRIORITY_1, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_SPO2] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_PRESSURE] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_PRESSURE_REMIND] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_POWER_CONSUME_REMIND] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_ALARM_REMINDER] = {MSG_PRIORITY_7, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_VRB_BACKWARD] = {MSG_PRIORITY_6, MSG_TYPE_HALF_TIP, SCOPE_ALL},
    [enumPOPUP_DELETE_TRAINING_COURSE] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_DELETE_NAVIGATION] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_TRIATHLON_RESET] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_TRIATHLON_CHANGE] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_TRIATHLON_NEXT] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_TRIATHLON_COMPLETED] = {MSG_PRIORITY_4, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_MODIFY_WHEEL_PERIMETER] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FEC_NOT_CON] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_INNER_TRAIN_TIP] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION] = {MSG_PRIORITY_7, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION] = {MSG_PRIORITY_7, MSG_TYPE_TOAST, SCOPE_ALL},     
    [enumPOPUP_SENSOR_CONNECT_IN_MOTION] = {MSG_PRIORITY_6, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_SENSOR_DISCONNECT_IN_MOTION] = {MSG_PRIORITY_6, MSG_TYPE_TOAST, SCOPE_ALL},
    [enumPOPUP_FTP_TRAIN_COURSE] = {MSG_PRIORITY_7, MSG_TYPE_FULL_ENTER, SCOPE_ALL},
    [enumPOPUP_SENSOR_CONNECT_FEC_POWER_TIP] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FTP_TRAIN_COMPLETED] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FTP_TRAIN_FAILED] = {MSG_PRIORITY_6, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_BIND_WATCH_SUCC] = {MSG_PRIORITY_7, MSG_TYPE_OTHER, SCOPE_ALL},
    [enumPOPUP_BIND_WATCH_FAILED] = {MSG_PRIORITY_7, MSG_TYPE_OTHER, SCOPE_ALL},
    [enumPOPUP_UNBIND] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FACTORY_RESET_ID_CHANGE] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_SPORTING_GPS_LOCATE] = {MSG_PRIORITY_6, MSG_TYPE_HALF_TIP, SCOPE_ALL},
    [enumPOPUP_SPORTING_GPS_LOSS] = {MSG_PRIORITY_6, MSG_TYPE_HALF_TIP, SCOPE_ALL},
    [enumPOPUP_NAVI_START] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_NAVI_YAW] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_NAVI_RESTORE_ROUTE] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_NAVI_END] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_NAVI_REMAINING_CLIMB] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_NAVI_CHANGE_DIR] = {MSG_PRIORITY_6, MSG_TYPE_BOTTOM_TOAST, SCOPE_ALL},
    [enumPOPUP_INTELLIGENT_NOTIFY] = {MSG_PRIORITY_5, MSG_TYPE_HALF_MESSAGE, SCOPE_ALL},
    [enumPOPUP_INCOMING_CALL] = {MSG_PRIORITY_3, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_ACH_RESET] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_ACH_UPDATE] = {MSG_PRIORITY_2, MSG_TYPE_FULL_ENTER, SCOPE_ALL},
    [enumPOPUP_MAX_HR_UPDATE] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_LT_HR_UPDATE] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FTP_UPDATE] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_UNBIND_WATCH_APP] = {MSG_PRIORITY_7, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_RESET] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_RESTORE_FACTORY] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_FTP_TEST_REMINDER] = {MSG_PRIORITY_2, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_NAVI_BACK] = {MSG_PRIORITY_7, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
    [enumPOPUP_SPORT_START_COUNT_DOWN] = {MSG_PRIORITY_3, MSG_TYPE_FULL_TIP, SCOPE_ALL},
    [enumPOPUP_TODAY_TRAIN_TIP] = {MSG_PRIORITY_4, MSG_TYPE_FULL_SELECT, SCOPE_ALL},
};
