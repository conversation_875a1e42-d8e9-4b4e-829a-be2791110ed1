/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SportStartView.cpp
@Time    :   2024/12/10 19:20:02
*
**************************************************************************/

#include "SportStartView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QWGUIImage.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "../../qwos_app/GUI/Translate/QwDataKeyText.h"
#include "../navi_srv_module/navi_srv_module.h"
#include "GUI/QwGUIKey.h"
#include "GUIMsg/MsgBoxDataDef.h"
#include "Image/images.h"
#include "gui_event_service.h"
#include "workout_interface.h"
#include <climits>
#ifndef SIMULATOR
#include "ble_ant_module.h"
#endif
#include "../Page/Summary/SummaryIntervalTrain/interval_train_summary.h"
#include "fit_workout.h"
static const char g_sports_start_view_title[] = "_sports_settings";
TM_KEY(g_sports_start_view_title)
static const char g_interval_text[] = "_interval";   //间歇
TM_KEY(g_interval_text)

static const char g_free_interval_text[] = "_free_training";   //间歇
TM_KEY(g_free_interval_text)

static const char *g_sport_sub_text[] = {
    "_free",
    "_times",
};
TM_DECLARE(g_sport_sub_text)

extern const char *g_runway_settings_text[];
extern const char *runway_selections[];
extern const char *g_sport_sls_text[];
constexpr auto SENSOR_GAP = 4;

struct select_sport_info
{
    const char *name;
    const void *img;

    select_sport_info(const char *t, const void *m)
        : name(t)
        , img(m) {};
};

static const void *s_sensor_img[] = {
    &sensor_runbeans_44,   &sensor_speed_44,     &sensor_cadence_44, &sensor_heart_44,        &sensor_external_heart_44, &sensor_power_44,
    &sensor_frontlight_44, &sensor_taillight_44, &sensor_radar_44,   &sensor_rideplatform_44, &sensor_speed_cadence_44,
};

SportStartView::SportStartView(PageManager *manager)
    : PageView(manager)
    , p_sport_type_(nullptr)
    , p_location_state_(nullptr)
    , p_sport_tips_(nullptr)
    , p_sport_sensor_state_(nullptr)
    , p_start_sport_(nullptr)
    , p_quit_sport_(nullptr)
    , p_countdown_time_(nullptr)
    , p_arc_angle_(nullptr)
    , on_start_sport_(this, &SportStartView::on_click_start_sport)
    , on_quit_sport_(this, &SportStartView::on_click_quit_sport)
    , update_sport_type_(this, &SportStartView::update_sport_type)
    , update_location_state_(this, &SportStartView::update_location_state)
    , update_sport_tips_(this, &SportStartView::update_sport_tips)
    , update_sport_sensor_state_(this, &SportStartView::update_sport_sensor_state)
    , update_arc_angle_(this, &SportStartView::update_arc_angle)
    , fab_click_evt_(this, &SportStartView::handleKeyEvent)
    , sensor_count_(0)
    , circle_arc_(0)
    , gps_flag_(false)
    , has_drag_(false)
    , has_gesture_(false)
    , tick_(0)
    , p_refresh_flag_(nullptr)
    , p_set_refresh_flag_(nullptr)
    , on_set_refresh_flag_(this, &SportStartView::on_click_set_refresh_flag)
    , update_refresh_flag_(this, &SportStartView::update_refresh_flag)
{}

SportStartView::~SportStartView()
{
    //TopStatus::getTopStatus()->close();
}

void SportStartView::setup()
{
    clear_all();

    int index = *p_sport_type_->get_val(0);

    add(bg_);
    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(lv_color_black());

    add(top_);
    top_.set_time_style(get_time_style());
    if (get_sport_type_is_outdoor((SPORTTYPE) index))
    {
        top_.set_show_type(TOP_STATUS_TYPE_ADD_ALL);
    }
    else
    {
        top_.set_show_type(TOP_STATUS_TYPE_ADD_BATTERY);
    }
    top_.setup();

    add(sport_icon_);
    sport_icon_.setWidth(40);
    sport_icon_.setHeight(40);
    sport_icon_.setBitmap(Bitmap(g_sport_img_44[index]));
    sport_icon_.setVisible(true);

    add(sport_name_);
    sport_name_.setWidth(466);
    sport_name_.setTextFont(&PUBLIC_NO_38_M_FONT);
    sport_name_.setTextAlignment(LEFT);
    sport_name_.setColor(lv_color_white());
    sport_name_.setTypedDynamicText(get_sports_name_by_type(index));
    sport_name_.resizeToCurrentTextWithAlignment();
    sport_name_.setHeight(52);
    sport_name_.setTextVerticalAlignment(ALIGN_Y_MID);

    int tmp = sport_icon_.getWidth() + sport_name_.getWidth();
    tmp = (HAL::DISPLAY_WIDTH - tmp) / 2;
    sport_icon_.setAlign(ALIGN_IN_LT, tmp, 4 + 76);
    sport_name_.setAlignTo(sport_icon_, ALIGN_OUT_RM, 4, 0);

    //添加传感器状态图标
    for (int i = 0; i < SPORT_SENSOR_NUM; i++)
    {
        add(sensor_icon_[i]);
        sensor_icon_[i].setBitmap(Bitmap(s_sensor_img[i]));
        sensor_icon_[i].setVisible(false);
    }
    for (int i = 0; i < SPORT_SENSOR_NUM; i++)
    {
        uint8_t sensor_status = *p_sport_sensor_state_->get_val(i);
        update_sensor((PAGE_SENSOR_TYPE) i, (PAGE_SENSOR_STATUS) sensor_status);
    }

    add(item_bg_);
    item_bg_.setPosition(0, 165, 466, 136);
    Bitmap bitmap((const void *) app_select_item_img_bg_dir);
    item_bg_.setBitmap(bitmap);
    item_bg_.setBitmapXYoffset((item_bg_.getWidth() - bitmap.getWidth()) / 2, (item_bg_.getHeight() - bitmap.getHeight()) / 2);
    item_bg_.setVisible(true);

    add(start_con_);
    start_con_.setWidthHeight(item_bg_);
    start_con_.setAlignTo(item_bg_, ALIGN_IN_CENTER, 0, 0);

    start_con_.add(start_);
    start_.setWidthHeight(204, 56);
    start_.setBitmap(Bitmap(&sport_start_prompt));
    start_.setVisible(true);

    //TODO 需要添加一个函数，用来处理当前副标题应该如何显示
    /*
    1、导航需要显示 优先级1
    2、间歇训练需要显示 优先级2
    3、某些运动需要显示 优先级3

    static bool is_show_sport_tips(SPORTTYPE sport_type, char** sub_str)
    {
        bool is_navi_running = check_navi_status();
        bool is_innr_train_running = check_innr_train_status();
        bool is_special_sport = check_special_sport(sport_type);

        if (is_navi_running)
        {
            *sub_str = get_navi_sub_title();
        }
        else if (is_innr_train_running)
        {
            *sub_str = get_innr_train_sub_title();
        }
        else if (is_special_sport)
        {
            *sub_str = get_special_sport_sub_title();
        }
    }




    */

    //导航
    if (navi_srv_get_prepare_status() == NAVI_PREPARE_STATUS_RUNNING)
    {
        char round_text[MAX_NAVI_FILE_NAME_SIZE] = {0};
        if (navi_srv_is_support_sport_type((SPORTTYPE)index))
        {
            char navi_file_name[MAX_NAVI_FILE_NAME_SIZE];
            navi_srv_get_preview_file_name(navi_file_name);
            sprintf(round_text, "%s", navi_file_name);
        }
        start_con_.add(round_);
        round_.setWidthHeight(386, 44);
        round_.setTextAlignment(CENTER);
        round_.setColor(lv_color_white());
        round_.setLabelAlpha(LV_OPA_0);
        round_.setTextFont(&PUBLIC_NO_32_R_FONT);
        round_.setTypedDynamicText(round_text);
        round_.setAlign(ALIGN_IN_BM, 0, -14);

        if (strcmp(round_text, "") == 0)
        {
            start_.setAlign(ALIGN_IN_CENTER, 0, 0);
        }
        else
        {
            start_.setAlign(ALIGN_IN_TM, 0, 18);
        }
    }
    else if (index <= SPORTSTYPE_TRAIL_RUNNING || (index <= SPORTSTYPE_TRIP_CYCLING && index >= SPORTSTYPE_CYCLING) || index == SPORTSTYPE_POOL_SWIMMING
             || index == SPORTSTYPE_STRENGTH_TRAINING || index == SPORTSTYPE_ROWING_MACHINE || index == SPORTSTYPE_JUMP_ROPE)
    {
        char round_text[MAX_NAVI_FILE_NAME_SIZE] = {0};
        //间歇训练
        if (index <= SPORTSTYPE_TRIP_CYCLING && get_cur_course_type() == enum_course_interval)
        {
            IT_TRAIN_DURATION_TYPE type = get_interval_train_train_duration_type();
            uint32_t cycle = get_interval_train_cycle();
            if (type == IT_TRAIN_DURATION_TYPE_DISTANCE)
            {
                uint32_t val = get_interval_train_train_duration_value(IT_TRAIN_DURATION_TYPE_DISTANCE) / 1000;
                uint32_t d = val / 100;
                uint32_t f = val % 100;
                if (d > 0)
                {
                    sprintf(round_text, "%s %dx%d.%02dkm", _TM(g_interval_text), cycle, d, f);
                }
                else
                {
                    sprintf(round_text, "%s %dx0.%dkm", _TM(g_interval_text), cycle, f);
                }
            }
            else if (type == IT_TRAIN_DURATION_TYPE_TIME)
            {
                uint32_t val = get_interval_train_train_duration_value(IT_TRAIN_DURATION_TYPE_TIME);
                sprintf(round_text, "%s %dx%02d:%02d", _TM(g_interval_text), cycle, val / 60, val % 60);
            }
            else if (type == IT_TRAIN_DURATION_TYPE_MANUAL)
            {
                sprintf(round_text, "%s", _TM(g_free_interval_text));
            }
        }
        //操场跑步
        else if (index == SPORTSTYPE_PLAYGROUND)
        {
            sprintf(round_text, "%s/%s", _TM(g_runway_settings_text[*p_sport_tips_->get_val(0)]), runway_selections[*p_sport_tips_->get_val(0)]);
        }
        //泳池长度
        else if (index == SPORTSTYPE_POOL_SWIMMING)
        {
            uint8_t swim_info = get_sport_swim_info();
            if (swim_info < 5)
            {
                sprintf(round_text, "%s", _TM(g_sport_sls_text[swim_info]));
            }
            else
            {
                sprintf(round_text, "%d%s", get_sport_swim_custom_info(), get_sport_swim_metric_info() ? "yd" : "m");
            }
        }
        else if (index == SPORTSTYPE_STRENGTH_TRAINING || index == SPORTSTYPE_ROWING_MACHINE || index == SPORTSTYPE_JUMP_ROPE)
        {
            if (!get_auto_record_lap((SPORTTYPE) index))
            {
                sprintf(round_text, "%s", _TM(g_sport_sub_text[0]));
            }
            else if (get_auto_record_lap_type((SPORTTYPE) index) == AUTO_RECORD_LAP_TIMES)
            {
                uint32_t times = get_auto_record_lap_value((SPORTTYPE) index, AUTO_RECORD_LAP_TIMES);
                sprintf(round_text, "%02d:%02d:%02d", times / 3600, (times / 60) % 60, times % 60);
            }
            else if (get_auto_record_lap_type((SPORTTYPE) index) == AUTO_RECORD_LAP_NUMS)
            {
                sprintf(round_text, "%d", get_auto_record_lap_value((SPORTTYPE) index, AUTO_RECORD_LAP_NUMS));
            }
        }
        start_con_.add(round_);
        round_.setWidthHeight(386, 44);
        round_.setTextAlignment(CENTER);
        round_.setColor(lv_color_white());
        round_.setLabelAlpha(LV_OPA_0);
        round_.setTextFont(&PUBLIC_NO_32_R_FONT);
        round_.setTypedDynamicText(round_text);
        round_.setAlign(ALIGN_IN_BM, 0, -14);

        if (strcmp(round_text, "") == 0)
        {
            start_.setAlign(ALIGN_IN_CENTER, 0, 0);
        }
        else
        {
            start_.setAlign(ALIGN_IN_TM, 0, 18);
        }
    }
    else
    {
        start_.setAlign(ALIGN_IN_CENTER, 0, 0);
    }

    add(fab_);
    fab_.set_fab_click_handle(fab_click_evt_);
    fab_.setup(FABTN_START::NONE, FABTN_BACK::NONE, FABTN_POWER::SETTING);

    add(gps_serach_circle_);
    gps_serach_circle_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    gps_serach_circle_.setCircle(gps_serach_circle_.getWidth() / 2, gps_serach_circle_.getHeight() / 2, gps_serach_circle_.getWidth() / 2);
    gps_serach_circle_.setLineWidth(8);
    gps_serach_circle_.setColor(lv_color_hex(get_system_color(enum_system_red)));
    gps_serach_circle_.setLineEndingStyle(BUTT_CAP_ENDING);
    gps_serach_circle_.setVisible(get_sport_type_is_outdoor((SPORTTYPE) index));

    update_circle(*p_arc_angle_->get_val(0));
}

void SportStartView::handleTickEvent()
{
    for (int i = 0; i < SPORT_SENSOR_NUM; i++)
    {
        uint8_t state = *p_sport_sensor_state_->get_val(i);
        update_flicker((PAGE_SENSOR_TYPE) i, (PAGE_SENSOR_STATUS) state);
    }
    top_.handleTickEvent();
    fab_.handleTickEvent();
}

void SportStartView::handleKeyEvent(uint8_t c)
{
    if (c == KEY_CLK_BACK)
    {
        on_click_quit_sport();
        manager_->push("SportsMenu");
    }
    else if (c == KEY_CLK_START)
    {
        ClickStartSport_state state = ClickStartSport_state::CLICK_START_SPORT;
        manager_->page_command("SportStart", (int) SportStart_CMD::SET_CLICK_START_SPORT, &state);
    }
    else if (c == KEY_CLK_POWER)
    {
        page_to_setting();
    }
}

void SportStartView::handleClickEvent(const ClickEvent &evt)
{
    fab_.handleClickEvent(evt);
    if (evt.getType() == ClickEvent::RELEASED)
    {
        if (!has_drag_ && !has_gesture_)
        {
            if (evt.getY() >= 188 && evt.getY() <= 302)
            {
                ClickStartSport_state state = ClickStartSport_state::CLICK_START_SPORT;
                manager_->page_command("SportStart", (int) SportStart_CMD::SET_CLICK_START_SPORT, &state);
            }
        }
        has_drag_ = false;
        has_gesture_ = false;
    }
}

void SportStartView::handleDragEvent(const DragEvent &evt)
{
    fab_.handleDragEvent(evt);
    has_drag_ = true;
}

void SportStartView::handleGestureEvent(const GestureEvent &evt)
{
    fab_.handleGestureEvent(evt);
    has_gesture_ = true;
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
    {
        on_click_quit_sport();
        manager_->push("SportsMenu");
    }
}

// Notification Callback function
void SportStartView::set_on_start_sport(Notification<> *command)
{
    p_start_sport_ = command;
}

void SportStartView::on_click_start_sport()
{
    if (p_start_sport_ != nullptr)
    {
        p_start_sport_->notify();
    }
}

void SportStartView::set_on_quit_sport(Notification<> *command)
{
    p_quit_sport_ = command;
}

void SportStartView::on_click_quit_sport()
{
    if (p_quit_sport_ != nullptr)
    {
        p_quit_sport_->notify();
    }
}

void SportStartView::set_on_out_page(Notification<> *command)
{
    p_out_page_ = command;
}

void SportStartView::set_on_countdown_time_change(Notification<int> *command)
{
    p_countdown_time_change_ = command;
}

void SportStartView::on_click_countdown_time_change(int t1)
{
    if (p_countdown_time_change_ != nullptr)
    {
        p_countdown_time_change_->notify(t1);
    }
}

void SportStartView::set_on_set_refresh_flag(Notification<uint8_t> *command)
{
    p_set_refresh_flag_ = command;
}

void SportStartView::on_click_set_refresh_flag(uint8_t t1)
{
    if (p_set_refresh_flag_ != nullptr)
    {
        p_set_refresh_flag_->notify(t1);
    }
}

// ObserverDrawable Callback function
void SportStartView::set_update_refresh_flag(ObserverDrawable<Drawable, uint8_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_refresh_flag_ = observer;
        observer->bind_ctrl(0, round_);
        observer->bind_notify(update_refresh_flag_);
    }
}

void SportStartView::update_refresh_flag(Drawable *ctrl, Parameters<uint8_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        if (data->get_val() != 0)
        {
            char navi_file_name[MAX_NAVI_FILE_NAME_SIZE] = {0};
            navi_srv_get_preview_file_name(navi_file_name);
            round_.setTypedDynamicText(navi_file_name);
            round_.invalidate();
            p_set_refresh_flag_->notify(0);
        }
    }
}

void SportStartView::set_update_sport_type(ObserverDrawable<Drawable, uint8_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_sport_type_ = observer;
        observer->bind_ctrl(0, sport_name_);
        observer->bind_notify(update_sport_type_);
    }
}

void SportStartView::update_sport_type(Drawable *ctrl, Parameters<uint8_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void SportStartView::set_update_location_state(ObserverDrawable<Drawable, uint8_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_location_state_ = observer;
        observer->bind_ctrl(0, gps_serach_circle_);
        observer->bind_notify(update_location_state_);
    }
}

void SportStartView::update_location_state(Drawable *ctrl, Parameters<uint8_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void SportStartView::set_update_countdown_time(ObserverDrawable<Drawable, uint8_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_countdown_time_ = observer;
        observer->bind_ctrl(0, start_);
        observer->bind_notify(update_countdown_time_);
    }
}

void SportStartView::update_countdown_time(Drawable *ctrl, Parameters<uint8_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void SportStartView::set_update_sport_tips(ObserverDrawable<Drawable, uint32_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_sport_tips_ = observer;
        observer->bind_ctrl(0, round_);
        observer->bind_notify(update_sport_tips_);
    }
}

void SportStartView::update_sport_tips(Drawable *ctrl, Parameters<uint32_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void SportStartView::set_update_sport_sensor_state(ObserverDrawable<Drawable, uint8_t, SPORT_SENSOR_NUM> *observer)
{
    if (observer != nullptr)
    {
        p_sport_sensor_state_ = observer;
        for (int i = 0; i < SPORT_SENSOR_NUM; i++)
        {
            observer->bind_ctrl(i, sensor_icon_[i]);
        }
        observer->bind_notify(update_sport_sensor_state_);
    }
}

void SportStartView::update_sport_sensor_state(Drawable *ctrl, Parameters<uint8_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr && sensor_icon_[SPORT_SENSOR_NUM - 1].getParent() != nullptr && idx < SPORT_SENSOR_NUM)
    {
        int type = idx;
        uint8_t sensor_status = *p_sport_sensor_state_->get_val(idx);

        update_sensor((PAGE_SENSOR_TYPE) idx, (PAGE_SENSOR_STATUS) sensor_status);
    }
}

void SportStartView::set_update_arc_angle(ObserverDrawable<Drawable, int, 1> *observer)
{
    if (observer != nullptr)
    {
        p_arc_angle_ = observer;
        observer->bind_ctrl(0, gps_serach_circle_);
        observer->bind_notify(update_arc_angle_);
    }
}

void SportStartView::update_arc_angle(Drawable *ctrl, Parameters<int> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr && ctrl->getParent() != nullptr)
    {
        update_circle(*p_arc_angle_->get_val(0));
    }
}

// custom function
void SportStartView::clear_all()
{
    getRootContainer().removeAll();
    getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
}

void SportStartView::update_sensor(PAGE_SENSOR_TYPE type, PAGE_SENSOR_STATUS status)
{
    if (type < PAGE_SENSOR_TYPE__MAX && status < PAGE_SENSOR_STATUS__MAX && (int) type >= 0 && (int) status >= 0)
    {
        switch (status)
        {
        case PAGE_SENSOR_STATUS_CLOSE:
            sensor_icon_tick_[(int) type] = 0;
            if (sensor_icon_[(int) type].isVisible())
            {
                sensor_icon_[(int) type].setVisible(false);
                break;
            }
            else
            {
                return;
            }
        case PAGE_SENSOR_STATUS_SAVED:
            if (!sensor_icon_[(int) type].isVisible() || sensor_icon_tick_[(int) type] == 0)
            {
                sensor_icon_[(int) type].setVisible(true);
                sensor_icon_[(int) type].setAlpha(0);
                sensor_icon_tick_[(int) type] = get_boot_msec();
                break;
            }
            else
            {
                return;
            }
        case PAGE_SENSOR_STATUS_CONNECTED:
            sensor_icon_tick_[(int) type] = 0;
            if (!sensor_icon_[(int) type].isVisible() || sensor_icon_[(int) type].getAlpha() != LV_OPA_COVER)
            {
                sensor_icon_[(int) type].setVisible(true);
                sensor_icon_[(int) type].setAlpha(LV_OPA_COVER);
                break;
            }
            else
            {
                return;
            }

        default:
            return;
        }

        int count = 0;
        for (int i = 0; i < SPORT_SENSOR_NUM; i++)
        {
            if (sensor_icon_[i].isVisible())
            {
                count++;
            }
        }

        if (count > 0)
        {
            int width = count * sensor_icon_[(int) type].getWidth() + (count - 1) * SENSOR_GAP;
            int start_pos = (HAL::DISPLAY_WIDTH - width) / 2;

            for (int i = 0; i < SPORT_SENSOR_NUM; i++)
            {
                if (sensor_icon_[i].isVisible())
                {
                    sensor_icon_[i].setAlign(ALIGN_IN_LT, start_pos, 333);
                    //sensor_icon_[i].invalidate();
                    start_pos += (sensor_icon_[i].getWidth() + SENSOR_GAP);
                }
            }
        }
    }
}

void SportStartView::update_flicker(PAGE_SENSOR_TYPE type, PAGE_SENSOR_STATUS status)
{
    if (sensor_icon_tick_[(int) type] != 0 && sensor_icon_[(int) type].isVisible())
    {
        int opa = (500 - abs(((get_boot_msec() - sensor_icon_tick_[(int) type]) % 1000) - 500)) * 255 / 500;
        sensor_icon_[(int) type].setAlpha(opa);
        sensor_icon_[(int) type].invalidate();
    }
}

void SportStartView::update_circle(int arc)
{
    uint8_t tmp = *p_sport_type_->get_val(0);
    if (circle_arc_ != arc && get_sport_type_is_outdoor((SPORTTYPE) tmp))
    {
        if (arc == INT_MAX)
        {
            gps_serach_circle_.setColor(lv_color_hex(get_system_color(enum_system_green)));
            gps_serach_circle_.updateEndAngle(360);
        }
        else if (arc > circle_arc_)
        {
            gps_serach_circle_.setColor(lv_color_hex(get_system_color(enum_system_red)));
            if (!gps_serach_circle_.isVisible())
            {
                gps_serach_circle_.setVisible(true);
            }
            gps_serach_circle_.updateEndAngle(arc);
        }

        if (circle_arc_ == INT_MAX || arc == INT_MAX)
        {
            gps_serach_circle_.invalidate();
        }
        circle_arc_ = arc;
    }
}

void SportStartView::page_to_setting()
{
    uint8_t tmp = 0;
    manager_->page_command("SportSettings", (int) SportSettings_CMD::SET_FROM_PAGE, &tmp);
    manager_->page_command("SportSettingsMore", (int) SportSettingsMore_CMD::SET_FROM_PAGE, &tmp);

    // p_out_page_->notify();
    manager_->push("SportSettings");
}

// /************************************************************************
//  *@function:开启运动
//  *@brief:
//  *@param:ctrl:true-强制开启运动，false-判断定位状态后开启运动
//  *@return:
// *************************************************************************/
// void SportStartView::page_to_sport(bool ctrl)
// {
//     if (!ctrl)
//     {
//         uint8_t tmp = *p_sport_type_->get_val(0);
//         if (get_sport_type_is_outdoor((SPORTTYPE)tmp))//户外运动
//         {
//             if (*p_location_state_->get_val(0) == 0)//没有GPS
//             {
//                 submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORT_NO_GPS, NULL);
//                 return;
//             }
//             else//有GPS
//             {
//                 if (if_need_show_sensor_connect_tip())
//                 {
//                     show_sensor_connect_fec_power_tip();
//                     return;
//                 }
//             }
//         }
//         else//室内运动
//         {
//             if (if_need_show_sensor_connect_tip())
//             {
//                 show_sensor_connect_fec_power_tip();
//                 return;
//             }
//         }
//     }

//     if (*p_countdown_time_->get_val(0) == 0)
//     {
//         submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CLOSE, (void*)enumPOPUP_SPORT_START_COUNT_DOWN);
//         on_click_start_sport();
//         manager_->push("WebGrid");
//     }
//     else
//     {
//         // manager_->push("SportStartCountDown");
//     }
// }
