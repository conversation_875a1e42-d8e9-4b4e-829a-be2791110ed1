/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AutoPageSetting.cpp
@Time    :   2024/12/13 19:12:47
*
**************************************************************************/
#include "TrainFeelModel.h"
#include "qw_time_util.h"
#include "backlight_module/backlight_module.h"

static time_t g_timer_max = 0;

TrainFeelModel::TrainFeelModel() :
	on_set_item_select_(this, &TrainFeelModel::on_set_item_select),
	on_enter_item_(this, &TrainFeelModel::on_enter_item),
    save_flag_(false)
{
    item_select_ = 0;
}

TrainFeelModel::~TrainFeelModel()
{

}


/**
 * @brief PageModel::setup() 进入页面通知
 */
void TrainFeelModel::setup()
{

}

/**
 * @brief PageModel::quit() 退出页面通知
 */
void TrainFeelModel::quit()
{

}

/**
 * @brief PageModel::notify() 更新检查, 在 PageBase::notify() 中调用
 */
void TrainFeelModel::notify()
{
    time_t now = get_boot_sec();
    if (now != g_timer_max)
    {
        g_timer_max = now;

        GUI_VIEW_LOG_E("[BUG:15205] TrainFeelModel::notify progress:%d save_flag:%d", 
            fit_load_progress_get(), save_flag_);
    }

    if (save_flag_)
    {
        if (fit_load_progress_get() == 100)
        {
            if(tick_ == INT_MAX)
            {
                tick_ = 0;
                return;
            }
            tick_++;
            if(tick_ > 42)  // 延时让动画飞一下 时间是宇宙的答案
            {
                GUI_VIEW_LOG_E("[BUG:15205] TrainFeelModel::GUI_EVT_SERVICE_POPOUT progress:%d tick:%d", 
                    fit_load_progress_get(), tick_);

                save_flag_ = false;
                backlight_open_app();
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CLOSE, reinterpret_cast<void*>(enumPOPUP_SAVE_RECORD));
                tick_ = INT_MAX;
            }
        }
    }
}


// Notification
void TrainFeelModel::on_set_item_select(uint8_t t1)
{
    item_select_ = t1;
}

void TrainFeelModel::on_enter_item(uint8_t t1)
{
    if (!save_flag_)
    {
        g_timer_max = get_boot_sec();
        save_flag_ = true;
        item_select_ = 0;
        item_enter_index_ = t1;
        set_activity_training_feel(static_cast<TRAINING_FEEL_TYPE>(t1));
        stop_activity_record(true); // 停止记录
    }
}

// Parameters function
