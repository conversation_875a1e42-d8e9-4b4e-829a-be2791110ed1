0000000000000000000000000000000000000000 5d000139581496f7af0f021cfeaa6e82ef1b40cf yanxuqiang <<EMAIL>> 1731998804 +0800
5d000139581496f7af0f021cfeaa6e82ef1b40cf 5d000139581496f7af0f021cfeaa6e82ef1b40cf yanxuqiang <<EMAIL>> 1731998851 +0800
5d000139581496f7af0f021cfeaa6e82ef1b40cf c1f7094ad4062b6da5d29b4d056b9cbc6af7b068 yanxuqiang <<EMAIL>> 1732074112 +0800	pull --rebase: Fast-forward
c1f7094ad4062b6da5d29b4d056b9cbc6af7b068 c1f7094ad4062b6da5d29b4d056b9cbc6af7b068 yanxuqiang <<EMAIL>> 1732081679 +0800	reset: moving to HEAD
c1f7094ad4062b6da5d29b4d056b9cbc6af7b068 fcc19c587b785e7bcb328b8f7b421e278325495f yanxuqiang <<EMAIL>> 1732081789 +0800	commit: Dial: 添加打开表盘bin文件相关文件，添加日常健康达标弹窗ui
fcc19c587b785e7bcb328b8f7b421e278325495f 3def83c6787ccd449fe66f0788879ef6e7a72c82 yanxuqiang <<EMAIL>> 1732081798 +0800	commit (amend): Dial: 添加打开表盘bin文件相关文件，添加日常健康达标弹窗ui
3def83c6787ccd449fe66f0788879ef6e7a72c82 c921ea2dbcc1f813044d24bbed7a09af2d4e530a yanxuqiang <<EMAIL>> 1732674100 +0800	pull --rebase: Fast-forward
c921ea2dbcc1f813044d24bbed7a09af2d4e530a c921ea2dbcc1f813044d24bbed7a09af2d4e530a yanxuqiang <<EMAIL>> 1732687100 +0800	reset: moving to HEAD
c921ea2dbcc1f813044d24bbed7a09af2d4e530a df0bb1a884aed6e1e0dab824af7cabdcb2e2e8b8 yanxuqiang <<EMAIL>> 1732687217 +0800	commit: GUI: 修改gui层lvgl读写文件接口，由于emmc特性读写文件要整512读写，如果按照用户输入的大小一次读写会有问题
df0bb1a884aed6e1e0dab824af7cabdcb2e2e8b8 96d79972a1aea1480a8eafc642836babd6a661f7 yanxuqiang <<EMAIL>> 1732687228 +0800	commit (amend): GUI: 修改gui层lvgl读写文件接口，由于emmc特性读写文件要整512读写，如果按照用户输入的大小一次读写会有问题
96d79972a1aea1480a8eafc642836babd6a661f7 e6ec490e641334b6ca56c4a39ee620f77b277080 yanxuqiang <<EMAIL>> 1732692675 +0800	pull --rebase (start): checkout e6ec490e641334b6ca56c4a39ee620f77b277080
e6ec490e641334b6ca56c4a39ee620f77b277080 e6ec490e641334b6ca56c4a39ee620f77b277080 yanxuqiang <<EMAIL>> 1732692675 +0800	pull --rebase (finish): returning to refs/heads/develop
e6ec490e641334b6ca56c4a39ee620f77b277080 f47d9025e3b86e24d4dca2fcdd31e6a366fca82e yanxuqiang <<EMAIL>> 1733105667 +0800	pull --rebase: Fast-forward
f47d9025e3b86e24d4dca2fcdd31e6a366fca82e 090e5f1b37d2fcc3fb8f78b1f7a9f6ce47966b0a yanxuqiang <<EMAIL>> 1733138761 +0800	pull --rebase: Fast-forward
090e5f1b37d2fcc3fb8f78b1f7a9f6ce47966b0a 0c50758903d3519381bcc7fbe6d364f7eecedca6 yanxuqiang <<EMAIL>> 1733204766 +0800	pull --rebase: Fast-forward
0c50758903d3519381bcc7fbe6d364f7eecedca6 0c50758903d3519381bcc7fbe6d364f7eecedca6 yanxuqiang <<EMAIL>> 1733212092 +0800	reset: moving to HEAD
0c50758903d3519381bcc7fbe6d364f7eecedca6 d3337f9f99a88b135b1a479c124ab1c07d506f0c yanxuqiang <<EMAIL>> 1733212108 +0800	commit: GUI: 对GraphScroll控件新增刷新标志，默认为true则刷新，false则不刷新
d3337f9f99a88b135b1a479c124ab1c07d506f0c 50eaedf4d3023154bf18425c07c2f8d311992adf yanxuqiang <<EMAIL>> 1733214952 +0800	commit: DIAL: 适配表盘获取心率接口
50eaedf4d3023154bf18425c07c2f8d311992adf 50eaedf4d3023154bf18425c07c2f8d311992adf yanxuqiang <<EMAIL>> 1733231064 +0800	reset: moving to HEAD
50eaedf4d3023154bf18425c07c2f8d311992adf cbe846e0763d68f246d7d3eb36a7e9fd39abe962 yanxuqiang <<EMAIL>> 1733231068 +0800	pull --rebase (start): checkout cbe846e0763d68f246d7d3eb36a7e9fd39abe962
cbe846e0763d68f246d7d3eb36a7e9fd39abe962 cbe846e0763d68f246d7d3eb36a7e9fd39abe962 yanxuqiang <<EMAIL>> 1733231191 +0800	rebase (finish): returning to refs/heads/develop
cbe846e0763d68f246d7d3eb36a7e9fd39abe962 cbe846e0763d68f246d7d3eb36a7e9fd39abe962 yanxuqiang <<EMAIL>> 1733231677 +0800	reset: moving to HEAD
cbe846e0763d68f246d7d3eb36a7e9fd39abe962 cbe846e0763d68f246d7d3eb36a7e9fd39abe962 yanxuqiang <<EMAIL>> 1733276175 +0800	reset: moving to HEAD
cbe846e0763d68f246d7d3eb36a7e9fd39abe962 99e4b3215d8e62beab88a9d2dfecd611435df87d yanxuqiang <<EMAIL>> 1733276180 +0800	pull --rebase: Fast-forward
99e4b3215d8e62beab88a9d2dfecd611435df87d 05fd5da337a4cdae295bee4f41b096dbe4ea5527 yanxuqiang <<EMAIL>> 1733276488 +0800	commit: DIAL: 移植sifli quickjs相关库到qw_pllatform
05fd5da337a4cdae295bee4f41b096dbe4ea5527 05fd5da337a4cdae295bee4f41b096dbe4ea5527 yanxuqiang <<EMAIL>> 1733292342 +0800	reset: moving to HEAD
05fd5da337a4cdae295bee4f41b096dbe4ea5527 0427b063c005799a75546bc9a295d18456d63f29 yanxuqiang <<EMAIL>> 1733292352 +0800	pull --rebase: Fast-forward
0427b063c005799a75546bc9a295d18456d63f29 **************************************** yanxuqiang <<EMAIL>> 1733292363 +0800	commit: [Code-Review]: 修正 sonarreview 报错问题
**************************************** d57f19dd8c8d529eed37b5dcbaf7ee2722eec116 yanxuqiang <<EMAIL>> 1733556678 +0800	pull --rebase: Fast-forward
d57f19dd8c8d529eed37b5dcbaf7ee2722eec116 d57f19dd8c8d529eed37b5dcbaf7ee2722eec116 yanxuqiang <<EMAIL>> 1733709676 +0800	reset: moving to HEAD
d57f19dd8c8d529eed37b5dcbaf7ee2722eec116 5e53214c40988ee578aaaa42b5ece5d7d13f826d yanxuqiang <<EMAIL>> 1733709705 +0800	commit: TopStatus: 新增状态栏显示样式，只显示一张图片
5e53214c40988ee578aaaa42b5ece5d7d13f826d cd43c8157996d4f4ded51c63143db1a4811e4a4b yanxuqiang <<EMAIL>> 1734620156 +0800	pull --rebase (start): checkout cd43c8157996d4f4ded51c63143db1a4811e4a4b
cd43c8157996d4f4ded51c63143db1a4811e4a4b cd43c8157996d4f4ded51c63143db1a4811e4a4b yanxuqiang <<EMAIL>> 1734620156 +0800	pull --rebase (finish): returning to refs/heads/develop
cd43c8157996d4f4ded51c63143db1a4811e4a4b 6e4d406b42197c1e675eea4b8b2a47cfdad52cca yanxuqiang <<EMAIL>> 1734686153 +0800	pull --rebase: Fast-forward
6e4d406b42197c1e675eea4b8b2a47cfdad52cca 4eaa9a210c9a503c7437e84fab8ea9237224a0b7 yanxuqiang <<EMAIL>> 1734933380 +0800	pull --rebase: Fast-forward
4eaa9a210c9a503c7437e84fab8ea9237224a0b7 9e045f02030746ff3a783fd343ac30e5ca8cd4c8 yanxuqiang <<EMAIL>> 1744290305 +0800	pull --rebase: Fast-forward
9e045f02030746ff3a783fd343ac30e5ca8cd4c8 04f580d4eb4a6080e9e60525c1f6d6f50d75a135 yanxuqiang <<EMAIL>> 1745392901 +0800	pull --rebase: Fast-forward
04f580d4eb4a6080e9e60525c1f6d6f50d75a135 ed2069b0b7c65b03cd5740b9577f06990604edee yanxuqiang <<EMAIL>> 1746498441 +0800	pull --rebase: Fast-forward
ed2069b0b7c65b03cd5740b9577f06990604edee a15fe551765d7b0c448084255fac586565a2ab3b yanxuqiang <<EMAIL>> 1746503351 +0800	pull --rebase: Fast-forward
a15fe551765d7b0c448084255fac586565a2ab3b a15fe551765d7b0c448084255fac586565a2ab3b yanxuqiang <<EMAIL>> 1746601067 +0800	reset: moving to HEAD
a15fe551765d7b0c448084255fac586565a2ab3b e8fb8cafec69954d52e822da6cf69993c73bf6a8 yanxuqiang <<EMAIL>> 1746601069 +0800	pull --rebase: Fast-forward
e8fb8cafec69954d52e822da6cf69993c73bf6a8 e8fb8cafec69954d52e822da6cf69993c73bf6a8 yanxuqiang <<EMAIL>> 1746689236 +0800	reset: moving to HEAD
e8fb8cafec69954d52e822da6cf69993c73bf6a8 7b7afe604021c619462e38e5ca79843ebdc09e77 yanxuqiang <<EMAIL>> 1746689240 +0800	pull --rebase: Fast-forward
7b7afe604021c619462e38e5ca79843ebdc09e77 7b7afe604021c619462e38e5ca79843ebdc09e77 yanxuqiang <<EMAIL>> 1746753412 +0800	reset: moving to HEAD
7b7afe604021c619462e38e5ca79843ebdc09e77 fed1ff4e138d437b72d1fc4612f48ed231db337f yanxuqiang <<EMAIL>> 1746753418 +0800	pull --rebase: Fast-forward
fed1ff4e138d437b72d1fc4612f48ed231db337f 56c70b70ed934b8875798c53533f4ece902a7067 yanxuqiang <<EMAIL>> 1746753659 +0800	commit: GUI: 新增表盘图片设置控件，新增一块1.5M缓存buffer,加速表盘加载速度
56c70b70ed934b8875798c53533f4ece902a7067 a81bfe37be9eb488a55271926b85e8fbd3dcbc33 yanxuqiang <<EMAIL>> 1746754176 +0800	commit (amend): GUI: 新增表盘图片设置控件，新增一块1.5M缓存buffer,加速表盘加载速度
a81bfe37be9eb488a55271926b85e8fbd3dcbc33 4f6b4e8ddc9d9c5d22fc7d3f1b67583fce5d1798 yanxuqiang <<EMAIL>> 1746755783 +0800	commit (amend): GUI: 新增表盘图片设置控件，新增一块1.5M缓存buffer,加速表盘加载速度
4f6b4e8ddc9d9c5d22fc7d3f1b67583fce5d1798 f9a410405a82fc5e8fa6c3fd1e4835e1704163aa yanxuqiang <<EMAIL>> 1747619039 +0800	pull --rebase (start): checkout f9a410405a82fc5e8fa6c3fd1e4835e1704163aa
f9a410405a82fc5e8fa6c3fd1e4835e1704163aa f9a410405a82fc5e8fa6c3fd1e4835e1704163aa yanxuqiang <<EMAIL>> 1747619039 +0800	pull --rebase (finish): returning to refs/heads/develop
f9a410405a82fc5e8fa6c3fd1e4835e1704163aa f9a410405a82fc5e8fa6c3fd1e4835e1704163aa yanxuqiang <<EMAIL>> 1747790845 +0800	reset: moving to HEAD
f9a410405a82fc5e8fa6c3fd1e4835e1704163aa c769267d633cf467833f9ead80ec9d97fe18844b yanxuqiang <<EMAIL>> 1747790850 +0800	pull --rebase: Fast-forward
c769267d633cf467833f9ead80ec9d97fe18844b 16900d77029b79d6aeacc26ed30b83737a671263 yanxuqiang <<EMAIL>> 1747790906 +0800	commit: GUI: 优化CachebleContainer draw逻辑
16900d77029b79d6aeacc26ed30b83737a671263 795fcac4e45589809a234918cc743cc89a85d788 yanxuqiang <<EMAIL>> 1747799736 +0800	commit: GUI: 表盘新增接口获取蓝牙连接状态
795fcac4e45589809a234918cc743cc89a85d788 1a5bbbd616b976ba7d0ca7124241ddf090067668 yanxuqiang <<EMAIL>> 1747829619 +0800	pull --rebase (start): checkout 1a5bbbd616b976ba7d0ca7124241ddf090067668
1a5bbbd616b976ba7d0ca7124241ddf090067668 1a5bbbd616b976ba7d0ca7124241ddf090067668 yanxuqiang <<EMAIL>> 1747829619 +0800	pull --rebase (finish): returning to refs/heads/develop
1a5bbbd616b976ba7d0ca7124241ddf090067668 86aeca0449f1a84de10bff39214a0bbb93434f3e yanxuqiang <<EMAIL>> 1747884840 +0800	pull --rebase: Fast-forward
86aeca0449f1a84de10bff39214a0bbb93434f3e f54d602881f797b884d2785192443b62ca70a8c9 yanxuqiang <<EMAIL>> 1747905689 +0800	pull --rebase: Fast-forward
f54d602881f797b884d2785192443b62ca70a8c9 f54d602881f797b884d2785192443b62ca70a8c9 yanxuqiang <<EMAIL>> 1747986080 +0800	reset: moving to HEAD
f54d602881f797b884d2785192443b62ca70a8c9 512c17a11e11895c17d0deb22d266c8ec77438cd yanxuqiang <<EMAIL>> 1747986085 +0800	pull --rebase: Fast-forward
512c17a11e11895c17d0deb22d266c8ec77438cd 512c17a11e11895c17d0deb22d266c8ec77438cd yanxuqiang <<EMAIL>> 1748241604 +0800	reset: moving to HEAD
512c17a11e11895c17d0deb22d266c8ec77438cd c65e660afb5ac4a71ef2e1492e440700408efda5 yanxuqiang <<EMAIL>> 1748241608 +0800	pull --rebase: Fast-forward
c65e660afb5ac4a71ef2e1492e440700408efda5 c65e660afb5ac4a71ef2e1492e440700408efda5 yanxuqiang <<EMAIL>> 1748249369 +0800	reset: moving to HEAD
c65e660afb5ac4a71ef2e1492e440700408efda5 1d3319073dcb7b437253e69863e799726f73fb80 yanxuqiang <<EMAIL>> 1748249375 +0800	pull --rebase: Fast-forward
1d3319073dcb7b437253e69863e799726f73fb80 52b38986024d9d7623ff49afd5cf565577ca5099 yanxuqiang <<EMAIL>> 1748249435 +0800	commit: GUI: 1.优化手势识别 2.表盘图片新增设置宽高接口 3.压力提醒弹窗优化
52b38986024d9d7623ff49afd5cf565577ca5099 0298860ca99513c47eae73b395b43b41a3fd85b0 yanxuqiang <<EMAIL>> 1748336024 +0800	pull --rebase (start): checkout 0298860ca99513c47eae73b395b43b41a3fd85b0
0298860ca99513c47eae73b395b43b41a3fd85b0 0298860ca99513c47eae73b395b43b41a3fd85b0 yanxuqiang <<EMAIL>> 1748336024 +0800	pull --rebase (finish): returning to refs/heads/develop
0298860ca99513c47eae73b395b43b41a3fd85b0 0298860ca99513c47eae73b395b43b41a3fd85b0 yanxuqiang <<EMAIL>> 1748482880 +0800	reset: moving to HEAD
0298860ca99513c47eae73b395b43b41a3fd85b0 7efdb44167f95235d7a05eb8f92edf59117b59dc yanxuqiang <<EMAIL>> 1748482886 +0800	pull --rebase: Fast-forward
7efdb44167f95235d7a05eb8f92edf59117b59dc 2cedc7b7ecfd090585bca1c88019caf0ab203184 yanxuqiang <<EMAIL>> 1748482990 +0800	commit: GUI: 1.新增表盘同步界面 2.新增app对表盘操作枚举
2cedc7b7ecfd090585bca1c88019caf0ab203184 426bbea2f004ce1bd4ffdec22c88934896c829e3 yanxuqiang <<EMAIL>> 1748522744 +0800	commit: GUI: 优化手势识别功能
426bbea2f004ce1bd4ffdec22c88934896c829e3 426bbea2f004ce1bd4ffdec22c88934896c829e3 yanxuqiang <<EMAIL>> 1748598699 +0800	reset: moving to HEAD
426bbea2f004ce1bd4ffdec22c88934896c829e3 3ea338242a4ae02f6fe7df8b6f997f3d1f82018c yanxuqiang <<EMAIL>> 1748598704 +0800	pull --rebase (start): checkout 3ea338242a4ae02f6fe7df8b6f997f3d1f82018c
3ea338242a4ae02f6fe7df8b6f997f3d1f82018c 3ea338242a4ae02f6fe7df8b6f997f3d1f82018c yanxuqiang <<EMAIL>> 1748598704 +0800	pull --rebase (finish): returning to refs/heads/develop
3ea338242a4ae02f6fe7df8b6f997f3d1f82018c 84288e80fad6f70cc516d4e04668311ff2ce8114 yanxuqiang <<EMAIL>> 1748598895 +0800	commit: GUI: 优化工具中心手势斜向上滑动时回弹问题
84288e80fad6f70cc516d4e04668311ff2ce8114 84288e80fad6f70cc516d4e04668311ff2ce8114 yanxuqiang <<EMAIL>> 1749633319 +0800	reset: moving to HEAD
84288e80fad6f70cc516d4e04668311ff2ce8114 0897bd8fd2d194dd74c577eea12712a21513ac00 yanxuqiang <<EMAIL>> 1749633324 +0800	pull --rebase: Fast-forward
0897bd8fd2d194dd74c577eea12712a21513ac00 80c0336a54ce9d59c0d73857c1492c18de2018bd yanxuqiang <<EMAIL>> 1749633371 +0800	commit: GUI: QwVerticalSwipeContainer控件新增动态销毁，生成功能
80c0336a54ce9d59c0d73857c1492c18de2018bd c6056f58ceb50d68f04f18a69be8b8e44d9edeef yanxuqiang <<EMAIL>> 1749634345 +0800	commit (amend): GUI: QwVerticalSwipeContainer控件新增动态销毁，生成功能
c6056f58ceb50d68f04f18a69be8b8e44d9edeef 240903abf0ea20f3ec9c58c3e7c538a1ab72379e yanxuqiang <<EMAIL>> 1749634669 +0800	pull --rebase (start): checkout 240903abf0ea20f3ec9c58c3e7c538a1ab72379e
240903abf0ea20f3ec9c58c3e7c538a1ab72379e 1785d87333f868e4452eebf9cf6d7343f7ce9a97 yanxuqiang <<EMAIL>> 1749634669 +0800	pull --rebase (pick): GUI: QwVerticalSwipeContainer控件新增动态销毁，生成功能
1785d87333f868e4452eebf9cf6d7343f7ce9a97 1785d87333f868e4452eebf9cf6d7343f7ce9a97 yanxuqiang <<EMAIL>> 1749634669 +0800	pull --rebase (finish): returning to refs/heads/develop
1785d87333f868e4452eebf9cf6d7343f7ce9a97 fe71ec92decab203405d1a7850b6923abd6c2cd4 yanxuqiang <<EMAIL>> 1749695385 +0800	pull --rebase (start): checkout fe71ec92decab203405d1a7850b6923abd6c2cd4
fe71ec92decab203405d1a7850b6923abd6c2cd4 fe71ec92decab203405d1a7850b6923abd6c2cd4 yanxuqiang <<EMAIL>> 1749695385 +0800	pull --rebase (finish): returning to refs/heads/develop
fe71ec92decab203405d1a7850b6923abd6c2cd4 fe71ec92decab203405d1a7850b6923abd6c2cd4 yanxuqiang <<EMAIL>> 1749789891 +0800	reset: moving to HEAD
fe71ec92decab203405d1a7850b6923abd6c2cd4 0e2bf922c4dbd67734bc03076b19229fe1c30867 yanxuqiang <<EMAIL>> 1749789898 +0800	pull --rebase: Fast-forward
0e2bf922c4dbd67734bc03076b19229fe1c30867 6eb44646104059924d726b75a171a41874ab13b8 yanxuqiang <<EMAIL>> 1749789956 +0800	commit: GUI: 优化swipe控件更新流程，优化canvas逻辑，加快截图速度
6eb44646104059924d726b75a171a41874ab13b8 6eb44646104059924d726b75a171a41874ab13b8 yanxuqiang <<EMAIL>> 1749889642 +0800	reset: moving to HEAD
6eb44646104059924d726b75a171a41874ab13b8 bcc0586d39ff3c28b0e991c5fde32c3428332f9a yanxuqiang <<EMAIL>> 1749889651 +0800	pull --rebase (start): checkout bcc0586d39ff3c28b0e991c5fde32c3428332f9a
bcc0586d39ff3c28b0e991c5fde32c3428332f9a 3fef842ad6e506fff29d74995bee2f6edfe233c1 yanxuqiang <<EMAIL>> 1749889651 +0800	pull --rebase (pick): GUI: 优化swipe控件更新流程，优化canvas逻辑，加快截图速度
3fef842ad6e506fff29d74995bee2f6edfe233c1 3fef842ad6e506fff29d74995bee2f6edfe233c1 yanxuqiang <<EMAIL>> 1749889651 +0800	pull --rebase (finish): returning to refs/heads/develop
3fef842ad6e506fff29d74995bee2f6edfe233c1 7490fe34397a61fab208d290999813a6bfcb5376 yanxuqiang <<EMAIL>> 1749889708 +0800	commit: gui: 修复swipe控件导致的死机问题
7490fe34397a61fab208d290999813a6bfcb5376 3fef842ad6e506fff29d74995bee2f6edfe233c1 yanxuqiang <<EMAIL>> 1749889992 +0800	reset: moving to 3fef842ad6e506fff29d74995bee2f6edfe233c1
3fef842ad6e506fff29d74995bee2f6edfe233c1 3fef842ad6e506fff29d74995bee2f6edfe233c1 yanxuqiang <<EMAIL>> 1749889998 +0800	reset: moving to HEAD
3fef842ad6e506fff29d74995bee2f6edfe233c1 8651eccc5aa5b05728b573192592ee957f977a87 yanxuqiang <<EMAIL>> 1749890004 +0800	pull --rebase (start): checkout 8651eccc5aa5b05728b573192592ee957f977a87
8651eccc5aa5b05728b573192592ee957f977a87 8651eccc5aa5b05728b573192592ee957f977a87 yanxuqiang <<EMAIL>> 1749890004 +0800	pull --rebase (finish): returning to refs/heads/develop
8651eccc5aa5b05728b573192592ee957f977a87 a4ecf4b9bc7d855846bd60204823be146cf71cb5 yanxuqiang <<EMAIL>> 1749890019 +0800	commit: gui: 修复swipe控件导致的死机问题
a4ecf4b9bc7d855846bd60204823be146cf71cb5 56abd4d1438211f8bf20daaaf66afcfbc5f55eb2 yanxuqiang <<EMAIL>> 1749890336 +0800	commit (amend): gui: 修复swipe控件导致的死机问题
