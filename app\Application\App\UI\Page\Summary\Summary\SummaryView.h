/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SummaryView.h
@Time    :   2025-01-08 17:43:51
*
**************************************************************************/

#pragma once
#include "SummaryViewModel.h"
#include "QwVerticalSwipe/QwVerticalSwipeContainer.h"
#include "Summary/SummarySport/SummarySportContainer.h"
#include "Summary/SummaryDetail/SummaryDetailContainer.h"
#include "Summary/SummaryChart/SummaryChartContainer.h"
#include "Summary/SummaryZone/SummaryZoneContainer.h"
#include "Summary/SummaryLap/SummaryLapContainer.h"
#include "Summary/SummaryIntervalTrain/SummaryIntervalTrainCon.h"
#include "Summary/SummaryWorkout/RecoveryTimeCon.h"
#include "Summary/SummaryWorkout/SummaryWorkoutRet.h"
#include "Summary/SummaryWorkout/SummaryVo2max.h"
#define PAGE_NUM_MAX 18
class SummaryView : public PageView
{
private:
	int chart_index_;
	int zone_index_;

	// Drawable
    Box bg_;
    QwVerticalSwipeContainer swipe_;
	SummarySportContainer summary_sport_con_;
	SummaryDetailContainer summary_detail_con_;
	SummaryChartContainer summary_chart_con_[8]; // 8种图表类型
	SummaryZoneContainer summary_zone_con_[3]; // 3种区间类型
	SummaryLapContainer summary_lap_con_;
	SummaryIntervalTrainCon summary_interval_train_con_;
	RecoveryTimeCon recovery_time_con_;//恢复时间
	SummaryWorkoutRet summary_workout_con_;
	SummaryVo2max summary_vo2max_con_;

	// Notification bak
	Notification<uint32_t>* p_set_fit_name_;
	Notification<int&>* p_get_lap_count_;
	Notification<int, SummaryModel::LAP_INFO&>* p_get_lap_info_;
	Notification<int16_t&>* p_get_summary_data_count_;
	Notification<int, int16_t&>* p_get_summary_data_;
	Notification<int, uint8_t&, uint32_t&>* p_get_zone_val_;
	Notification<bool>* p_set_change_chart_type_;
	Notification<const char*>* p_set_route_page_name_;

	// ObserverDrawable bak
	ObserverDrawable<Drawable, SPORTTYPE, 1>* p_sport_type_;
	ObserverDrawable<Drawable, const char*, 1>* p_fit_time_;
	ObserverDrawable<Drawable, SummaryModel::SPORT_INFO, (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL>* p_summary_sport_data_;
	ObserverDrawable<Drawable, QwItemSummary::INFO, MAX_SUMMARY_NUMBER>* p_summary_detail_data_;
	ObserverDrawable<Drawable, FIT_CHART_SECTION, 1>* p_chart_type_;
	ObserverDrawable<Drawable, const char*, 1>* p_title_;
	ObserverDrawable<Drawable, const char*, 1>* p_left_;
	ObserverDrawable<Drawable, const char*, 1>* p_right_;
	ObserverDrawable<Drawable, const char*, 1>* p_total_;
	ObserverDrawable<Drawable, uint32_t, 1>* p_timer_;
	ObserverDrawable<Drawable, const char*, 1>* p_route_page_name_;
	ObserverDrawable<Drawable, SummaryModel::TRAIN_INFO, 1>* p_train_info_;

	// Drawable Update
	Callback<SummaryView, Drawable*, Parameters<SPORTTYPE>*, int> update_sport_type_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_fit_time_;
	Callback<SummaryView, Drawable*, Parameters<SummaryModel::SPORT_INFO>*, int> update_summary_sport_data_;
	Callback<SummaryView, Drawable*, Parameters<QwItemSummary::INFO>*, int> update_summary_detail_data_;
	Callback<SummaryView, Drawable*, Parameters<FIT_CHART_SECTION>*, int> update_chart_type_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_title_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_left_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_right_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_total_;
	Callback<SummaryView, Drawable*, Parameters<uint32_t>*, int> update_timer_;
	Callback<SummaryView, Drawable*, Parameters<const char*>*, int> update_route_page_name_;
	Callback<SummaryView, Drawable*, Parameters<SummaryModel::TRAIN_INFO>*, int> update_train_info_;

	uint8_t page_count_;
	uint8_t page_type[PAGE_NUM_MAX];
	QwSwipePageBase *page_base_[PAGE_NUM_MAX];
	QwSwipePageBase chart_page_base_[PAGE_NUM_MAX];
	lv_img_dsc_t cache_img_dsc[3];
	Image img_[3];

	// custom variables
	// Dynamic caching test variables
	Callback<SummaryView, QwSwipePageBase*, int16_t> pageUpdateDemonCallback_;


protected:

public:
	SummaryView(PageManager* manager);
	virtual ~SummaryView();

	// PageView override
	void setup() override;
	void quit() override;

	// Screen override
	void handleTickEvent() override;
	void handleKeyEvent(uint8_t c) override;
	void handleClickEvent(const ClickEvent& evt) override;
	void handleDragEvent(const DragEvent& evt) override;
	void handleGestureEvent(const GestureEvent& evt) override;

	// Notification Callback function
	void set_on_set_fit_name(Notification<uint32_t>* command);

	void set_on_get_lap_count(Notification<int&>* command);

	void set_on_get_lap_info(Notification<int, SummaryModel::LAP_INFO&>* command);

	void set_on_get_summary_data_count(Notification<int16_t&>* command);

	void set_on_get_summary_data(Notification<int, int16_t&>* command);

	void set_on_get_zone_val(Notification<int, uint8_t&, uint32_t&>* command);

	void set_on_set_change_chart_type(Notification<bool>* command);

	void set_on_set_route_page_name(Notification<const char*>* command);

	// ObserverDrawable Callback function
	void set_update_sport_type(ObserverDrawable<Drawable, SPORTTYPE, 1>* observer);
	void update_sport_type(Drawable* ctrl, Parameters<SPORTTYPE>* data, int idx);

	void set_update_fit_time(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_fit_time(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_summary_sport_data(ObserverDrawable<Drawable, SummaryModel::SPORT_INFO, (int)SummaryModel::SUMMARY_SPORT_TYPE::__TOTAL>* observer);
	void update_summary_sport_data(Drawable* ctrl, Parameters<SummaryModel::SPORT_INFO>* data, int idx);

	void set_update_summary_detail_data(ObserverDrawable<Drawable, QwItemSummary::INFO, MAX_SUMMARY_NUMBER>* observer);
	void update_summary_detail_data(Drawable* ctrl, Parameters<QwItemSummary::INFO>* data, int idx);

	void set_update_chart_type(ObserverDrawable<Drawable, FIT_CHART_SECTION, 1>* observer);
	void update_chart_type(Drawable* ctrl, Parameters<FIT_CHART_SECTION>* data, int idx);

	void set_update_title(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_title(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_left(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_left(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_right(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_right(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_total(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_total(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_timer(ObserverDrawable<Drawable, uint32_t, 1>* observer);
	void update_timer(Drawable* ctrl, Parameters<uint32_t>* data, int idx);

	void set_update_route_page_name(ObserverDrawable<Drawable, const char*, 1>* observer);
	void update_route_page_name(Drawable* ctrl, Parameters<const char*>* data, int idx);

	void set_update_train_info(ObserverDrawable<Drawable, SummaryModel::TRAIN_INFO, 1>* observer);
	void update_train_info(Drawable* ctrl, Parameters<SummaryModel::TRAIN_INFO>* data, int idx);

	// custom functions
	// Dynamic caching test functions
	void updateTestPageDemonContent(QwSwipePageBase* page, int16_t pageIndex);
	bool create_summary_snapshort(int index, void *buf);
	bool is_summary_snapshort_exit(int index);
	bool get_summary_snapshort_buf_app(int index, void *buf);
	bool get_summary_snapshort_buf_win32(int index, void *buf);
	bool get_summary_snapshort_buf(int index, void *buf);
	
};

