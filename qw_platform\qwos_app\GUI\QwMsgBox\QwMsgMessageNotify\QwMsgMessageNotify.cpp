/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwMsgMessageNotify.cpp
@Time    :   2024/12/10 19:28:45
*
**************************************************************************/

#include "QwMsgMessageNotify.h"
#include "Image/images.h"
#include "MvcApp.h"
#include "view_page_model.h"
#include "poweroff_ctrl.h"
#include "basic_app.h"
#include "GUIMsg/MsgBoxService.h"
#include "GUI/PageManager/PageManager.h"
#include "qw_time_util.h"

QwMsgMessageNotify::QwMsgMessageNotify() :
    push_message_details_(nullptr),
    start_tick_(0),
    title_font_(nullptr),
    text_font_(nullptr),
    sub_text_wide_text_action_(WIDE_TEXT_WORDWRAP_DOT),
	select_item_pos_(this, &QwMsgMessageNotify::select_item_pos)
{
    // info_.priority = MSG_PRIORITY::TIP_1;
    // info_.scope = (uint16_t) MSG_SCOPE::ALL;
    info_.msg = "QwMsgMessageNotify";
    // info_.msg_page_type = PAGE_TYPE::MSG_TYPE_FULL_TIP;

    qw_msg_message_notify_info_t* info = &msg_message_notify_info_;
    info->sub_title = nullptr;
    info->sub_text = nullptr;

    memset(&info->qhst_icon_info, 0, sizeof(info->qhst_icon_info));
}

void QwMsgMessageNotify::setup()
{
    setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    add(halfscreen_);
    halfscreen_.set_reset();
    halfscreen_.set_select_pos_handle(select_item_pos_);
    halfscreen_.setup();

	update_parameter();

    halfscreen_.on_notify();
}

void QwMsgMessageNotify::on_notify()
{
    halfscreen_.set_reset();
	update_parameter();

    halfscreen_.on_notify();
}

void QwMsgMessageNotify::update_parameter()
{
	set_info_parameter();

    qw_msg_message_notify_info_t* info = &msg_message_notify_info_;

    start_tick_ = get_boot_msec();

    halfscreen_.setPosition(*this);
    halfscreen_.set_type(QHST_ICON);

    if(info->sub_title)
    {
        halfscreen_.set_sub_title_type(info->sub_title_type);
        rt_kprintf("sub_title:%s\n", info->sub_title);
        halfscreen_.set_typed_dynamic_sub_title(info->sub_title);
        if(title_font_ != nullptr)
        {
            halfscreen_.set_sub_title_font(title_font_);
        }
    }

    if(info->sub_text)
    {
        halfscreen_.set_typed_dynamic_sub_text(info->sub_text);
        if(text_font_ != nullptr)
        {
            halfscreen_.set_sub_text_font(text_font_);
        }
        halfscreen_.set_sub_text_wide_text_action(sub_text_wide_text_action_);
    }

    Container* high_con = halfscreen_.get_high_contaner();
    QhstIcon* icon = dynamic_cast<QhstIcon*>(high_con);
    qhst_icon_t* icon_info = &info->qhst_icon_info;
    if(icon_info->img_)
    {
        icon->set_img_bitmap(Bitmap(icon_info->img_));
    }
}

void QwMsgMessageNotify::force_close()
{
}

void QwMsgMessageNotify::handleTickEvent()
{
    halfscreen_.handleTickEvent();

    // qw_msg_message_notify_info_t* info = &msg_message_notify_info_;
    // if(get_boot_msec() - start_tick_ > info->time)
    // {
    //     if (self_close_cb_ != nullptr && self_close_cb_->isValid())
    //     {
    //         self_close_cb_->execute((uint8_t) ANIMAT_TYPE::NORTH);
    //     }
    // }
}

void QwMsgMessageNotify::handleKeyEvent(uint8_t c)
{
    if(c == KEY_CLK_START)
    {
        if(push_message_details_ && push_message_details_->isValid())
        {
            push_message_details_->execute();
        }
    }

    if(c == KEY_CLK_BACK || c == KEY_CLK_START || c == KEY_CLK_POWER
        || c == KEY_HOLD_BACK || c == KEY_HOLD_POWER)
    {
        if (self_close_cb_ != nullptr && self_close_cb_->isValid())
        {
            self_close_cb_->execute((uint8_t) ANIMAT_TYPE::NORTH);
        }
    }
}

void QwMsgMessageNotify::handleClickEvent(const touchgfx::ClickEvent& evt)
{
    halfscreen_.handleClickEvent(evt);
}

void QwMsgMessageNotify::handleDragEvent(const touchgfx::DragEvent& evt)
{
    halfscreen_.handleDragEvent(evt);
}

void QwMsgMessageNotify::handleGestureEvent(const touchgfx::GestureEvent& evt)
{
    halfscreen_.handleGestureEvent(evt);
}

qw_msg_message_notify_info_t* QwMsgMessageNotify::get_msg_message_notify_info()
{
    return &msg_message_notify_info_;
}

void QwMsgMessageNotify::select_item_pos(void* item, int x, int y)
{
    if(y < HAL::DISPLAY_HEIGHT / 2)
    {
        if(push_message_details_ && push_message_details_->isValid())
        {
            push_message_details_->execute();
        }

        if (self_close_cb_ != nullptr && self_close_cb_->isValid())
        {
            self_close_cb_->execute((uint8_t) ANIMAT_TYPE::NORTH);
        }
    }
}

void QwMsgMessageNotify::set_push_message_details_handle(GenericCallback<>& notify)
{
    push_message_details_ = &notify;
}

void QwMsgMessageNotify::set_title_font(lv_font_t *font_)
{
    title_font_ = font_;
}

void QwMsgMessageNotify::set_text_font(lv_font_t *font_)
{
    text_font_ = font_;
}

/**
 * @brief 设置副文本的换行模式
 * @param action 换行模式
 */
void QwMsgMessageNotify::set_sub_text_wide_text_action(WideTextAction action)
{
    sub_text_wide_text_action_ = action;
}
