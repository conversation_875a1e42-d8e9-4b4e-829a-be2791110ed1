/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   navi_srv_module.c
@Time    :   2024/12/23 15:21:27
<AUTHOR>   nullptr
*
**************************************************************************/
#include "navi_srv_module.h"
#include "cfg_navigation.h"
#include "cfg_sportspage.h"
#include "cfg_time.h"
#include "fit_file_manager.h"
#include "gui_event_service.h"
#include "navi_file_manager.h"
#include "navi_port.h"
#include "qw_log.h"
#include "qw_time_util.h"
#include "QwMsgBottomToast/QwMsgBottomToast.h"

#define MAX_UINT32 4294967295U
static navi_srv_t navi_srv = {0};

NAVI_PREPARE_STATUS navi_srv_get_prepare_status(void)
{
    return navi_srv.status;
}

void navi_srv_set_prepare_status(NAVI_PREPARE_STATUS status)
{
    navi_srv.status = status;
    QW_LOG_D("NAVI_SRV:", "navi_srv status-->%d\n", navi_srv.status);
}

void navi_srv_set_enter_mode(NAVI_SRV_ENTER_WAY mode)
{
    navi_srv.enter_navi_menus_mode = mode;
}

NAVI_SRV_ENTER_WAY navi_srv_get_enter_mode(void)
{
    return navi_srv.enter_navi_menus_mode;
}

void navi_srv_set_preview_file_index(uint8_t index, uint8_t type)
{
    navi_srv.preview_file_index = index;
    navi_srv.preview_file_type = type;
}

void navi_srv_get_preview_file_index(uint8_t *index, uint8_t *type)
{
    *index = navi_srv.preview_file_index;
    *type = navi_srv.preview_file_type;
}

void navi_srv_get_preview_file_path(char *file_path)
{
    if (navi_srv.preview_file_type == 0)
    {
        navi_get_file_name(navi_srv.preview_file_index, file_path);
    }
    else
    {
        fit_activity_get_file_name(navi_srv.preview_file_index, file_path);
    }
}

void navi_srv_get_preview_file_name(char *file_name)
{
    if (navi_srv.preview_file_type == 0)
    {
        navi_brief_t file_info;
        navi_get_file_brief(navi_srv.preview_file_index, &file_info);
        sprintf(file_name, "%s", file_info.name);
    }
    else
    {
        uint32_t fittime = 0;
        fit_activity_get_file_fittime(navi_srv.preview_file_index, &fittime);
        sprintf(file_name, "%s", get_localtime_str(fittime + FIT_BASE_TIME_OFFSET));
    }
    navi_srv_filter_file_ext(file_name);
}

void navi_srv_filter_file_ext(char name[])
{
    char *last_dot = strchr(name, '.');   // 找到第一个点号的位置

    if (last_dot == NULL)
    {
        return;
    }

    // 检查点号是否在文件名末尾（如“file.”）
    if (last_dot[1] == '\0')
    {
        *last_dot = '\0';
        return;
    }

    *last_dot = '\0';
}

static float navi_srv_alt_data[NAVI_SRV_ALT_DATA_MAX_COUNT] = {0};

void navi_srv_convert_height_data(float **p_data, uint32_t *count)
{
    const NaviClimbSample *temp_alt_data;
    temp_alt_data = navi_climb_sample_get();
    if (temp_alt_data == NULL)
    {
        *p_data = NULL;
        *count = 0;
        return;
    }
    *count = temp_alt_data->len;
    for (int i = 0; i < temp_alt_data->len; i++)
    {
        navi_srv_alt_data[i] = temp_alt_data->buf[i].alt;
    }
    *p_data = navi_srv_alt_data;
}

static record_simple_t navi_srv_wp_data[NAVI_SRV_WayPoint_DATA_MAX_COUNT] = {0};

void navi_srv_convert_waypoint_data(record_simple_t **p_out, uint32_t *count)
{
    const NaviRouteWpSample *temp_wp_data;
    temp_wp_data = navi_route_wp_sample_get();
    if (temp_wp_data == NULL)
    {
        *p_out = NULL;
        return;
    }

    *count = temp_wp_data->len;
    for (int i = 0; i < temp_wp_data->len; i++)
    {
        navi_srv_wp_data[i].gps_lon = (FIT_SINT32) (temp_wp_data->wpdc_buf[i].lng * 10000000);
        navi_srv_wp_data[i].gps_lat = (FIT_SINT32) (temp_wp_data->wpdc_buf[i].lat * 10000000);
    }
    *p_out = navi_srv_wp_data;
}

static void navi_yaw_tip_cb(navi_status_t state)
{
    static bool is_off_course = false;           // 记录当前是否处于偏航状态
    static bool off_course_notified = false;     // 记录是否已经提醒过偏航
    static int64_t last_off_course_notify = 0;   // 记录上次偏航提醒的时间
    static bool restore_notified = false;        // 记录是否已经提醒过恢复路线

    if (state == enumNAVI_STATUS_OFF_COURSE)     // 偏航状态
    {
        if (!is_off_course)                      // 刚进入偏航状态
        {
            is_off_course = true;
            off_course_notified = false;
            restore_notified = false;
        }

        int64_t current_time = get_boot_sec();

        // 首次偏航提醒或距离上次提醒已超过3分钟
        if (!off_course_notified || (current_time - last_off_course_notify > 3 * 60))
        {
            // 发送偏航提醒
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_YAW, NULL);

            // 更新状态
            off_course_notified = true;
            last_off_course_notify = current_time;
        }
    }
    else if (state == enumNAVI_STATUS_NORMAL)     // 正常状态
    {
        if (is_off_course && !restore_notified)   // 从偏航状态恢复且未提醒
        {
            // 发送恢复路线提醒
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_RESTORE_ROUTE, NULL);

            // 更新状态
            restore_notified = true;
            is_off_course = false;
            off_course_notified = false;
        }
        else if (!is_off_course)   // 一直处于正常状态
        {
            // 重置所有状态变量，确保下次偏航时能正确触发
            restore_notified = false;
            off_course_notified = false;
        }
    }
}

void navi_srv_update_turning_info(const navi_progress_t *draw_data)
{
    if (!draw_data)
    {
        return;
    }

    //判断是否在导航地图页面
    bool is_router_page = false;
    if (get_current_webgrid_page_type() == GRAPH_PAGE_NAVI)
    {
        is_router_page = true;
    }
    //判断运动类型
    bool is_bike_sport = false;
    if (get_current_sport_mode() >= SPORTSTYPE_CYCLING && get_current_sport_mode() <= SPORTSTYPE_TRIP_CYCLING)
    {
        is_bike_sport = true;
    }

    /*
    提醒距离
               骑行          非骑行
    路线页    500、200      200、50
    非路线页     200、        50、
    */

    // 判断偏航
    navi_yaw_tip_cb(draw_data->status);

    // 判断转向
    if (!get_navi_turn_tip() || draw_data->turn.type == enumNAVI_TURN_GO_STRAIGHT)
    {
        return;
    }

    static uint32_t last_turn_idx = MAX_UINT32;         // 记录上一次的转向索引
    static uint32_t last_notified_distance_range = 0;   // 记录上一次提醒的距离范围：0=未提醒，1=远距离提醒，2=近距离提醒
    navi_turn_type_t turn_type = draw_data->turn.type;      // 转向类型
    uint32_t turn_dist = (uint32_t) draw_data->turn.dist;   // 距离

    // 转向有效性检查
    if (turn_dist > 500 || turn_dist < 1 || draw_data->turn.is_valid == false)
    {
        // 如果之前有提醒，现在关闭
        if (last_notified_distance_range != 0)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CLOSE, (void *) enumPOPUP_NAVI_CHANGE_DIR);
            last_notified_distance_range = 0;
        }
        return;
    }

    // 当转向索引变化时，重置状态
    if (last_turn_idx != draw_data->turn.idx)
    {
        last_turn_idx = draw_data->turn.idx;
        last_notified_distance_range = 0;   // 重置提醒状态
    }

    // 根据不同场景设置阈值
    uint32_t far_threshold = 0;            // 远距离阈值
    uint32_t near_threshold = 0;           // 近距离阈值
    bool need_far_notify = false;          // 是否需要远距离提醒

    if (is_bike_sport && is_router_page)   // 骑行运动 + 导航页
    {
        far_threshold = 500;
        near_threshold = 200;
        need_far_notify = true;
    }
    else if (!is_bike_sport && is_router_page)   // 非骑行运动 + 导航页
    {
        far_threshold = 200;
        near_threshold = 50;
        need_far_notify = true;
    }
    else if (is_bike_sport && !is_router_page)   // 骑行运动 + 非导航页
    {
        near_threshold = 200;
        need_far_notify = false;
    }
    else   // 非骑行运动 + 非导航页
    {
        near_threshold = 50;
        need_far_notify = false;
    }



    // 远距离提醒逻辑
    if (need_far_notify && turn_dist <= far_threshold && turn_dist > near_threshold && last_notified_distance_range == 0)
    {
        // 准备弹窗信息
        static qw_msg_bottom_toast_info_t info = {0};
        info.turn_type = turn_type;
        info.distance = turn_dist;
        info.time_flag_ = true;
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_CHANGE_DIR, &info);
        last_notified_distance_range = 1;   // 标记已进行远距离提醒
    }
    // 近距离提醒逻辑
    else if (turn_dist <= near_threshold && last_notified_distance_range != 2)
    {
        // 准备弹窗信息
        static qw_msg_bottom_toast_info_t info = {0};
        info.turn_type = turn_type;
        info.distance = turn_dist;
        info.time_flag_ = false;
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_CHANGE_DIR, &info);
        last_notified_distance_range = 2;   // 标记已进行近距离提醒
    }
}

void navi_srv_start_arrive_remind(bool start_or_arrive)
{
    if (start_or_arrive)
    {
        //TODO 开始提醒
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_START, NULL);   // 导航开始
    }
    else
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_END, NULL);   // 导航结束
    }
}

void navi_srv_climb_remind(void)
{
    static qw_msg_bottom_toast_info_t info = {0};
    info.remaining_climb = 100;   //单位m
    submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_NAVI_REMAINING_CLIMB, &info);
}

bool navi_srv_is_support_sport_type(SPORTTYPE type)
{
    return (type == SPORTSTYPE_RUNNING ||
        type == SPORTSTYPE_TRAIL_RUNNING ||
        type == SPORTSTYPE_WALKING ||
        type == SPORTSTYPE_CYCLING ||
        type == SPORTSTYPE_ROAD_CYCLING ||
        type == SPORTSTYPE_MOUNTAIN_CYCLING ||
        type == SPORTSTYPE_COMMUTING ||
        type == SPORTSTYPE_TRIP_CYCLING ||
        type == SPORTSTYPE_MOUNTAINEERING ||
        type == SPORTSTYPE_HIKING);
}

void navi_srv_set_last_preview_info(navi_last_preview_t *info)
{
    navi_srv_filter_file_ext(info->file_name);
    memcpy(&navi_srv.last_info, info, sizeof(navi_last_preview_t));
    navi_srv.last_info.had_preview = true;
}

bool navi_srv_get_last_preview_info(navi_last_preview_t *info)
{
    if (!navi_srv.last_info.had_preview)
    {
        info = NULL;
        return false;
    }
    memcpy(info, &navi_srv.last_info, sizeof(navi_last_preview_t));
    return true;
}
