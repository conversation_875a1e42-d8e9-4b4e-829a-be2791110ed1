/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ItemSensorCheck.cpp
@Time    :   2024/12/10 19:27:13
*
**************************************************************************/

#include "limits.h"
#include "stdint.h"
//#include "AppList.hpp"
#include "ItemSensorCheck.h"
#include "Image/images.h"
//#include "GUICtrl/AppList/AppItem/AppListTypes.h"
//#include "GUICtrl/AppList/AppListView.hpp"
#include "QwMenuItem/QwMenuItem.h"
#include "QwMenuItem/QwMenuView.hpp"

static const char g_sensor_default_title[] = "_connected";
TM_KEY(g_sensor_default_title)

static const char* g_sensor_check_name[] = {
	"_ant+",
	"_heart_rate",
	"_speed&bike_cadence",
	"_power",
	"",
	"_bike_cadence",
	"_speed",
	"_shifter",
	"_di2",
	"_cycling_platform",
	"_lev",
	"_running_dynamics_sensor",
	"_radar",
	"_headlight",
};
TM_DECLARE(g_sensor_check_name)

ItemSensorCheck::ItemSensorCheck() :
	index_(INT_MAX),
	focus_(false),
	check_box_state_(false)
	//state_base_(nullptr)
{};

void ItemSensorCheck::setup(void* info)
{
	item_info_t* item_info = (item_info_t*)info;
	if (item_info == nullptr)
	{
		assert("[ItemSensorShow:8001]ItemSensorShow info is nullptr");
	}
	if (item_info->sensor_info != nullptr)
	{
		memcpy(&sensor_info_, item_info->sensor_info, sizeof(sensor_find_info_t));
	}

	//state_base_ = item_info->item_state_base;
	index_ = item_info->item_index;

	add(switch_);
	switch_.setWidthHeight(64, 64);
	switch_.setAlign(ALIGN_IN_LM, LEFT_TO_IMAGE_GAP);
	Bitmap bitmap_open(&custom_repeat_time_checked);
	Bitmap bitmap_close(&custom_repeat_time_unchecked);
	switch_.setBitmap(bitmap_close);
	check_box_state_ = false;

	//if (state_base_ != nullptr)
	//{
	//	if (state_base_->get_item_state(index_))
	//	{
	//		switch_.setBitmap(bitmap_open);
	//		check_box_state_ = true;
	//	}
	//	else
	//	{
	//		switch_.setBitmap(bitmap_close);
	//		check_box_state_ = false;
	//	}
	//}
	switch_.invalidate();

	add(text_con_);
	text_con_.setWidthHeight(getWidth(), getHeight());

	text_con_.add(text_title_);
	text_con_.add(text_subtitle_);
	text_title_.setPosition(0, 0, MENU_IMAGE_TITLE_WIDTH, MENU_TITLE_HIGHT);
	text_title_.setTextFont(&MENU_TITLE_FONT);
	text_title_.setTextAlignment(LEFT);
	text_title_.setTextVerticalAlignment(ALIGN_Y_MID);
	text_title_.setLabelAlpha(LV_OPA_TRANSP);
	text_title_.setColor(lv_color_hex(MENU_TITLE_COLOR));
	text_title_.setAlpha(MENU_TITLE_COLOR_ALPHA);
	if (item_info->sensor_info == nullptr)
	{
		text_title_.setTypedDynamicText(_TM(g_sensor_default_title));
	}
	else
	{
		// rt_kprintf("sensor_info_.show_info.ble_nam:%s\n", sensor_info_.show_info.ble_name);
		if (strlen(sensor_info_.show_info.ble_name))
		{
			text_title_.setTypedDynamicText(sensor_info_.show_info.ble_name);
		}
		else
		{
			text_title_.setTypedDynamicText(_TM(g_sensor_check_name[(int)sensor_info_.key_info.type]));
		}
	}
	// text_title_.resizeHeightToCurrentText();

	text_subtitle_.setPosition(0, 0, MENU_IMAGE_TITLE_WIDTH, MENU_SUB_TEXT_HIGHT);
	text_subtitle_.setTextFont(&MENU_SUB_TEXT_FONT);
	text_subtitle_.setTextAlignment(LEFT);
	text_subtitle_.setLabelAlpha(LV_OPA_TRANSP);
	text_subtitle_.setColor(lv_color_hex(MENU_SUB_TEXT_COLOR));
	text_subtitle_.setAlpha(MENU_SUB_TEXT_COLOR_ALPHA);
	text_subtitle_.setVisible(true);
	if (item_info->sensor_info == nullptr)
	{
		text_subtitle_.setHeight(0);
	}
	else
	{
		char show_str[30] = { 0 };
		if (sensor_info_.key_info.channel == SEN_DEF_CHANEL_ANT)
		{
			sprintf(show_str, "ANT+:%d", sensor_info_.key_info.key.ant_id);
			text_subtitle_.setTypedDynamicText(show_str);
		}
		else if (sensor_info_.key_info.channel == SEN_DEF_CHANEL_BLE)
		{
			sprintf(show_str, "%02X:%02X:%02X:%02X:%02X:%02X", sensor_info_.key_info.key.ble_mac[5], sensor_info_.key_info.key.ble_mac[4],
				sensor_info_.key_info.key.ble_mac[3], sensor_info_.key_info.key.ble_mac[2], sensor_info_.key_info.key.ble_mac[1], sensor_info_.key_info.key.ble_mac[0]);
			text_subtitle_.setTypedDynamicText(show_str);
		}
		//text_subtitle_.resizeHeightToCurrentText();
	}

	text_con_.setWidth(text_title_.getWidth() > text_subtitle_.getWidth() ? text_title_.getWidth() : text_subtitle_.getWidth());
	text_con_.setHeight(text_title_.getHeight() + text_subtitle_.getHeight() + (text_subtitle_.getHeight() > 0 ? TITLE_TO_SUBTEXT_GAP : 0));
	text_con_.setAlignTo(switch_, ALIGN_OUT_RM, IMAGE_TO_TEXT_GAP);
	text_title_.setAlign(ALIGN_IN_LT);
	text_subtitle_.setAlign(ALIGN_IN_LB);
    text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
    text_subtitle_.resizeHeightToCurrentText();
    text_title_.resizeHeightToCurrentText();

	void* img_data = nullptr;
	if (sensor_info_.show_info.rssi == SEN_DEF_RSSI_LOW)
	{
		img_data = (void*)&signal_low;
	}
	else if (sensor_info_.show_info.rssi == SEN_DEF_RSSI_NORMAL)
	{
		img_data = (void*)&signal_normal;
	}
	else if (sensor_info_.show_info.rssi == SEN_DEF_RSSI_FULL)
	{
		img_data = (void*)&signal_full;
	}

	if (img_data)
	{
		add(sensor_signal_);
		sensor_signal_.setWidthHeight(64, 64);
		sensor_signal_.setBitmap(Bitmap(img_data));
		sensor_signal_.setAlign(ALIGN_IN_RM, IMAGE_TO_RIGHT_GAP);
	}
	if(item_info->is_selected)
    {
        Bitmap bitmap_open(&custom_repeat_time_checked);
        switch_.setBitmap(bitmap_open);
	}
	else
	{
        Bitmap bitmap_close(&custom_repeat_time_unchecked);
        switch_.setBitmap(bitmap_close);
	}
	switch_.invalidate();

	//gfx_printf("focus_type_:%d\n", index_);
};

void ItemSensorCheck::quit()
{
    focus_update(false);
	text_con_.removeAll();
	removeAll();
	//state_base_ = nullptr;
	memset(&sensor_info_, 0, sizeof(sensor_find_info_t));
};

void ItemSensorCheck::focus_update(bool focus)
{
	if (focus_ != focus)
	{
		focus_ = focus;
		if (focus_)
		{
			text_title_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
			text_subtitle_.setWideTextAction(WIDE_TEXT_CHARWRAPL_SCROLL_CIRCULAR);
		}
		else
		{
			text_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
			text_subtitle_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		}
		text_title_.resizeHeightToCurrentText();
        text_subtitle_.resizeHeightToCurrentText();
	}
	text_title_.invalidate();
	text_subtitle_.invalidate();
};


void* ItemSensorCheck::get_user_data()
{
	return (void*)index_;
}

int ItemSensorCheck::set_type()
{
	return (int) CTRL_TYPE::TYPE_ONWER_DRAW;
}

Rect ItemSensorCheck::get_switch_click_area(Rect switch_rc)
{
	Rect rc;
	rc.x = switch_rc.x - (APP_ITEM_HEIGHT - switch_rc.width) / 2;
	rc.y = switch_rc.y - (APP_ITEM_HEIGHT - switch_rc.height) / 2;
	rc.width = APP_ITEM_HEIGHT;
	rc.height = APP_ITEM_HEIGHT;
	return rc;
}

void ItemSensorCheck::update_left_image(void* info)
{
	if (info == nullptr)
	{
		return;
	}

	bool* state = (bool*) info;
	if(*state)
    {
        Bitmap bitmap_open(&custom_repeat_time_checked);
        switch_.setBitmap(bitmap_open);
	}
	else
	{
        Bitmap bitmap_close(&custom_repeat_time_unchecked);
        switch_.setBitmap(bitmap_close);
	}

	invalidate();
}