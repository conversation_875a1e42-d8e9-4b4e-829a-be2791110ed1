/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   LanguageSelectionMenuView.cpp
@Time    :   2025-05-15 15:51:30
*
**************************************************************************/

#include "LanguageSelectionMenuView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"


static const char g_language_selection_title[] = "_language";
TM_KEY(g_language_selection_title)

#if ONLY_ENABLE_CN_ENG
typedef enum {
    LAN_ENGLISH,
    LAN_SIMPLIFIED_CHINESE,
    LAN_MAX,
} LANGUAGE_SELECTION_E;

const char* g_language_selection[] = {
    "_english",
    "_simplified_chinese",
};
TM_DECLARE(g_language_selection)

const qw_menu_info g_language_selection_menu_info[] = {
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[0]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[1]},
};

#else
typedef enum {
    LAN_GERMAN,
    LAN_ENGLISH,
    LAN_SPANISH,
    LAN_FRENCH,
    LAN_ITALIAN,
    LAN_POLISH,
    LAN_PORTUGUESE,
    LAN_RUSSIAN,
    LAN_SIMPLIFIED_CHINESE,
    LAN_JAPANESE,
    LAN_KOREAN,
    LAN_MAX,
} LANGUAGE_SELECTION_E;

const char* g_language_selection[] = {
    "_german",
    "_english",
    "_spanish",
    "_french",
    "_italian",
    "_polish",
    "_portuguese",
    "_russian",
    "_simplified_chinese",
    "_japanese",
    "_korean",
};
TM_DECLARE(g_language_selection)

const qw_menu_info g_language_selection_menu_info[] = {
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[0]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[1]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[2]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[3]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[4]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[5]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[6]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[7]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[8]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[9]},
    {(const int)ITEM_TYPES::ITEM_TEXT, g_language_selection[10]},
};
#endif

LanguageSelectionMenuView::LanguageSelectionMenuView(PageManager* manager)
: QwMenuView(manager),
    p_m_language_type_(nullptr),p_v_language_type_(nullptr),
    update_m_language_type_(this, &LanguageSelectionMenuView::update_m_language_type),
    custom_user_mode_select_(this, &LanguageSelectionMenuView::custom_user_mode_select)

{
}

LanguageSelectionMenuView::~LanguageSelectionMenuView()
{
}

void LanguageSelectionMenuView::setup()
{
    uint8_t last_selection = *p_m_language_type_->get_val(0);
    #if  ONLY_ENABLE_CN_ENG
    if(last_selection == 1)
    {
        last_selection = 0;
    }
    #endif
    QwMenuView::show_menu(LAN_MAX, last_selection, _TM(g_language_selection_title));
}

void LanguageSelectionMenuView::quit()
{
}

void LanguageSelectionMenuView::handleTickEvent()
{
    QwMenuView::list_.handleTickEvent();
}

void LanguageSelectionMenuView::handleKeyEvent(uint8_t c)
{
    QwMenuView::list_.handleKeyEvent(c);
    if (c == KEY_CLK_BACK)
    {
        manager_->push("UniversalSettingsMenu");
    }
    else if (c == KEY_CLK_START)
    {
        int select_index = get_select_app();
        switch_to_app(select_index);
    }
}

void LanguageSelectionMenuView::handleClickEvent(const ClickEvent& evt)
{
    QwMenuView::list_.handleClickEvent(evt);
}

void LanguageSelectionMenuView::handleDragEvent(const DragEvent& evt)
{
    QwMenuView::list_.handleDragEvent(evt);
}

void LanguageSelectionMenuView::handleGestureEvent(const GestureEvent& evt)
{
    QwMenuView::list_.handleGestureEvent(evt);
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL
        && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
    {
        manager_->push("UniversalSettingsMenu");
    }
}

// Notification Callback function
void LanguageSelectionMenuView::set_on_v_language_type(Notification<uint8_t>* command)
{
    p_v_language_type_ = command;
}

// ObserverDrawable Callback function
void LanguageSelectionMenuView::set_update_m_language_type(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
    if (observer != nullptr)
    {
        p_m_language_type_ = observer;
        observer->bind_ctrl(0, QwMenuView::list_);
        observer->bind_notify(update_m_language_type_);
    }
}

void LanguageSelectionMenuView::update_m_language_type(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {

    }
}

// custom function

void LanguageSelectionMenuView::set_item_info(item_info_t *info, int index)
{
    if (info == nullptr || index >= LAN_MAX)
    {
        assert(false && "[LanguageSelectionMenuView:8001]set_item_info index is out");
        return;
    }

    info->item_index = index;
    info->type = (ITEM_TYPES) g_language_selection_menu_info[index].type;
    memset(info->title, 0, sizeof(info->title));
    memcpy(info->title, _TM(g_language_selection_menu_info[index].name),
           strlen(_TM(g_language_selection_menu_info[index].name)));

    memset(info->subtitle, 0, sizeof(info->subtitle));

    info->item_text_info.text_align = 1;
}

void LanguageSelectionMenuView::set_item_notify(QwMenuItem *item, int index)
{
    item->set_select_pos_handle(custom_user_mode_select_);
}

void LanguageSelectionMenuView::custom_user_mode_select(void *item, int x, int y)
{
    if (item == nullptr)
    {
        assert("[LanguageSelectionMenuView:8001]custom_dnd_on_select item is "
               "nullptr");
        return;
    }

    QwMenuItem *item_ = dynamic_cast<QwMenuItem *>((ItemBaseCtrl *) item);
    if (item_ == nullptr)
    {
        assert("[LanguageSelectionMenuView:8001]custom_dnd_on_select item_ not is "
               "QwMenuItem");
        return;
    }

    int select_index = (int) item_->get_user_data();
    switch_to_app(select_index);
}

void LanguageSelectionMenuView::switch_to_app(int index)
{
    #if ONLY_ENABLE_CN_ENG
    if(index == 0)
    {
        index = 1;
    }
    else if(index == 1)
    {
        index = 8;
    }
    #endif
    p_v_language_type_->notify(static_cast<uint8_t>(index));
    manager_->push("UniversalSettingsMenu");
}
