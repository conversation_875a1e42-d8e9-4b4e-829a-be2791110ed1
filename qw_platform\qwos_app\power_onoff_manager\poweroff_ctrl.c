/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   poweroff_ctrl.c
@Time    :   2025/02/10 17:44:14
@Brief   :   关机控制流程
@Details :
*
************************************************************/

#include "poweronoff_cb.h"
#include "poweronoff_ctrl.h"
#include "qw_timer.h"
#include "qwos.h"
#include <rtthread.h>
#include "version_check_api.h"
#include "basic_app_module/basic_app.h"
#ifndef SIMULATOR
#include "drv_common.h"
#include "key_module.h"
#include "power_ctl.h"
#include "subscribe_service.h"
#include "bsp_board.h"
#include "pmic_controller.h"
#ifdef BF0_HCPU
#include "qw_sys_trace.h"
#include "cfg_header_def.h"
#endif
#endif

//定义一个关机阻塞信息的节点结构体
typedef struct block_app_node_t
{
    rt_slist_t list;
    block_app_info node;
} block_app_node;

//定义一个关机阻塞信息的链表变量
static rt_slist_t block_app_dynamic_list = {0};

//打印注册的所有关机阻塞信息
void block_app_dynamic_list_printf(void)
{
    rt_slist_t *pos = NULL;
    rt_slist_for_each(pos, &block_app_dynamic_list)
    {
        block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
        REBOOT_E("%s app = %d priority = %d source = %d!", __FUNCTION__,
                 temp->node.info.app, temp->node.info.priority, temp->node.info.source);
    }
}

//开关机回调的链表变量初始化
static void block_app_dynamic_list_init(void)
{
    //rt_slist_init(&block_app_dynamic_list); // 初始化block_app_dynamic_list,默认就为空，这里不再置空，防止注册在开关机初始化之前时出错。
}

// 硬阻塞关机应用注册
// app 应用的枚举ID
// name 应用的名称，默认NULL
// reason 应用的阻塞原因列表，默认NULL
static bool poweroff_ctrl_hardbolck_register(uint16_t app, char *name, char **reason,
                                             uint8_t reason_max_num)
{
    if (!rt_slist_isempty(&block_app_dynamic_list))
    {   // 如果链表不为空，检测是否存在加入的优先级和APP，存在就报错，不存在则添加。
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if (temp->node.info.app == app)
            {
                REBOOT_E("%s app = %d already register!", __FUNCTION__, app);
                return false;
            }
        }
    }

    // 如果链表不为空且不存在加入的优先级和APP，或者链表为空，添加。
    block_app_node *node;
    node = rt_malloc(sizeof(block_app_node));
    RT_ASSERT(node != RT_NULL);

    node->node.info.app = app;
    node->node.info.type = BLOCK_TYPE_HARD_BLOCK;
    node->node.info.priority = 0;
#ifndef SOC_BF0_LCPU
    node->node.info.source = BLOCK_SOURCE_HCPU;
#else
    node->node.info.source = BLOCK_SOURCE_LCPU;
#endif
    node->node.info.reason_id = 0;
    node->node.prepare_cb = NULL;
    node->node.name = name;
    node->node.reason = reason;
    node->node.reason_max_num = reason_max_num;

    if (rt_slist_isempty(&block_app_dynamic_list))
    {
        rt_slist_append(&block_app_dynamic_list, &node->list);
    }
    else
    {
        rt_slist_t *next = &block_app_dynamic_list;   // 记录要插入的位置
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if (temp->node.info.priority > 0)
            {
                break;
            }
            next = pos;
        }
        rt_slist_insert(next, &node->list);
    }

    //block_app_dynamic_list_printf();
    return true;
}

// 软阻塞关机应用注册
// app 应用的枚举ID
// name 应用的名称，默认NULL
// reason 应用的阻塞原因列表，默认NULL
// prepare_cb 应用在其收到预关机通知后，通知关机管理线程阻塞关机,开始存档/停止及数据保存
static bool poweroff_ctrl_softbolck_register(uint16_t app, uint8_t priority, char *name,
                                             char **reason, uint8_t reason_max_num,
                                             void (*prepare_cb)(void))
{
    if (!rt_slist_isempty(&block_app_dynamic_list))
    {   // 如果链表不为空，检测是否存在加入的优先级和APP，存在就报错，不存在则添加。
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if ((temp->node.info.app == app) || (temp->node.info.priority == priority))
            {
                REBOOT_E("%s app = %d priority =%d already register!", __FUNCTION__, app,
                         priority);
                return false;
            }
        }
    }

    // 如果链表不为空且不存在加入的优先级和APP，或者链表为空，添加。
    block_app_node *node;
    node = rt_malloc(sizeof(block_app_node));
    RT_ASSERT(node != RT_NULL);

    node->node.info.app = app;
    node->node.info.type = BLOCK_TYPE_SOFT_BLOCK;
    node->node.info.priority = priority;
#ifndef SOC_BF0_LCPU
    node->node.info.source = BLOCK_SOURCE_HCPU;
#else
    node->node.info.source = BLOCK_SOURCE_LCPU;
#endif
    node->node.info.reason_id = 0;
    node->node.prepare_cb = prepare_cb;
    node->node.name = name;
    node->node.reason = reason;
    node->node.reason_max_num = reason_max_num;

    if (rt_slist_isempty(&block_app_dynamic_list))
    {
        rt_slist_append(&block_app_dynamic_list, &node->list);
    }
    else
    {
        rt_slist_t *next = &block_app_dynamic_list;   // 记录要插入的位置
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if (temp->node.info.priority > priority)
            {
                break;
            }
            next = pos;
        }
        rt_slist_insert(next, &node->list);
    }

    //block_app_dynamic_list_printf();

    return true;
}

// 软延时关机应用注册
// app 应用的枚举ID
// name 应用的名称，默认NULL
// reason 应用的阻塞原因列表，默认NULL
// prepare_cb 应用在其收到预关机通知后，通知关机管理线程阻塞关机,开始存档/停止及数据保存
static int poweroff_ctrl_softdelay_register(uint16_t app, uint8_t priority, char *name,
                                            char **reason, uint8_t reason_max_num,
                                            void (*prepare_cb)(void))
{
    if (!rt_slist_isempty(&block_app_dynamic_list))
    {   // 如果链表不为空，检测是否存在加入的优先级和APP，存在就报错，不存在则添加。
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if ((temp->node.info.app == app) || (temp->node.info.priority == priority))
            {
                REBOOT_E("%s app = %d priority =%d already register!", __FUNCTION__, app,
                         priority);
                return false;
            }
        }
    }

    // 如果链表不为空且不存在加入的优先级和APP：插入，或者链表为空：添加。
    block_app_node *node;
    node = rt_malloc(sizeof(block_app_node));
    RT_ASSERT(node != RT_NULL);

    node->node.info.app = app;
    node->node.info.type = BLOCK_TYPE_SOFT_DELAY;
    node->node.info.priority = priority;
#ifndef SOC_BF0_LCPU
    node->node.info.source = BLOCK_SOURCE_HCPU;
#else
    node->node.info.source = BLOCK_SOURCE_LCPU;
#endif
    node->node.info.reason_id = 0;
    node->node.prepare_cb = prepare_cb;
    node->node.name = name;
    node->node.reason = reason;
    node->node.reason_max_num = reason_max_num;

    if (rt_slist_isempty(&block_app_dynamic_list))
    {
        rt_slist_append(&block_app_dynamic_list, &node->list);
    }
    else
    {
        rt_slist_t *next = &block_app_dynamic_list;   // 记录要插入的位置
        rt_slist_t *pos = NULL;
        rt_slist_for_each(pos, &block_app_dynamic_list)
        {
            block_app_node *temp = (block_app_node *) pos;   // 获取链表中的结构体
            if (temp->node.info.priority > priority)
            {
                break;
            }
            next = pos;
        }
        rt_slist_insert(next, &node->list);
    }

    //block_app_dynamic_list_printf();
    return true;
}

// 阻塞关机应用注册,可以填充注册表自动注册。
// info 关机阻塞APP的信息
bool poweroff_ctrl_bolck_register(const block_app_info *info)
{
    bool ret = false;
    if (info && (info->info.type != BLOCK_TYPE_NULL))
    {
        switch (info->info.type)
        {
        case BLOCK_TYPE_HARD_BLOCK:   // 硬阻塞关机控制 (不可打断应用)
        {
            ret = poweroff_ctrl_hardbolck_register(info->info.app, info->name,
                                                   info->reason, info->reason_max_num);
            break;
        }
        case BLOCK_TYPE_SOFT_BLOCK:   // 软阻塞关机控制 (可以打断应用,时间不固定)
        {
            ret = poweroff_ctrl_softbolck_register(info->info.app, info->info.priority,
                                                   info->name, info->reason,
                                                   info->reason_max_num,
                                                   info->prepare_cb);
            break;
        }
        case BLOCK_TYPE_SOFT_DELAY:   // 软延时关机控制 (可以打断应用,固定时间内，希望延时关机)
        {
            ret = poweroff_ctrl_softdelay_register(info->info.app, info->info.priority,
                                                   info->name, info->reason,
                                                   info->reason_max_num,
                                                   info->prepare_cb);
            break;
        }
        default:
        {
            break;
        }
        };
    }

    return true;
}

// 阻塞锁管理
static block_lock_mgr *p_block_lock_mgr = NULL;

// 关机控制:初始化
// poweroff_cb  触发关机完成时调用，通知已经关机完成。
// poweroffblock_cb  触发软阻塞解除完成时调用，通知准备关机。
void poweroff_ctrl_init(void)
{
    if (p_block_lock_mgr == NULL)
    {
        //1.动态分配内存
        p_block_lock_mgr = qwos_malloc(sizeof(block_lock_mgr));
    }
    if (p_block_lock_mgr)
    {
        block_app_dynamic_list_init();   // 初始化block_app_dynamic_list

        memset(p_block_lock_mgr, 0, sizeof(block_lock_mgr));
        //2.关机回调
        p_block_lock_mgr->request_cb.poweroff_cb
            = NULL;   // 关机完成，发送关机事件时HCPU回调，避免自己订阅
        p_block_lock_mgr->request_cb.poweroffblockrealese_cb
            = NULL;   // 软阻塞解除完成，发送关机阻塞解除事件时HCPU回调，避免自己订阅
#ifndef SIMULATOR
        //3.注册数据服务
        // 阻塞设置事件服务注册(大小核通用)(LCPU->HCPU,HCPU->HCPU)
        // 预备关机事件服务注册(大小核通用)(HCPU->LCPU,HCPU->HCPU)
        // 阻塞释放事件服务注册(HCPU->LCPU)
        // 开始关机事件服务注册(HCPU->LCPU)
        // 开始复位事件服务注册(HCPU->LCPU)

        //4.订阅数据服务
        optional_config_t config = {
            .sampling_rate = 0,
        };
        // 阻塞设置服务订阅(大小核通用)(LCPU->HCPU,HCPU->HCPU)
        //  qw_dataserver_subscribe("block_config", block_config_in_callback, &config);
        // 小核关机准备就绪服务订阅(LCPU->HCPU)
        //	qw_dataserver_subscribe("lcpu_off_ready_ok", block_config_in_callback, &config);
        // 小核关机完全结束服务订阅(LCPU->HCPU)
        qw_dataserver_subscribe("sta_hcpu", hcpu_poweronoff_sta_cb, &config);
        qw_dataserver_subscribe("sta_lcpu", lcpu_poweronoff_sta_cb, &config);
#endif
        REBOOT_I("poweroff_ctrl_init ----------------------------");
    }
}

// 关机控制:获取阻塞锁管理信息句柄
block_lock_mgr *poweroff_ctrl_get_hander(void)
{
    return p_block_lock_mgr;
}

// 关机控制:销毁
static void poweroff_ctrl_deinit(void)
{
    qwos_free(p_block_lock_mgr);
    p_block_lock_mgr = NULL;
}

// 关机控制:设置关机完成回调
// poweroff_cb  触发关机完成时调用，通知已经关机完成。
// poweroffblock_cb  触发软阻塞解除完成时调用，通知准备关机。
void poweroff_ctrl_set_cb(const poweronoff_ctrl_cb *cb)
{
    if (p_block_lock_mgr)
    {
        // 修改关机回调
        p_block_lock_mgr->request_cb.poweroff_cb
            = cb->poweroff_cb;   // 关机完成，发送关机事件时HCPU回调，避免自己订阅
        p_block_lock_mgr->request_cb.poweroffblockrealese_cb
            = cb->poweroffblockrealese_cb;   // 软阻塞解除完成，发送关机阻塞解除事件时HCPU回调，避免自己订阅
    }
}

// 关机中控制:设置关机中锁定
static bool poweroff_lock = false;

// 关机中控制:仅用于关机动画的特殊场景，当关机动画不使用UI回调时，开始播放时设置true用于锁住关机流程，等待播放完毕，设置false释放
void poweroff_ctrl_set_lock(bool lock)
{
    poweroff_lock = lock;
}

// 关机中控制:获取关机中锁定状态
bool poweroff_ctrl_get_lock()
{
    return poweroff_lock;
}

// 通过app枚举查找app在block_app_table中对应的信息
static const block_app_info *block_app_table_search(PowerOffCtrlBlockApp app)
{
    rt_slist_t *pos = NULL;
    rt_slist_for_each(pos, &block_app_dynamic_list)
    {
        const block_app_info *block_app = &((block_app_node *) pos)
                                               ->node;   // 获取链表中的结构体
        if (block_app->info.app == app)
        {
            return block_app;
        }
    }
    return NULL;
}

// 通过app枚举获取app朱否阻塞 true 阻塞  false 未阻塞
static bool poweroff_ctrl_get_block_stata(uint8_t app)
{
    const block_app_info *app_info = block_app_table_search(app);
    if (app_info->info.type == BLOCK_TYPE_SOFT_BLOCK)
    {
        if (p_block_lock_mgr->softblock_state && APP_BIT2MAP_LIFT_SHIFT(app))
            return true;
        else
            return false;
    }
    else if (app_info->info.type == BLOCK_TYPE_SOFT_DELAY)
    {
        if (p_block_lock_mgr->softdelay_state && APP_BIT2MAP_LIFT_SHIFT(app))
            return true;
        else
            return false;
    }
    return false;
}

// 查找ID最小的APP，作为阻塞APP，ID为MAP表位索引
static uint8_t poweroff_ctrl_get_stata_bit_index(uint32_t bit_map)
{
    uint8_t bit_index = 0;
    if (bit_map > 0)
    {
        while ((bit_map & APP_BIT2MAP_LIFT_SHIFT(bit_index)) == 0)
        {
            bit_index++;
        }
        return (bit_index + 1);
    }
    return 0;
}

// 获取阻塞详细信息
static void poweroff_ctrl_get_block_reason(void)
{
    //1.查找ID最小的APP，作为阻塞APP
    if (p_block_lock_mgr->reason.type == BLOCK_TYPE_HARD_BLOCK)
    {
        uint8_t app_index = poweroff_ctrl_get_stata_bit_index(
            p_block_lock_mgr->hardblock_state);
        if (app_index > 0)
        {
            p_block_lock_mgr->reason.app = app_index;
            p_block_lock_mgr->reason.reason_id = p_block_lock_mgr
                                                     ->hardblock_reason_id[app_index];
        }
    }
    else if (p_block_lock_mgr->reason.type == BLOCK_TYPE_SOFT_BLOCK)
    {
        uint8_t app_index = poweroff_ctrl_get_stata_bit_index(
            p_block_lock_mgr->softblock_state);
        if (app_index > 0)
        {
            p_block_lock_mgr->reason.app = app_index;
            p_block_lock_mgr->reason.reason_id = p_block_lock_mgr
                                                     ->softblock_reason_id[app_index];
        }
    }
    else if (p_block_lock_mgr->reason.type == BLOCK_TYPE_SOFT_DELAY)
    {
        uint8_t app_index = poweroff_ctrl_get_stata_bit_index(
            p_block_lock_mgr->softdelay_state);
        if (app_index > 0)
        {
            p_block_lock_mgr->reason.app = app_index;
            p_block_lock_mgr->reason.reason_id = p_block_lock_mgr
                                                     ->softdelay_reason_id[app_index];
        }
    }
    //注意考虑小核获取大核阻塞状态：本地注册表找不到就是另一个核的注册表里面
    //2.查找注册表，获取注册信息
    //block_app_info* p_block_app = ;
    //3.通过注册信息，获取字符串信息
    //p_block_lock_mgr->reason.app_name = p_block_app->name;                                       // 阻塞APP名称
    //p_block_lock_mgr->reason.reasion =  p_block_app->reason[p_block_lock_mgr->reason.reason_id]; // 阻塞APP原因
}

#ifndef SOC_BF0_LCPU
// 软延时定时到了，发送释放事件
static void poweroff_delay_timer_handler(void *parameter)
{
    delay_info *poweroff_delay_timer = (delay_info *) parameter;
    block_config *config = &poweroff_delay_timer->config;
    config->parm.gap = 0;
    // 发送延时阻塞完成事件
    publish_status_event(EVENT_SOFT_BLOCK_REQUEST_OR_REALSE, config, sizeof(block_config));
}

// 软延时定时器创建
static delay_info *poweroff_delay_create(block_config *config)
{
    REBOOT_I("%s config = %08x", __FUNCTION__, config);
    if (config)
    {
        delay_info *poweroff_delay_timer = (delay_info *) qwos_malloc(sizeof(delay_info));
        if (poweroff_delay_timer)
        {
            memcpy(&poweroff_delay_timer->config, config, sizeof(block_config));
            // 开启，等待100ms等待超时，等待阻塞打断
            qw_timer_init(&poweroff_delay_timer->delay_timer,
                          QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER,
                          poweroff_delay_timer_handler);
        }
        return poweroff_delay_timer;
    }
    return NULL;
}

/* 大核启动定时器 */
static void poweroff_delay_start(delay_info *delay_timer)
{
    REBOOT_I("%s delay_timer = %08x", __FUNCTION__, delay_timer);
    if (delay_timer)
    {
        //qw_timer_stop(&delay_timer->delay_timer);
        qw_timer_start(&delay_timer->delay_timer, delay_timer->config.parm.gap,
                       delay_timer, "poweroff_delay");
    }
}

/* 大核收到预关机指令后，开启全部定时器 */
static void poweroff_delay_startall(void)
{
    rt_slist_t *pos = NULL;
    rt_slist_for_each(pos, &block_app_dynamic_list)
    {
        const block_app_info *block_app = &((block_app_node *) pos)
                                               ->node;   // 获取链表中的结构体
        // 查询当前任务是否延时关机，若有，开启定时器
        if ((block_app->info.type == BLOCK_TYPE_SOFT_DELAY)
            && (poweroff_ctrl_get_block_stata(block_app->info.app)))
        {
            poweroff_delay_start(p_block_lock_mgr->delay_timer[block_app->info.app]);
        }
    }
}

static void poweroff_delay_delete(delay_info *delay_timer)
{
    REBOOT_I("%s", __FUNCTION__);
    if (delay_timer)
    {
        qw_timer_stop(&delay_timer->delay_timer);
        qw_timer_detach(&delay_timer->delay_timer);
        qwos_free(delay_timer);
    }
}

// 关机控制:订阅后调用
void poweroff_ctrl(block_config *config)
{
    if (p_block_lock_mgr)
    {
        // 阻塞状态修改
        if (config && (config->info.type != BLOCK_TYPE_NULL))
        {
            switch (config->info.type)
            {
#ifdef USE_POWEROFF_HARD_BLOCK
            case BLOCK_TYPE_HARD_BLOCK:   // 硬阻塞关机控制 (不可打断应用)
            {
                // 初次进入硬阻塞
                if ((p_block_lock_mgr->hardblock_state == 0) && (config->parm.lock))
                {
                    // 发送硬阻塞进入通知
                    publish_status_event(EVENT_HARD_BLOCK_ENTER, NULL, 0);
                }

                uint8_t bit_index = (config->info.app - BLOCK_APP_NULL) & APP_BIT_MASK;
                if (config->parm.lock)
                {
                    p_block_lock_mgr->hardblock_state |= APP_BIT2MAP_LIFT_SHIFT(bit_index);
                    p_block_lock_mgr->hardblock_reason_id[bit_index] = config->info
                                                                           .reason_id;
                }
                else
                {
                    p_block_lock_mgr->hardblock_state &= ~APP_BIT2MAP_LIFT_SHIFT(
                        bit_index);
                    p_block_lock_mgr->hardblock_reason_id[bit_index] = 0;
                }

                // 所有硬阻塞解除，发送退出通知
                if (p_block_lock_mgr->hardblock_state == 0)
                {
                    // 发送硬阻塞退出通知
                    publish_status_event(EVENT_HARD_BLOCK_REALSEALL, NULL, 0);
                }
                break;
            }
#endif
#ifdef USE_POWEROFF_SOFT_BLOCK
            case BLOCK_TYPE_SOFT_BLOCK:   // 软阻塞关机控制 (可以打断应用,时间不固定)
            {
                // 初次进入软阻塞
                //if ((p_block_lock_mgr->softblock_state == 0)
                //    && (p_block_lock_mgr->softdelay_state == 0)
                //    && (p_block_lock_mgr->prepare_info.poweroff_reason
                //        != POWEROFF_EVENT_NULL))
                //{
                    //// 发送软阻塞进入通知
                    //publish_status_event(EVENT_SOFT_BLOCK_ENTER, NULL, 0);
                //}

                uint8_t bit_index = (config->info.app - BLOCK_APP_NULL) & APP_BIT_MASK;
                if (config->parm.lock)
                {
                    p_block_lock_mgr->softblock_state |= APP_BIT2MAP_LIFT_SHIFT(bit_index);
                    p_block_lock_mgr->softblock_reason_id[bit_index] = config->info
                                                                           .reason_id;
                }
                else
                {
                    p_block_lock_mgr->softblock_state &= ~APP_BIT2MAP_LIFT_SHIFT(
                        bit_index);
                    p_block_lock_mgr->softblock_reason_id[bit_index] = 0;
                }
                // 所有软阻塞解除，发送退出通知
                if ((p_block_lock_mgr->softblock_state == 0)
                    && (p_block_lock_mgr->softdelay_state == 0)
                    && (p_block_lock_mgr->prepare_info.poweroff_reason
                        != POWEROFF_EVENT_NULL))
                {
                    publish_status_event(EVENT_SOFT_BLOCK_REALSEALL_AND_DELAY_TIMEOUT,
                                         &p_block_lock_mgr->realse_info,
                                         sizeof(block_realse_info));
                }
                break;
            }
            case BLOCK_TYPE_SOFT_DELAY:   // 软延时关机控制 (可以打断应用,固定时间内，希望延时关机)
            {
                // 初次进入软阻塞
                //if ((p_block_lock_mgr->softblock_state == 0)
                //    && (p_block_lock_mgr->softdelay_state == 0)
                //    && (p_block_lock_mgr->prepare_info.poweroff_reason
                //    != POWEROFF_EVENT_NULL))
                //{
                    //// 发送软阻塞进入通知
                    //publish_status_event(EVENT_SOFT_BLOCK_ENTER, NULL, 0);
                //}
                uint8_t bit_index = (config->info.app - BLOCK_APP_NULL) & APP_BIT_MASK;
                if (config->parm.gap > 0)
                {
                    p_block_lock_mgr->softdelay_state |= APP_BIT2MAP_LIFT_SHIFT(bit_index);
                    p_block_lock_mgr->softdelay_reason_id[bit_index] = config->info
                                                                           .reason_id;
                    // 若当前已经在关机过程中，收到延时阻塞，直接开启定时器
                    if (p_block_lock_mgr->prepare_info.poweroff_reason
                        != POWEROFF_EVENT_NULL)
                    { /* 已经发布，处在关机过程中 */
                        poweroff_delay_start(p_block_lock_mgr->delay_timer[bit_index]);
                    }
                }
                else
                {
                    p_block_lock_mgr->softdelay_state &= ~APP_BIT2MAP_LIFT_SHIFT(
                        bit_index);
                    p_block_lock_mgr->softdelay_reason_id[bit_index] = 0;
                    poweroff_delay_delete(p_block_lock_mgr->delay_timer[bit_index]);
                }
                // 所有软阻塞解除，发送退出通知
                if ((p_block_lock_mgr->softblock_state == 0)
                    && (p_block_lock_mgr->softdelay_state == 0)
                    && (p_block_lock_mgr->prepare_info.poweroff_reason
                        != POWEROFF_EVENT_NULL))
                {
                    publish_status_event(EVENT_SOFT_BLOCK_REALSEALL_AND_DELAY_TIMEOUT,
                                         &p_block_lock_mgr->realse_info,
                                         sizeof(block_realse_info));
                }
                break;
            }
#endif
            default:
            {
                break;
            }
            };
        }

        // 阻塞原因赋值
        memset(&p_block_lock_mgr->reason, 0, sizeof(block_reason));
        if (p_block_lock_mgr->hardblock_state > 0)
        {   // 硬阻塞置位，阻塞原因变为硬阻塞，软阻塞应该是0。
            p_block_lock_mgr->reason.type = BLOCK_TYPE_HARD_BLOCK;
        }
        else if (p_block_lock_mgr->softblock_state > 0)
        {   // 软阻塞置位，阻塞原因变为软阻塞，硬阻塞应该是0。
            p_block_lock_mgr->reason.type = BLOCK_TYPE_SOFT_BLOCK;
        }
        else if (p_block_lock_mgr->softdelay_state > 0)
        {   // 软延时置位，阻塞原因变为软延时，硬/软阻塞应该是0。
            p_block_lock_mgr->reason.type = BLOCK_TYPE_SOFT_DELAY;
        }
        poweroff_ctrl_get_block_reason();   // 获取阻塞详细信息
    }
}
#endif

// 收到预关机指令后，执行
void poweroff_ctrl_prepare_cb(PowerOffCtrlBlockSource source)
{
    rt_slist_t *pos = NULL;
    rt_slist_for_each(pos, &block_app_dynamic_list)
    {
        const block_app_info *block_app = &((block_app_node *) pos)
                                               ->node;   // 获取链表中的结构体
        if ((block_app->info.type == BLOCK_TYPE_SOFT_BLOCK)
            && (block_app->prepare_cb != NULL))
        {
            if (block_app->info.source == source)
            {
                // 查询当前任务是否阻塞
                REBOOT_I("%s block_sta = %d ctrl_status = %d #######", __FUNCTION__,
                         poweroff_ctrl_get_block_stata(block_app->info.app),
                         poweroff_ctrl_status_get());
                block_app->prepare_cb();   // 预关机回调，给应用发送停止指令
            }
        }
        if ((block_app->info.type == BLOCK_TYPE_SOFT_DELAY)
            && (block_app->prepare_cb != NULL))
        {
            if (block_app->info.source == source)
            {
                block_app->prepare_cb();   // 预关机回调，给应用发送停止指令
            }
        }
    }
#ifndef SOC_BF0_LCPU
    // 当前已经在关机过程前，已经收到延时阻塞，直接开启对应的定时器
    if (source == BLOCK_SOURCE_HCPU)
    {
        poweroff_delay_startall();   // HCPU 开启全部延时关机定时器
    }
#endif
}

// 硬阻塞，在开始时，通知关机管理线程阻塞关机；在结束时，通知关机管理线程，使能关机控制。
//  app 应用的枚举ID
//  eable应用阻塞和使能
//  reason_id应用的阻塞原因ID，默认0
static int poweroff_ctrl_hardbolck_set(uint16_t app, bool eable, uint8_t reason_id)
{
    int ret = 0;

    //1.检测是否容许设置，不容许返回-1。


    //2.检测是否注册过，若无返回1

    //3.检测是否输入合法，不容许返回2。

    return ret;
}

// 软阻塞,在其收到预关机通知后，通知关机管理线程阻塞关机；在其处理完毕后通知关机管理线程，使能关机控制。
//  app 应用的枚举ID
//  eable应用阻塞和使能
//  reason_id应用的阻塞原因ID，默认0
static int poweroff_ctrl_softbolck_set(uint16_t app, bool eable, uint8_t reason_id)
{
    int ret = 0;
    //1.检测是否注册过，若无返回-1

    //2.检测是否容许设置，不容许返回1。
    return ret;
}

// 软延时,通知关机管理线程需要最多需要等待多久后关机和软重启（在未关机前，可以多次发送修改时间）
//  app 应用的枚举ID
//  gap 单位ms,应用的关闭需要的最大时间，或希望延时关闭的时间
//  reason_id应用的阻塞原因ID，默认0
static int poweroff_ctrl_softdelay_set(uint16_t app, uint32_t gap, uint8_t reason_id)
{
    int ret = 0;
    //1.检测是否注册过，若无返回-1

    //2.检测是否容许设置，不容许返回1。

    return ret;
}

// 阻塞设置:
// HARD_BLOCK：硬阻塞，在开始时，通知关机管理线程阻塞关机；
// SOFT_BLOCK: 软阻塞,在其收到预关机通知后，通知关机管理线程阻塞关机；
// SOFT_DELAY: 软延时,通知关机管理线程需要最多需要等待多久后关机和软重启（在未关机前，可以多次发送修改时间）
// config 应用阻塞的设置
static bool poweroff_ctrl_block_set(const block_config *config)
{
    int ret = -1;
    event_code_t event = EVENT_INIT;
    if (config && (config->info.type != BLOCK_TYPE_NULL))
    {
        switch (config->info.type)
        {
#ifdef USE_POWEROFF_HARD_BLOCK
        case BLOCK_TYPE_HARD_BLOCK:   // 硬阻塞关机控制 (不可打断应用)
        {
            ret = poweroff_ctrl_hardbolck_set(config->info.app, config->parm.lock,
                                              config->info.reason_id);
            event = EVENT_HARD_BLOCK_REQUEST_OR_REALSE;
            break;
        }
#endif
#ifdef USE_POWEROFF_SOFT_BLOCK
        case BLOCK_TYPE_SOFT_BLOCK:   // 软阻塞关机控制 (可以打断应用,时间不固定)
        {
            ret = poweroff_ctrl_softbolck_set(config->info.app, config->parm.lock,
                                              config->info.reason_id);
            event = EVENT_SOFT_BLOCK_REQUEST_OR_REALSE;
            break;
        }
        case BLOCK_TYPE_SOFT_DELAY:   // 软延时关机控制 (可以打断应用,固定时间内，希望延时关机)
        {
            ret = poweroff_ctrl_softdelay_set(config->info.app, config->parm.gap,
                                              config->info.reason_id);
            event = EVENT_SOFT_BLOCK_REQUEST_OR_REALSE;
            break;
        }
#endif
        default:
        {
            break;
        }
        };
    }
    if ((ret == 0) && p_block_lock_mgr)
    {
        return publish_status_event(event, (void *) config,
                                    sizeof(block_config));   // 发布阻塞通知
    }
    return false;
}

// 硬阻塞，在开始时，通知关机管理线程阻塞关机；
// 软阻塞,在其收到预关机通知后，通知关机管理线程阻塞关机；
//  app 应用的枚举ID
//  reason_id应用的阻塞原因ID，默认0
void poweroff_ctrl_lock(uint16_t app, uint8_t reason_id)
{
    const block_app_info *app_info = block_app_table_search(app);
    block_config config = {
        .info = {.app = app,                    // 注册的APP_ID PowerOffCtrlBlockApp
                 .type = app_info->info.type,   // 阻塞类型     PowerOffCtrlBlockType
                 .reason_id = reason_id},       // 阻塞原因ID
        .parm = {.lock = true},                 ///阻塞
    };
    poweroff_ctrl_block_set(&config);
}

// 硬阻塞，在结束时，通知关机管理线程，使能关机控制。
// 软阻塞,在其处理完毕后通知关机管理线程，使能关机控制。
// app 应用的枚举ID
void poweroff_ctrl_unlock(uint16_t app)
{
    const block_app_info *app_info = block_app_table_search(app);
    block_config config = {
        .info = {.app = app,                    // 注册的APP_ID PowerOffCtrlBlockApp
                 .type = app_info->info.type,   // 阻塞类型     PowerOffCtrlBlockType
                 .reason_id = 0},               // 阻塞原因ID
        .parm = {.lock = false},                ///释放
    };
    poweroff_ctrl_block_set(&config);
}

// 软延时,通知关机管理线程需要最多需要等待多久后关机和软重启（在未关机前，可以多次发送修改时间）
//  app 应用的枚举ID
//  gap 单位ms,应用的关闭需要的最大时间，或希望延时关闭的时间
//  reason_id应用的阻塞原因ID，默认0
void poweroff_ctrl_delay(uint16_t app, uint32_t gap, uint8_t reason_id)
{
    const block_app_info *app_info = block_app_table_search(app);
    block_config config = {
        .info = {.app = app,                    // 注册的APP_ID PowerOffCtrlBlockApp
                 .type = app_info->info.type,   // 阻塞类型     PowerOffCtrlBlockType
                 .reason_id = reason_id},       // 阻塞原因ID
        .parm = {.gap = gap},                   ///阻塞
    };
    poweroff_ctrl_block_set(&config);
}


#ifndef SOC_BF0_LCPU
// 系统复位原因设置:
static void poweronoff_ctrl_cfg_reset(bool reset, bool first_poweron, uint16_t reset_reson)
{
#ifdef BF0_HCPU
    set_system_reset_flag(reset);           //重置标识true:进入复位,false:正常开机
    set_system_reset_reson(reset_reson);    //复位关机原因
    if (first_poweron)                      //首次工厂标识 true:首次开机，false:非首次开机
    {
        reset_system_activation();
    }
    else
    {
        system_activation();
    }

    // 如果是恢复出厂设置，创建一个标志文件
    if (reset_reson == POWEROFF_EVENT_FACTORY) {
        create_factory_reset_flag();
    }

    REBOOT_W("###reset_reson = %d", get_system_reset_reson());
#endif
}

// 获取关机原因:
// 返回值参考PowerOffType
uint16_t poweronoff_ctrl_cfg_get(void)
{
    return get_system_reset_reson();   //复位关机原因
}

// 大核尝试发送关机通知
// 返回 0 当前场景无法使用关机 1 当前场景可以关机
static bool request_off_or_rboot(uint8_t poweroff_reason, bool restore, bool factory,
                                 bool poweroff, bool shiping)
{
    REBOOT_W("%s %d,%d,%d,%d\n", __func__, restore, factory, poweroff, shiping);
    if (p_block_lock_mgr && (poweroff_ctrl_status_get() == STATE_ON))
    {       /* 开机状态 */
        if (p_block_lock_mgr->hardblock_state)
        {   // 硬阻塞关机控制 (不可打断应用)
            REBOOT_W("###hardblock_state = %08x", p_block_lock_mgr->hardblock_state);
            return false;
        }
        else
        {
            // 预关机通知信息
            memset(&p_block_lock_mgr->prepare_info, 0, sizeof(poweroff_prepare_info));
            p_block_lock_mgr->prepare_info.poweroff_reason = poweroff_reason;
            // 阻塞解除时的通知信息
            memset(&p_block_lock_mgr->realse_info, 0, sizeof(block_realse_info));
            p_block_lock_mgr->realse_info.restore_mode = restore;     // 还原模式
            p_block_lock_mgr->realse_info.poweroff_mode = poweroff;   // 关机模式
            p_block_lock_mgr->realse_info.factory_mode = factory;     // 恢复出厂设置模式
            p_block_lock_mgr->realse_info.shiping_mode = shiping;     // 船运模式

            /* 设置关机原因 */
            if (p_block_lock_mgr->realse_info.factory_mode)
            {
                poweronoff_ctrl_cfg_reset(true, true,
                                          poweroff_reason);   // 恢复出产设置，复位，第一次进入
            }
            else if (p_block_lock_mgr->realse_info.shiping_mode)
            {
                poweronoff_ctrl_cfg_reset(false, true,
                                          poweroff_reason);   // 船运模式，关机，第一次进入
            }
            else if (p_block_lock_mgr->realse_info.poweroff_mode)
            {
                poweronoff_ctrl_cfg_reset(false, false,
                                          poweroff_reason);   // 关机，关机，非第一次进入
            }
            else
            {
                poweronoff_ctrl_cfg_reset(
                    true, false, poweroff_reason);   // 还原设置/重启，复位，非第一次进入
            }
            #ifdef BF0_HCPU
            //记录
            QW_TRACE_APP_EVENT(TRACE_APP_TYPE_POWER_CTRL, TRACE_MODULE_END);//关机
            #endif
            // 发布关机通知
            fsm_notify(EVENT_SOURCE(EVENT_SOURCE_HCPU, EVENT_DIR_HCPU_TO_HCPU,
                                    EVENT_APP_POWEROFF),
                       EVENT_SOFT_OFF_REBOOT_REQUEST, &p_block_lock_mgr->realse_info,
                       sizeof(block_realse_info));

            return true;
        }
    }
    REBOOT_W("###ctrl_status = %d", poweroff_ctrl_status_get());
    return false;
}

// 系统强制重启:
void request_reboot_force(void)
{
    //POWEROFF_EVENT_REBOOT_FORCE
#ifndef SIMULATOR
    BSP_PMIC_DeInit();
    rt_hw_interrupt_disable();
    HAL_PMU_Reboot();
#endif
}



// 系统强制关机:
void request_poweroff_force(void)
{
    //POWEROFF_EVENT_POWEROFF_FORCE
#ifndef SIMULATOR
    BSP_PMIC_DeInit();
    rt_hw_interrupt_disable();
    HAL_PMU_EnterHibernate();
#endif
}

// 请求关机重启返回阻塞原因:
static bool request_reboot_with_reason(block_reason *reason, uint8_t poweroff_reason,
                                       bool restore, bool factory, bool poweroff,
                                       bool shiping)
{
    if (p_block_lock_mgr)
    {
        // 拷贝阻塞原因
        if (reason)
        {
            memcpy(reason, &p_block_lock_mgr->reason, sizeof(block_reason));
        }
        // 填充预关机通知信息，填充阻塞解除时的通知信息，发布预关机通知
        return request_off_or_rboot(poweroff_reason, restore, factory, poweroff, shiping);
    }
    else// 在未初始化关机模块之前使用，确保功能正常
    {
        REBOOT_E("###p_block_lock_mgr = NULL");
        if((poweroff_reason == POWEROFF_EVENT_POWEROFF)||(poweroff_reason == POWEROFF_EVENT_SHIPING))
            request_poweroff_force();
        else
            request_reboot_force();
    }
    return false;
}

// 重启控制:
// 1.发送关机重启指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，直接开始关机重启流程.
// 2.获取当前是否容许关机重启，若不容许关机重启，填充阻塞类型,应用ID和原因。
bool request_reboot(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_REBOOT, false, false, false,
                                      false);
}

// 关机控制:
// 1.发送关机指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位关机模式标志，开始关机流程.
// 2.获取当前是否容许关机，若不容许关机，填充阻塞类型,应用ID和原因。
bool request_poweroff(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_POWEROFF, false, false, true,
                                      false);
}

// 低电关机控制:
// 1.发送关机指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位关机模式标志，开始关机流程.
// 2.获取当前是否容许关机，若不容许关机，填充阻塞类型,应用ID和原因。
bool request_lowpower_off(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_LOWPOWER, false, false, true,
                                      false);
}

// 恢复出厂设置:
// 1.发送关机重启指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位恢复出厂设置标志，直接开始关机重启流程.
// 2.获取当前是否容许关机重启，若不容许关机重启，填充阻塞类型,应用ID和原因。
bool request_factory(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_FACTORY, false, true, false,
                                      false);
}

// 还原应用设置:
// 1.发送关机重启指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位还原应用设置标志，直接开始关机重启流程.
// 2.获取当前是否容许关机重启，若不容许关机重启，填充阻塞类型,应用ID和原因。
bool request_restore(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_RESORTE, true, false, false,
                                      false);
}

// 船运模式设置:
// 1.发送关机重启指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位还原应用设置标志，直接开始关机重启流程.
// 2.获取当前是否容许关机重启，若不容许关机重启，填充阻塞类型,应用ID和原因。
bool request_poweroff_for_shiping(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_SHIPING, false, false, false,
                                      true);
}

// OTA模式设置:
// 1.发送关机重启指令，如果是硬阻塞，返回false,返回阻塞原因。如果没有阻塞，发布预关机，等待软阻塞结束，置位还原应用设置标志，直接开始关机重启流程.
// 2.获取当前是否容许关机重启，若不容许关机重启，填充阻塞类型,应用ID和原因。
bool request_reboot_for_ota(block_reason *reason)
{
    return request_reboot_with_reason(reason, POWEROFF_EVENT_OTA, false, false, false,
                                      false);
}

/************************************************************************
 *@function:bool request_reboot_for_crash(block_reason *reason)
 *@brief:crash/assert/系统卡死后强制重启
 *@param:reason,now is not used,just set NULL.
 *@return:just return true.
*************************************************************************/
#undef USER_PRODUCT_RELEASE   //用户版本去掉该功能.

bool request_reboot_for_crash(block_reason *reason)
{
#ifdef BF0_HCPU
#ifndef USER_PRODUCT_RELEASE
    register int press_start_ts = 0;
    register int press_end_ts = 0;
    const int cycles_bpm = (int) HAL_LPTIM_GetFreq();
    int keep_sec = 0;
    disable_board_gpio_irq();
    int ok_key_pin = (hw_version_get() == HARDWARE_VERSION_A3) ? OK_KEY_PIN : OK_KEY_PIN_A4;
    rt_pin_mode(ok_key_pin, PIN_MODE_INPUT);
    rt_pin_mode(BACK_KEY_PIN, PIN_MODE_INPUT);
    rt_pin_mode(POWER_KEY_PIN, PIN_MODE_INPUT);
    do
    {
        /*长按Power键或者按OK+BACK 键 持续超过3秒则重启.*/
        if ((PIN_LOW == rt_pin_read(ok_key_pin) && PIN_LOW == rt_pin_read(BACK_KEY_PIN)) || (PIN_LOW == rt_pin_read(POWER_KEY_PIN)))
        {
            press_end_ts = rt_system_get_time();
            if (press_start_ts == 0)
            {
                press_start_ts = press_end_ts;
            }
            keep_sec = (press_end_ts - press_start_ts) / cycles_bpm;
        }
        else
        {
            press_start_ts = 0;
            press_end_ts = 0;
        }
    } while (keep_sec <= 3);
    rt_kprintf("will reboot keep_sec:%d\n", keep_sec);
    rt_hw_interrupt_disable();
    HAL_PMU_Reboot();
#endif
#endif
    return true;
}

#endif
