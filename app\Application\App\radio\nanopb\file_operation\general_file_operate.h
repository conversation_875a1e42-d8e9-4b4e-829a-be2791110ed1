/************************************************************************
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File    : general_file_operate.h
*<AUTHOR> igpsport
*@Date    : 2025/04/27
*@Description : pb协议文件操作头文件
************************************************************************/

#ifndef _GENERAL_FILE_OPERATE_H_
#define _GENERAL_FILE_OPERATE_H_


#include <stdbool.h>
#include "file_download.h"
#include "ble_interflow_single.h"

// 定义任务参数结构体
typedef struct {
    char fd_name[128];  // 根据实际需要调整大小
    char file_name[128]; // 根据实际需要调整大小
} unzip_dial_task_arg_t;

uint8_t ble_genaral_file_store(uint8_t service_type, uint8_t op_type, const download_pb_dsc *pb_dsc, const uint8_t* data, uint16_t data_length);
void ble_genaral_store_abort(uint8_t service_type,uint8_t op_type);

bool check_and_create_dir(const char* path);
void send_gui_page_command(char *page_name, uint8_t cmd);
#endif /* APP_RADIO_NANOPB_FILE_OPERATION_GENERAL_FILE_OPERATE_H_ */
