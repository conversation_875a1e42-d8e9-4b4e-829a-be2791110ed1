/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   Translate.h
@Time    :   2024/12/11 13:57:31
*
**************************************************************************/

#ifndef __TRANSLATE_H
#define __TRANSLATE_H
#include "qw_dev_cfg.h"

#define ONLY_ENABLE_CN_ENG 1 //只支持中文和英文

//#define EXPORT_TRANSLATE_KEY

typedef enum
{
    enum_qw_language_en,    // 英语
    enum_qw_language_sp,    // 西班牙语
    enum_qw_language_cn,    // 简体中文
    enum_qw_language_jp,    // 日语
    enum_qw_language_kr,    // 韩语
    enum_qw_language_fr,    // 法语
    enum_qw_language_pt,    // 葡萄牙语(巴西)
    enum_qw_language_pl,    // 波兰语
    // enum_qw_language_th,    // 泰语
    enum_qw_language_ru,    // 俄语
    enum_qw_language_ge,    // 德语
    enum_qw_language_it,    // 意大利语
} language_type;

void declare_list(const char** list, int count);

void set_translate_language(language_type lan);

//将kv中保存的语言类型赋值给显示中使用的枚举（枚举顺序不一致，需转换）
language_type switch_language_value_kv_to_show(LANGUAGE_TYPE type);

extern "C" const char* translate_text(const char* key);

struct TM_DECLARE_MACHINE
{
    TM_DECLARE_MACHINE(const char** arg, int count)
	{
		declare_list(arg, count);
    }
};

// 添加字符串到翻译键值表, 会自动忽略重复项和空字串
#define TM_DECLARE(list) static TM_DECLARE_MACHINE TM_DECLARE_##list(list, sizeof(list) / sizeof(char*));
#define TM_KEY(key) static TM_DECLARE_MACHINE TM_KEY_##key((const char**)key, 0);

// 翻译文本
#define _TM(key) translate_text(key)

#ifdef EXPORT_TRANSLATE_KEY
#define TM_DECLARE_END_STR "TM_DECLARE_END"
#endif // EXPORT_TRANSLATE_KEY

#endif //__TRANSLATE_H

