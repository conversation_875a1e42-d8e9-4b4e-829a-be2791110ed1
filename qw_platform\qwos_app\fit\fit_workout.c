/************************************************************************
*
* Copyright(c) 2024, igpsport Software Co., Ltd.
* All Rights Reserved.
*
* @file    : fit_workout.c
* @brief   : 训练课程文件管理实现
*
**************************************************************************/
#include "fit_workout.h"
//#include "workout_lib.h"
#include <string.h>
#include "cfg_header_def.h"
#include <sys/stat.h>  // 添加这个头文件用于 stat 函数

#define WORKOUTS_DIR "/WORKOUTS"  // 训练课程目录
int stat(const char *pathname, struct stat *statbuf);
static uint16_t s_workout_index = 0xFFFF;  // 当前课程索引
static qw_mlist_t s_workout_list;  // 课程文件链表
static uint16_t s_workout_count;   // 课程总数
static workout_course_detail_t s_course_detail;  // 静态课程详情结构体
static uint16_t s_latest_workout_index = 0xFFFF;  // 最新课程文件索引

/**
 * @brief 扫描训练课程文件
 */
static void fit_workout_scan_files(void)
{
    s_workout_count = fs_scan_files(&s_workout_list, g_fileDirName[enum_mian_disk_workouts], ".fit", 0, 0, 0, 0);

    // 找到最新添加的文件
    if (s_workout_count > 0) {
        qw_mlist_t* mnode = NULL;
        time_t latest_time = 0;
        uint16_t index = 0;
        s_latest_workout_index = 0xFFFF;

        // 遍历链表找到最新的文件
        FOR_MLIST(mnode, &s_workout_list) {
            if (mnode->data_size < 80) {
                char file_path[150] = {0};
                char file_name[80] = {0};

                // 检查数据大小是否有效
                if (mnode->data_size == 0) {
                    continue;
                }

                // 复制并检查文件名
                memcpy(file_name, mnode->mnode_data, mnode->data_size);
                file_name[mnode->data_size] = 0;
                if (strlen(file_name) == 0) {
                    continue;
                }

                // 构造并检查完整路径
                if (g_fileDirName[enum_mian_disk_workouts] == NULL) {
                    continue;
                }

                // 处理基础目录路径
                const char* base_dir = g_fileDirName[enum_mian_disk_workouts];
                // 如果路径以"0:/"开头，跳过这个前缀
                if (strncmp(base_dir, "0:/", 3) == 0) {
                    base_dir += 3;
                }

                // 构造文件路径
                snprintf(file_path, sizeof(file_path), "/%s/%s", base_dir, file_name);

                // 规范化路径（移除多余的'/'）
                char* src = file_path;
                char* dst = file_path;
                char prev = '\0';
                while (*src) {
                    if (*src != '/' || prev != '/') {
                        *dst = *src;
                        prev = *src;
                        dst++;
                    }
                    src++;
                }
                *dst = '\0';


                // 获取文件时间戳
                struct stat file_stat;
                memset(&file_stat, 0, sizeof(struct stat));

                int ret = stat(file_path, &file_stat);
                if (ret == 0) {
                    if (file_stat.st_mtime > latest_time) {
                        latest_time = file_stat.st_mtime;
                        s_latest_workout_index = index;
                    }
                }
            }
            index++;
        }

    } else {
        s_latest_workout_index = 0xFFFF;
    }
}

int8_t fit_workout_init(void)
{
    // 初始化链表
    qw_mlist_init(&s_workout_list);

    // 扫描文件
    fit_workout_scan_files();

    return 0;
}

void fit_workout_deinit(void)
{
    // 释放链表
    qw_mlist_uninit(&s_workout_list);
    s_workout_count = 0;
    s_latest_workout_index = 0xFFFF;  // 重置最新文件索引
}

uint16_t fit_workout_get_count(void)
{
    return s_workout_count;
}

int8_t fit_workout_get_info(uint16_t index, workout_info_t* info)
{
    if (info == NULL || index >= s_workout_count) {
        return -1;
    }

    char course_path[150] = {0};
    qw_mlist_t* mnode = NULL;
    int cnt = 0;

    // 遍历链表找到对应索引的文件
    FOR_MLIST(mnode, &s_workout_list) {
        if (cnt == index && mnode->data_size < 80) {
            // 构造完整路径
            memset(info->name, 0, sizeof(info->name));
            memcpy(info->name, mnode->mnode_data, mnode->data_size);
            info->name[mnode->data_size] = 0;

            // 先构造course_path,避免去掉数字和下划线影响路径
            sprintf(course_path, "%s/%s", g_fileDirName[enum_mian_disk_workouts], info->name);

            // 去掉.fit后缀
            char* dot = strrchr(info->name, '.');
            if (dot != NULL) {
                *dot = '\0';
            }

            // 如果以数字和下划线开头,去掉数字和下划线
            char *p = info->name;
            while (*p >= '0' && *p <= '9') {
                p++;
            }
            if (*p == '_') {
                p++;
                memmove(info->name, p, strlen(p) + 1);
            }

            // 预览课程文件获取信息
            hWorkout workout = workout_preview(course_path);
            if (workout != NULL) {
                // 获取步骤数
                info->total_steps = workout_steps_cnt_get(workout);

                // 获取总时间
                workout_duration_type_e duration_type;
                uint32_t duration_val;
                if (workout_duration_type_check(workout, &duration_type, &duration_val) == LIB_NOERROR) {
                    if (duration_type == WORKOUT_DURATION_TIME) {
                        info->total_time = duration_val;
                    } else {
                        info->total_time = 0;
                    }
                } else {
                    info->total_time = 0;
                }

                return 0;
            }
            break;
        }
        cnt++;
    }

    return -1;
}


/**
 * @brief 删除单个训练课程文件
 * @param[in] index 要删除的文件索引
 * @return 0:成功 -1:失败
 */
int8_t fit_workout_delete_file(uint16_t index)
{
    if (index >= s_workout_count) {
        return -1;
    }

    char course_path[150] = {0};
    qw_mlist_t* mnode = NULL;
    int cnt = 0;

    // 遍历链表找到对应索引的文件
    FOR_MLIST(mnode, &s_workout_list) {
        if (cnt == index && mnode->data_size < 80) {
            // 构造完整路径
            char file_name[80] = {0};
            memcpy(file_name, mnode->mnode_data, mnode->data_size);
            file_name[mnode->data_size] = 0;

            sprintf(course_path, "%s/%s", g_fileDirName[enum_mian_disk_workouts], file_name);

            // 删除文件
            if (fs_del_files(course_path) == 0) {
                // 从链表中移除该节点
                qw_mlist_data_remove_node(mnode);
                s_workout_count--;
                return 0;
            }
            break;
        }
        cnt++;
    }

    return -1;
}

/**
 * @brief 删除所有训练课程文件
 * @return 0:成功 -1:失败
 */
int8_t fit_workout_delete_all(void)
{
    qw_mlist_t* mnode = NULL;
    char course_path[150] = {0};
    bool success = true;

    // 遍历链表删除所有文件
    FOR_MLIST(mnode, &s_workout_list) {
        if (mnode->data_size < 80) {
            char file_name[80] = {0};
            memcpy(file_name, mnode->mnode_data, mnode->data_size);
            file_name[mnode->data_size] = 0;

            sprintf(course_path, "%s/%s", g_fileDirName[enum_mian_disk_workouts], file_name);

            // 删除文件
            if (fs_del_files(course_path) != 0) {
                success = false;
            }
        }
    }

    // 清空链表
    qw_mlist_uninit(&s_workout_list);
    s_workout_count = 0;

    return success ? 0 : -1;
}

// 辅助函数：设置步骤标题
static void set_step_title(uint8_t intensity, char* title, size_t size) {
    switch (intensity) {
        case enumIntensityWarmup:
            strncpy(title, "warm up", size - 1);
            break;
        case enumIntensityActive:
            strncpy(title, "active", size - 1);
            break;
        case enumIntensityRest:
            strncpy(title, "rest", size - 1);
            break;
        case enumIntensityCooldown:
            strncpy(title, "cool down", size - 1);
            break;
        default:
            title[0] = '\0';
            break;
    }
}

// 辅助函数：格式化目标范围字符串
static void format_target_str(uint8_t target_type, uint16_t low, uint16_t high, char* str, size_t size) {

    if (str == NULL || size == 0) {
        return;
    }
    // 确保字符串初始化为空
    str[0] = '\0';
    
    if (target_type == enumHeartRateBpm || target_type == enumHeartRateZone) {
        snprintf(str, size, "%u-%u bpm", (unsigned int)low, (unsigned int)high);
    } else if (target_type == enumPowerWatts || target_type == enumPowerZone) {
        snprintf(str, size, "%u-%u w", (unsigned int)low, (unsigned int)high);
    } else if (target_type == enumSpeedMms) {
        if (get_unit_distance() == 1) {
            // 使用整数计算避免浮点数问题
            int kmh_low = (int)((low * 36 + 5000) / 10000); // 转换为km/h并四舍五入到1位小数
            int kmh_high = (int)((high * 36 + 5000) / 10000);
            snprintf(str, size, "%d-%d kmh", 
                    kmh_low, 
                    kmh_high);
        } else {
            // 使用整数计算避免浮点数问题
            int mph_low = (int)((low * 36 * 621 + 500000) / 1000000);
            int mph_high = (int)((high * 36 * 621 + 500000) / 1000000);
            snprintf(str, size, "%d.%d-%d.%d mph", 
                    mph_low / 10, mph_low % 10, 
                    mph_high / 10, mph_high % 10);
        }
    } else if (target_type == enumCadRpm) {
        snprintf(str, size, "%u-%u rpm", (unsigned int)low, (unsigned int)high);
    } else if (target_type == enumPercentOfFtp || target_type == enumPercentOfMaxHr) {
        snprintf(str, size, "%u-%u%%", (unsigned int)low, (unsigned int)high);
    } else {
        str[0] = '\0';
    }
}

// 辅助函数：格式化持续时间字符串
static void format_duration_str(uint8_t duration_type, uint32_t duration_val, char* str, size_t size) {
    switch (duration_type) {
        case enumDurationOfTime:
            snprintf(str, size, "%02u:%02u:%02u",
                    duration_val / 3600,
                    (duration_val / 60) % 60,
                    duration_val % 60);
            break;
        case enumDurationOfDistance:
            snprintf(str, size, "%.2f", duration_val / 100000.0f);
            break;
        case enumDurationOfCalories:
            snprintf(str, size, "%u", duration_val);
            break;
        default:
            str[0] = '\0';
            break;
    }
}

void set_course_detail_info(void* workout)
{
    // 获取总步骤数
    s_course_detail.total_steps = workout_steps_cnt_get(workout);
    s_course_detail.sport_category = workout_sport_type_get(workout);
    s_course_detail.test_type = workout_test_type_get(workout);
    s_course_detail.repeat_groups = 0;
    s_course_detail.display_steps = 0;

    // 获取总时间
    workout_duration_type_e duration_type;
    uint32_t duration_val;
    if (workout_duration_type_check(workout, &duration_type, &duration_val) == LIB_NOERROR) {
        if (duration_type == WORKOUT_DURATION_TIME) {
            s_course_detail.total_time = duration_val;
        }
    }

    // 处理每个步骤
    uint8_t current_repeat_group = 0;
    uint8_t repeat_start_index = 0;
    bool in_repeat_sequence = false;
    s_course_detail.display_steps = 0;

    // 第一遍扫描：找出所有重复组的起始和结束位置
    for (uint16_t i = 0; i < s_course_detail.total_steps; i++) {
        wkt_step_t step_data = {0};
        workout_step_get(workout, i, &step_data);

        workout_step_detail_t* step = &s_course_detail.steps[s_course_detail.display_steps];

        // 设置基本信息
        step->step_index = i + 1;
        step->intensity = step_data.intensity;
        step->target_type = step_data.target;
        step->duration_type = step_data.duration;
        step->duration_val = step_data.duration_val;
        step->target_range[0] = step_data.target_range[0];
        step->target_range[1] = step_data.target_range[1];
        step->is_repeat_start = 0;
        step->is_repeat_end = 0;

        // 设置步骤标题
        set_step_title(step_data.intensity, step->title, sizeof(step->title));
        format_duration_str(step_data.duration, step_data.duration_val,
                          step->time, sizeof(step->time));
        format_target_str(step_data.target, step_data.target_range[0],
                         step_data.target_range[1], step->target_str,
                         sizeof(step->target_str));

        // 处理重复步骤
        if (step_data.repeat_type == enumStepTypeReStart) {
            current_repeat_group++;
            s_course_detail.repeat_groups++;
            repeat_start_index = s_course_detail.display_steps;
            in_repeat_sequence = true;

            step->repeat_group = current_repeat_group;
            step->is_repeat_start = 1;
            step->is_repeat_end = 0;
            step->repeat_times = 0;

            s_course_detail.display_steps++;
        } else if (step_data.repeat_type == enumStepTypeReEnd ||
                  step_data.repeat_type == enumStepTypeReOne) {
            uint8_t repeat_times = get_repeat_step_count(workout_step_param_get(enumWorkoutPreview)->workout_step, i);

            if (step_data.repeat_type == enumStepTypeReOne) {
                // 单步重复
                step->repeat_group = current_repeat_group;
                step->is_repeat_start = 1;
                step->is_repeat_end = 1;
                step->repeat_times = repeat_times;
                s_course_detail.display_steps++;
            } else {
                // 找到重复组的最后一个显示步骤，设置结束标志和重复次数
                if (repeat_start_index < s_course_detail.display_steps) {
                    workout_step_detail_t* last_step = &s_course_detail.steps[s_course_detail.display_steps - 1];
                    last_step->is_repeat_end = 1;
                    last_step->repeat_times = repeat_times;
                }
            }

            current_repeat_group = 0;
            in_repeat_sequence = false;
        } else if (!in_repeat_sequence) {
            // 非重复步骤，直接添加
            step->repeat_group = 0;
            step->repeat_times = 0;
            step->is_repeat_start = 0;
            step->is_repeat_end = 0;
            s_course_detail.display_steps++;
        } else if (in_repeat_sequence && repeat_start_index == s_course_detail.display_steps - 1) {
            // 重复组内的第一个步骤，需要显示
            step->repeat_group = current_repeat_group;
            step->repeat_times = 0;
            step->is_repeat_start = 0;
            step->is_repeat_end = 0;
            s_course_detail.display_steps++;
        }
    }
}

int8_t fit_workout_get_course_detail(uint16_t index)
{
    if (index >= s_workout_count) {
        return -1;
    }

    qw_mlist_t* mnode = NULL;
    int cnt = 0;

    // 遍历链表找到对应索引的文件
    FOR_MLIST(mnode, &s_workout_list) {
        if (cnt == index && mnode->data_size < 80) {
            // 设置当前课程索引
            s_course_detail.index = index;

            // 构造完整路径
            memset(s_course_detail.name, 0, sizeof(s_course_detail.name));
            memcpy(s_course_detail.name, mnode->mnode_data, mnode->data_size);
            s_course_detail.name[mnode->data_size] = 0;

            snprintf(s_course_detail.file_path, sizeof(s_course_detail.file_path), "%s/%s", g_fileDirName[enum_mian_disk_workouts], s_course_detail.name);

            // 去掉.fit后缀
            char *dot = strstr(s_course_detail.name, ".fit");
            if (dot) {
                *dot = '\0';
            }

            // 如果以数字和下划线开头,去掉数字和下划线
            char *p = s_course_detail.name;
            while (*p >= '0' && *p <= '9') {
                p++;
            }
            if (*p == '_') {
                p++;
                memmove(s_course_detail.name, p, strlen(p) + 1);
            }


            // 预览课程文件获取信息
            hWorkout workout = workout_preview(s_course_detail.file_path);
            if (workout == NULL) {
                rt_kprintf("%s %d workout_preview failed\n", __func__,__LINE__);
                return -2;
            }

            set_course_detail_info(workout);

            return 0;
        }
        cnt++;
    }

    return -1;
}

workout_course_detail_t* fit_workout_get_course_detail_ptr(void)
{
    return &s_course_detail;
}

void fit_workout_start(void)
{
    workout_load_t info;
    info.datatype = DATATYPE_COURSE;
    info.file_path = s_course_detail.file_path;
    info.type = TYPE_COURSE_FILE;
    FIT_WORKOUT_LOG_D("%s %d: %s", __func__, __LINE__, info.file_path);
    workout_start(&info);
}

void fit_workout_factory_reset(void)
{
    fit_workout_init();
    fit_workout_delete_all();
    fit_workout_deinit();
}

static const poweronoff_ctrl_app_cb g_poweronoff_ctrl_app_cb = {
    // .factory_cb = fit_workout_factory_reset,
    .factory_cb = NULL,
    .poweron_cb = NULL,
    .poweroff_cb = NULL,
    .restore_cb = NULL,
};

static poweronoff_cb_info g_poweronoff_cb_info ={
    .id = POWERONOFF_CB_APP_FITWORKOUT,
    .type = POWERONOFF_CB_TYPE_APP,
    .source = 0,
    .priority = 1,
    .res = 0,
    .name = "fit_workout",
    .app_cb = &g_poweronoff_ctrl_app_cb,
};

static int fit_workout_poweronoff_init(void)
{
    poweronoff_ctrl_cb_register(&g_poweronoff_cb_info); //注册开关机回调
    return 0;
}

INIT_APP_EXPORT(fit_workout_poweronoff_init);

void fit_workout_set_index(uint16_t index)
{
    s_workout_index = index;
}

uint16_t fit_workout_get_index(void)
{
    return s_workout_index;
}

uint16_t fit_workout_get_latest_index(void)
{
    return s_latest_workout_index;
}

uint8_t fit_workout_get_sport_category(void)
{
    return s_course_detail.sport_category;
}

uint8_t fit_workout_get_test_type(void)
{
    return s_course_detail.test_type;
}

/**
 * @brief 将SPORTTYPE转换为对应的训练课程大类
 * @param sport_type SPORTTYPE运动类型
 * @return 对应的训练课程大类
 */
uint8_t fit_workout_get_category_by_sport_type(uint8_t sport_type)
{
    // 根据qw_dev_cfg.h中的SPORTTYPE定义进行转换
    // 跑步类型
    if (sport_type == SPORTSTYPE_RUNNING ||
        sport_type == SPORTSTYPE_TREADMILL ||
        sport_type == SPORTSTYPE_PLAYGROUND ||
        sport_type == SPORTSTYPE_TRAIL_RUNNING ||
        sport_type == SPORTSTYPE_WALKING ||
        sport_type == SPORTSTYPE_INDOOR_RUNNING) {
        return FIT_SPORT_CATEGORY_RUNNING;
    }
    // 骑行类型
    else if (sport_type == SPORTSTYPE_CYCLING ||
             sport_type == SPORTSTYPE_INDOOR_CYCLING ||
             sport_type == SPORTSTYPE_ROAD_CYCLING ||
             sport_type == SPORTSTYPE_MOUNTAIN_CYCLING ||
             sport_type == SPORTSTYPE_COMMUTING ||
             sport_type == SPORTSTYPE_TRIP_CYCLING) {
        return FIT_SPORT_CATEGORY_CYCLING;
    }
    // 游泳类型
    else if (sport_type == SPORTSTYPE_POOL_SWIMMING ||
             sport_type == SPORTSTYPE_OPEN_WATER_SWIMMING) {
        return FIT_SPORT_CATEGORY_SWIMMING;
    }
    // 室内健身
    else if (sport_type == SPORTSTYPE_STRENGTH_TRAINING ||
             sport_type == SPORTSTYPE_INDOOR_AEROBIC ||
             sport_type == SPORTSTYPE_ELLIPTICAL_MACHINE ||
             sport_type == SPORTSTYPE_ROWING_MACHINE) {
        return FIT_SPORT_CATEGORY_INDOOR;
    }
    // 户外运动
    else if (sport_type == SPORTSTYPE_MOUNTAINEERING ||
             sport_type == SPORTSTYPE_HIKING ||
             sport_type == SPORTSTYPE_SKIING ||
             sport_type == SPORTSTYPE_OUTDOOR_AEROBIC) {
        return FIT_SPORT_CATEGORY_OUTDOOR;
    }
    // 其他类型
    else {
        return FIT_SPORT_CATEGORY_OTHER;
    }
}

/**
 * @brief 判断课程是否适用于指定运动类型
 * @param workout_sport 课程的运动类型
 * @param sport_type SPORTTYPE运动类型
 * @return 是否适用 1:适用 0:不适用
 */
uint8_t is_workout_for_sport_type(uint8_t workout_sport, uint8_t sport_type)
{
    // 精确匹配
    if (workout_sport == enum_wkt_sport_generic) {
        return 1; // 通用类型适用于所有运动
    }

    // 转换SPORTTYPE到课程大类进行匹配
    uint8_t sport_category = fit_workout_get_category_by_sport_type(sport_type);

    switch (sport_category) {
        case FIT_SPORT_CATEGORY_RUNNING:
            return (workout_sport == enum_wkt_sport_running || workout_sport == enum_wkt_sport_walking);

        case FIT_SPORT_CATEGORY_CYCLING:
            return (workout_sport == enum_wkt_sport_cycling);

        case FIT_SPORT_CATEGORY_SWIMMING:
            return (workout_sport == enum_wkt_sport_swimming);

        case FIT_SPORT_CATEGORY_ALL:
            return 1; // 所有课程

        default:
            return 0; // 暂不支持其他类型
    }
}

/**
 * @brief 获取课程的运动类型
 * @param course_path 课程文件路径
 * @return 课程的运动类型
 */
static uint8_t get_workout_sport_type(const char* course_path)
{
    // 预览课程文件获取信息
    hWorkout workout = workout_preview(course_path);
    if (workout == NULL) {
        return enum_wkt_sport_generic;
    }

    return workout_sport_type_get(workout);
}

/**
 * @brief 获取指定运动类型适用的训练课程数量
 * @param sport_type SPORTTYPE定义的运动类型
 * @return 课程总数
 */
uint16_t fit_workout_get_count_by_sport_type(uint8_t sport_type)
{
    uint16_t count = 0;
    qw_mlist_t* mnode = NULL;
    char file_path[150] = {0};

    // 遍历所有课程，计算适用于指定运动类型的数量
    FOR_MLIST(mnode, &s_workout_list) {
        if (mnode->data_size < 80) {
            // 构造课程文件路径
            char file_name[80] = {0};
            memcpy(file_name, mnode->mnode_data, mnode->data_size);
            file_name[mnode->data_size] = 0;

            snprintf(file_path, sizeof(file_path), "%s/%s", g_fileDirName[enum_mian_disk_workouts], file_name);

            // 获取课程运动类型
            uint8_t workout_sport = get_workout_sport_type(file_path);

            // 判断是否适用于当前运动类型
            if (is_workout_for_sport_type(workout_sport, sport_type)) {
                count++;
            }
        }
    }

    return count;
}

/**
 * @brief 获取全局索引对应指定运动类型下索引的映射
 * @param sport_type SPORTTYPE定义的运动类型
 * @param type_index 该运动类型下的索引
 * @return 全局索引，未找到返回0xFFFF
 */
static uint16_t get_global_index_by_sport_type(uint8_t sport_type, uint16_t type_index)
{
    uint16_t count = 0;
    uint16_t global_index = 0;
    qw_mlist_t* mnode = NULL;

    // 遍历所有课程，查找指定运动类型下的第type_index个课程
    FOR_MLIST(mnode, &s_workout_list) {
        if (mnode->data_size < 80) {
            // 构造课程文件路径
            char file_path[150] = {0};
            char file_name[80] = {0};
            memcpy(file_name, mnode->mnode_data, mnode->data_size);
            file_name[mnode->data_size] = 0;

            snprintf(file_path, sizeof(file_path), "%s/%s", g_fileDirName[enum_mian_disk_workouts], file_name);

            // 获取课程运动类型
            uint8_t workout_sport = get_workout_sport_type(file_path);

            // 判断是否适用于当前运动类型
            if (is_workout_for_sport_type(workout_sport, sport_type)) {
                if (count == type_index) {
                    return global_index;
                }
                count++;
            }
        }
        global_index++;
    }

    return 0xFFFF; // 未找到
}

/**
 * @brief 获取指定运动类型下特定索引课程的信息
 * @param sport_type SPORTTYPE定义的运动类型
 * @param type_index 该运动类型下的索引
 * @param info 课程信息结构体指针
 * @return 0:成功 其他:失败
 */
int8_t fit_workout_get_info_by_sport_type(uint8_t sport_type, uint16_t type_index, workout_info_t* info)
{
    if (info == NULL) {
        return -1;
    }

    // 获取全局索引
    uint16_t global_index = get_global_index_by_sport_type(sport_type, type_index);
    if (global_index == 0xFFFF) {
        return -1;
    }

    // 使用全局索引获取课程信息
    return fit_workout_get_info(global_index, info);
}

/**
 * @brief 获取指定运动类型下特定索引课程的详细信息
 * @param sport_type SPORTTYPE定义的运动类型
 * @param type_index 该运动类型下的索引
 * @return 0:成功 其他:失败
 */
int8_t fit_workout_get_course_detail_by_sport_type(uint8_t sport_type, uint16_t type_index)
{
    // 获取全局索引
    uint16_t global_index = get_global_index_by_sport_type(sport_type, type_index);
    if (global_index == 0xFFFF) {
        return -1;
    }

    // 使用全局索引获取课程详细信息
    return fit_workout_get_course_detail(global_index);
}

/**
 * @brief 删除指定运动类型下特定索引的训练课程文件
 * @param sport_type SPORTTYPE定义的运动类型
 * @param type_index 该运动类型下的索引
 * @return 0:成功 -1:失败
 */
int8_t fit_workout_delete_file_by_sport_type(uint8_t sport_type, uint16_t type_index)
{
    // 获取全局索引
    uint16_t global_index = get_global_index_by_sport_type(sport_type, type_index);
    if (global_index == 0xFFFF) {
        return -1;
    }

    // 使用全局索引删除课程文件
    return fit_workout_delete_file(global_index);
}

/**
 * @brief 清除当前课程详情
 * @return 0:成功
 */
void fit_workout_clear_course_detail(void)
{
    s_course_detail.test_type = 0;
}

/**
 * @brief 根据传入的fit文件名启动训练
 * @param[in] filename fit文件名（不含路径）
 * @return 0:成功 -1:文件不存在 -2:加载失败
 */
int8_t fit_workout_start_by_filename(const char* filename)
{
    if (filename == NULL || strlen(filename) == 0) {
        return -1;
    }
    FIT_WORKOUT_LOG_D("%s %d: Filename: %s", __func__, __LINE__, filename);

    // 构造完整路径，参考fit_workout_scan_files的处理方式
    char course_path[150] = {0};
    
    // 处理基础目录路径
    const char* base_dir = g_fileDirName[enum_mian_disk_workouts];
    // 如果路径以"0:/"开头，跳过这个前缀
    if (base_dir != NULL && strncmp(base_dir, "0:/", 3) == 0) {
        base_dir += 3;
    }
    
    // 构造文件路径
    snprintf(course_path, sizeof(course_path), "/%s/%s", base_dir ? base_dir : "WORKOUTS", filename);
    
    // 规范化路径（移除多余的'/'）
    char normalized_path[150] = {0};
    char* src = course_path;
    char* dst = normalized_path;
    char prev = '\0';
    while (*src) {
        if (*src != '/' || prev != '/') {
            *dst = *src;
            prev = *src;
            dst++;
        }
        src++;
    }
    *dst = '\0';
        
    // 检查文件是否存在
    struct stat file_stat;
    if (stat(normalized_path, &file_stat) != 0) {
        FIT_WORKOUT_LOG_D("%s %d: File not found: %s", __func__, __LINE__, normalized_path);
        return -1;
    }
    
    fit_workout_init();
    
    // 查找文件在链表中的索引
    qw_mlist_t* mnode = NULL;
    uint16_t index = 0;
    bool found = false;

    FOR_MLIST(mnode, &s_workout_list) {
        if (mnode->data_size < 80) {
            char file_name[80] = {0};
            memcpy(file_name, mnode->mnode_data, mnode->data_size);
            file_name[mnode->data_size] = 0;
            
            if (strcmp(file_name, filename) == 0) {
                found = true;
                break;
            }
        }
        index++;
    }

    if (!found) {
        FIT_WORKOUT_LOG_E("%s %d: File not in workout list: %s", __func__, __LINE__, filename);
        fit_workout_deinit();
        return -1;
    }

    hWorkout workout = workout_preview(course_path);
    if (workout == NULL) {
        FIT_WORKOUT_LOG_E("%s %d workout_preview failed", __func__,__LINE__);
        return -2;
    }
    
    s_course_detail.sport_category = workout_sport_type_get(workout);

    fit_workout_deinit();

    // 启动训练
    workout_load_t info;
    info.datatype = DATATYPE_COURSE;
    info.file_path = normalized_path;
    info.type = TYPE_COURSE_FILE;
    fit_workout_set_index(index);
    workout_start(&info);

    return 0;
}

/**
 * @brief 获取当前课程的运动类型
 * @return 课程的运动类型 (SPORTTYPE类型的值)
 */
uint8_t fit_workout_get_current_course_sport_type(void)
{
    uint8_t sport_category = s_course_detail.sport_category;
    
    // 根据课程的sport_category返回对应的SPORTTYPE
    switch (sport_category) {
        case enum_wkt_sport_running:
            return TOTAL_SPORTS_TYPE_RUNNING;
            
        case enum_wkt_sport_cycling:
            return TOTAL_SPORTS_TYPE_TREADMILL;

        default:
            // 如果是通用类型或其他类型，返回跑步作为默认值
            return TOTAL_SPORTS_TYPE_ALL;
    }
}
