/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   view_page_model.c
@Time    :   2024/12/19 14:04:49
*
**************************************************************************/

#include "view_page_model.h"
#include <string.h>
#include <stdio.h>
#include "igs_global.h"
#include "igs_dev_config.h"
#include "basictype.h"
#include "valid_data.h"
#include "rtthread.h"
#include "sunTime.h"
#include "data_convert.h"
#include "task_thread_helper.h"
#include "backlight_module/backlight_module.h"
#include "navi_srv_module/navi_srv_module.h"
#include "focus_mode_srv/focus_mode_srv.h"
#include "watch_lock_srv/watch_lock_srv.h"
#include "cfg_header_def.h"
#ifndef SIMULATOR
#include "algo_service_sport_status.h"
#else
#include "activity_record/activity_fit_app.h"
#include "ble_data_inf.h"
#endif
#include "hr_push.h"
//extern value
static DEV_TIME* p_sys_time = &g_sysTime;

//function
void get_real_time(TimeSplit* out_time)
{
	if (out_time != NULL)
	{
		out_time->hour = p_sys_time->sysTime_local_t.tm_hour;
		out_time->mday = p_sys_time->sysTime_local_t.tm_mday;
		out_time->min = p_sys_time->sysTime_local_t.tm_min;
		out_time->mon = p_sys_time->sysTime_local_t.tm_mon;
		out_time->sec = p_sys_time->sysTime_local_t.tm_sec;
		out_time->wday = p_sys_time->sysTime_local_t.tm_wday;
		out_time->year = p_sys_time->sysTime_local_t.tm_year;
		out_time->summer_time = p_sys_time->sysTime_local_t.tm_isdst;
	}
}

SPORTTYPE get_sport_type_by_total_type(uint8_t total_type, int index)
{
    // valid_count用于记录当前已经匹配到的符合total_type类型的运动项目的数量
    // 当valid_count等于传入的index时,表示找到了目标位置的运动类型
    int valid_count = 0;
    for (int i = 0; i < get_custom_sports_count() && i < SPORTSTYPE__MAX; i++)
    {
        SPORTTYPE type = get_custom_sports_context(i);
        bool match = false;
        // 根据运动大类过滤
        switch(total_type) {
        case TOTAL_SPORTS_TYPE_RUNNING:
            match = (type >= SPORTSTYPE_RUNNING && type <= SPORTSTYPE_INDOOR_RUNNING);
            break;
        case TOTAL_SPORTS_TYPE_TREADMILL:
            match = (type >= SPORTSTYPE_CYCLING && type <= SPORTSTYPE_TRIP_CYCLING);
            break;
		case TOTAL_SPORTS_TYPE_NAVI:
            match = navi_srv_is_support_sport_type(type);
            break;
        default:
            match = true;
            break;
        }
        if (match) {
            if (valid_count == index) {
                return type;
            }
            valid_count++;
        }
    }
    return SPORTSTYPE__MAX;
}

bool get_console_enable(CONSOLETYPE type)
{
	return true;
}

CONSOLESTATUS get_console_status(CONSOLETYPE type)
{
	CONSOLESTATUS res = SWITCH_NONE;
	uint8_t status;
	int disable_by_sport = 0,disable_by_ps = 0,disable_by_sleep = 0;
#ifndef SIMULATOR
	if(get_sport_status() > enum_status_free)//运动准备页开始禁用
#else
	if(get_simulator_sport_status() > ACTIVITY_FIT_IDLE)//运动准备页开始禁用
#endif // !SIMULATOR
	{
		disable_by_sport = TOOL_DISABLE_FLAG;
	}

	if (get_sleep_state(true))
	{
		disable_by_sleep = TOOL_DISABLE_FLAG;
        // if (get_sleep_sport())
        // {
        //     disable_by_sleep = 0;
        // }
	}
    if (get_power_save_setting() != POWER_SAVE_NO)
    {
        disable_by_ps = TOOL_DISABLE_FLAG;
    }

	switch (type)
	{
	case CONSOLETYPE_FOCUS_MODE:
		status = get_dnd_state(false);
		if (status != 0)
		{
			res = SWITCH_ON;
		}
		else
		{
			res = SWITCH_OFF;
		}
		break;
	case CONSOLETYPE_SLEEP_MODE:
		status = get_sleep_state(false);
		if (status != 0)
		{
			res = SWITCH_ON;// | disable_by_sport;
		}
		else
		{
			res = SWITCH_OFF;// | disable_by_sport;
		}
		break;
	case CONSOLETYPE_BRIGHTNESS:
		status = get_light_type();
		if (status != 0)
		{
			res =  SWITCH_ON | disable_by_ps | disable_by_sleep;
		}
		else
		{
			res = SWITCH_OFF | disable_by_ps | disable_by_sleep;
		}
		break;
	case CONSOLETYPE_ALWAYS_ON:
		status = get_always_on();
		if (status != 0)
		{
			res = SWITCH_ON | disable_by_ps | disable_by_sleep;
		}
		else
		{
			res = SWITCH_OFF | disable_by_ps | disable_by_sleep;
		}
		break;
	case CONSOLETYPE_RAISE_AWAKE:
		status = get_lift_wrist();
		if (status != 0)
		{
			res = SWITCH_ON | disable_by_sleep;
		}
		else
		{
			res = SWITCH_OFF | disable_by_sleep;
		}
		break;
	case CONSOLETYPE_LOCK:
		if (get_touch_lock_state())
		{
			res = SWITCH_ON;
		}
		else
		{
			res = SWITCH_OFF;
		}
		break;
	case CONSOLETYPE_LOCKALL:
		if (get_watch_lock_state())
		{
			res = SWITCH_ON;
		}
		else
		{
			res = SWITCH_OFF;
		}
		break;
	case CONSOLETYPE_BATTERY_SAVE:
		//判断常规省电模式是否开启
		if (get_power_save_setting() == POWER_SAVE_NO)
		{
			res = SWITCH_OFF | disable_by_sport;
		}
		else
		{
			res = SWITCH_ON | disable_by_sport;
		}
		break;
	case CONSOLETYPE_HRM:
		if (get_hr_push_en() == true)//开启
		{
			res = SWITCH_ON;
		}
		else
		{
			res = SWITCH_OFF;
		}
		break;
	case CONSOLETYPE_METRONOME:
	case CONSOLETYPE_BREATH_TRAINING:
		res = ENTRANCE | disable_by_sport;
		break;
	default:
		res = SWITCH_NONE;
		break;
	}

	return res;
}

//app & os operate function

void start_new_gui_thread(task_process process, void* arg, task_finished_notify notify)
{
	submit_long_task(process, arg, notify);
}

//real time function

static SPORTTYPE g_select_sport_type = SPORTSTYPE__MAX;

void set_select_sport_type(SPORTTYPE type)
{
	g_select_sport_type = type;
}

SPORTTYPE get_select_sport_type()
{
	return g_select_sport_type;
}
