/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : sensor_hub.c
*<AUTHOR>
*@Date : 2025/01/15
*@Description : Software uart driver header file
************************************************************************/
#include "sensor_hub.h"
#include "sensor_hub_app_service.h"
#include "cmsis_os2.h"
#include <rtthread.h>
#include <string.h>
#include "ipc_config.h"
#include "ipc_queue.h"


#define SENSOR_CORE_BRIDGE_MAX_XFER_DATA_SIZE      (512)
#define SENSOR_CORE_BRIDGE_TX_MAILBOX_MAX          (24)
#define CORE_BRIDGE_TX_STACK_SZ                    (2560)
#define CORE_BRIDGE_RX_STACK_SZ                    (4608)


static osThreadId_t sensor_core_bridge_tx_thread_id = NULL;
static bool sensor_core_bridge_inited = false;
static osMutexId_t sensor_core_bridge_tx_mutex_id = NULL;
static rt_sem_t ipc_sem_sensorhub;
static ipc_queue_handle_t ipc_sensorhub_queue;
static osSemaphoreId_t sensor_core_bridge_wait_rsp_id = NULL;

typedef struct
{
    int32_t  handle;
    uint16_t cmd_len;
    uint16_t timeout;
    uint8_t *cmd_buffer;
} SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T;

static const osMemoryPoolAttr_t MPAttr_sensor_hub = {
    .name = NULL,
    .attr_bits = 0,
    .cb_mem = NULL,
    .cb_size = 0,
    .mp_mem = NULL,
    .mp_size = 0,
};

static const osMessageQueueAttr_t MQAttr_sensor_hub = {
    .name = "sensor_core_bridge_tx_mailbox",
    .attr_bits = 0,
    .cb_mem = NULL,
    .cb_size = 0,
    .mq_mem = NULL,
    .mq_size = 0,
};

static const osMutexAttr_t MutexAttr_Sensor_Core_Bridge = {
    .name = "sensor_core_bridge_tx_mutex",
    .attr_bits = osMutexRecursive | osMutexPrioInherit | osMutexRobust,
    .cb_mem = NULL,
    .cb_size = 0U,
};

static uint64_t os_thread_def_stack_sensor_core_bridge_tx_thread[CORE_BRIDGE_TX_STACK_SZ / sizeof(uint64_t)];
static const osThreadAttr_t ThreadAttr_Sensor_Core_Bridge_Tx = {
    .name = "core_bridge_tx_thread",
    .attr_bits = osThreadDetached,
    .cb_mem = NULL,
    .cb_size = 0U,
    .stack_mem = os_thread_def_stack_sensor_core_bridge_tx_thread,
    .stack_size = CORE_BRIDGE_TX_STACK_SZ,
    .priority = osPriorityHigh1,
    .tz_module = 1U,                  // indicate calls to secure mode
    .reserved = 0U,
};

static uint64_t os_thread_def_stack_sensor_core_bridge_rx_thread[CORE_BRIDGE_RX_STACK_SZ / sizeof(uint64_t)];
static const osThreadAttr_t ThreadAttr_Sensor_Core_Bridge_Rx = {
    .name = "core_bridge_rx_thread",
    .attr_bits = osThreadDetached,
    .cb_mem = NULL,
    .cb_size = 0U,
    .stack_mem = os_thread_def_stack_sensor_core_bridge_rx_thread,
    .stack_size = CORE_BRIDGE_RX_STACK_SZ,
    .priority = osPriorityHigh1,
    .tz_module = 1U,                  // indicate calls to secure mode
    .reserved = 0U,
};


static uint8_t sensor_core_bridge_tx_mailbox_heap_pool[SENSOR_CORE_BRIDGE_MAX_XFER_DATA_SIZE*(SENSOR_CORE_BRIDGE_TX_MAILBOX_MAX/2)];
static osMemoryPoolId_t   sensor_hub_mp_id;
static osMessageQueueId_t sensor_hub_mq_id;
static struct rt_memheap sensor_core_bridge_tx_mailbox_heap;
static osThreadId_t sensor_core_bridge_rx_thread_id = NULL;


void sensor_core_bridge_tx_mailbox_heap_init(void)
{
    rt_memheap_init(&sensor_core_bridge_tx_mailbox_heap, "tx_heap",
                    sensor_core_bridge_tx_mailbox_heap_pool,
                    sizeof(sensor_core_bridge_tx_mailbox_heap_pool));
}

void *sensor_core_bridge_tx_mailbox_heap_malloc(uint32_t size)
{
    void *ptr = NULL;
    if (size){
        ptr = rt_memheap_alloc(&sensor_core_bridge_tx_mailbox_heap, size);
        if (ptr == NULL) {
            SENSORHUB_D("%s size:%d", __func__, size);
            return NULL;
        }
    }
    return ptr;
}

void sensor_core_bridge_tx_mailbox_heap_free(void *rmem)
{
    if (rmem) {
        rt_memheap_free(rmem);
    }
}

static int32_t sensor_core_bridge_tx_mailbox_init(void)
{
    if (sensor_hub_mp_id == NULL && sensor_hub_mq_id == NULL) {
        sensor_hub_mp_id = osMemoryPoolNew(SENSOR_CORE_BRIDGE_TX_MAILBOX_MAX,
                                               sizeof(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T), &MPAttr_sensor_hub);
        sensor_hub_mq_id = osMessageQueueNew(SENSOR_CORE_BRIDGE_TX_MAILBOX_MAX,
                                                 sizeof(void *), &MQAttr_sensor_hub);
        if (NULL == sensor_hub_mp_id || NULL == sensor_hub_mq_id) {
            SENSORHUB_E("Failed to Create core bridge mailbox");
            return -1;
        }
    }
    return 0;
}

static int32_t sensor_core_bridge_tx_mailbox_get(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T** msg_p)
{
    osStatus_t status;
    void *mail;

    status = osMessageQueueGet(sensor_hub_mq_id, &mail, NULL, osWaitForever);
    if (status == osOK) {
        *msg_p = (SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T *)mail;
        return 0;
    }
    SENSORHUB_E("lcpu core bridge tx error %d", __LINE__);
    return -1;
}

static int32_t sensor_core_bridge_tx_mailbox_raw(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T** msg_p)
{
    osStatus_t status;
    void *mail;
    status = osMessageQueueGet(sensor_hub_mq_id, &mail, NULL, 0);
    if (status == osOK)
    {
        *msg_p = (SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T *)mail;
        return 0;
    }
    SENSORHUB_E("lcpu core bridge mailbox error %d",__LINE__);
    return -1;
}

static int32_t sensor_core_bridge_tx_mailbox_free(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T* msg_p)
{
    osStatus_t status;
    sensor_core_bridge_tx_mailbox_heap_free((void *)msg_p->cmd_buffer);
    status = osMemoryPoolFree(sensor_hub_mp_id, msg_p);
    return (int32_t)status;
}

static int32_t sensor_core_bridge_tx_mailbox_put(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T* msg_src)
{
    SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T *msg_p = NULL;
    osStatus_t status;

    msg_p = (SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T*)osMemoryPoolAlloc(sensor_hub_mp_id, 0);

    if (!msg_p) {
        SENSORHUB_E("core bridge mailbox alloc error dump");
        for (uint8_t i = 0; i < SENSOR_CORE_BRIDGE_TX_MAILBOX_MAX; i++){
            sensor_core_bridge_tx_mailbox_raw(&msg_p);
            SENSORHUB_E("ctrl_mailbox:DUMP: handle=%s", msg_p->handle);
        }
        SENSORHUB_E("core bridge mailbox Alloc error dump end");
        SENSORHUB_E("core bridge mailbox reAlloc New");
        msg_p = (SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T*)osMemoryPoolAlloc(sensor_hub_mp_id, 0);
    }

    if (msg_p == NULL) {
        SENSORHUB_E("core bridge mailbox Alloc error");
        return -1;
    }

    msg_p->handle  = ipc_sensorhub_queue;
    msg_p->cmd_len = msg_src->cmd_len;
    msg_p->cmd_buffer = msg_src->cmd_buffer;
    msg_p->timeout = msg_src->timeout;

    status = osMessageQueuePut(sensor_hub_mq_id, &msg_p, 0U, 0U);
    if (status != osOK) {
        SENSORHUB_E("lcpu core bridge mailbox put error");
    }
    return (int32_t)status;
}

void sensor_core_bridge_send_data_with_waiting_rsp(SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T *msg_p)
{
    uint32_t  bytes_written = 0;
    osStatus_t sta;
    uint32_t stime = 0;
    uint32_t etime = 0;

    bytes_written = ipc_queue_write(msg_p->handle, msg_p->cmd_buffer, msg_p->cmd_len, 1000);
    if (bytes_written != msg_p->cmd_len) {
        SENSORHUB_D("tx data len is invaild: %d", bytes_written);
    }
    stime = rt_tick_get();
    sta = osSemaphoreAcquire(sensor_core_bridge_wait_rsp_id, msg_p->timeout);
    etime = rt_tick_get();
    if (sta != osOK)
    {
        SENSORHUB_D("%s err = %d", __func__, sta);
        SENSORHUB_D("core bridge:wait rsp timeout(%d)ms", (etime-stime)*(1000u / RT_TICK_PER_SECOND));
    }
}


void sensor_core_bridge_tx_thread(const void *arg)
{
    SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T *msg_p = NULL;
    uint32_t  bytes_written = 0;

    while(1) {
        if (0 == sensor_core_bridge_tx_mailbox_get(&msg_p)) {
            if (msg_p->timeout == 0) {
                bytes_written = ipc_queue_write(msg_p->handle, msg_p->cmd_buffer, msg_p->cmd_len, 1000);  //要注意cmd_len的长度
                if (bytes_written != msg_p->cmd_len) {
                    SENSORHUB_D("tx data len is invaild: %d", bytes_written);
                }
            } else {
                sensor_core_bridge_send_data_with_waiting_rsp(msg_p);
            }
        }
        sensor_core_bridge_tx_mailbox_free(msg_p);
    }
}

static void sensor_core_bridge_tx_thread_init(void)
{
    if (sensor_core_bridge_tx_mutex_id == NULL) {
        sensor_core_bridge_tx_mutex_id = osMutexNew(&MutexAttr_Sensor_Core_Bridge);
    }

    sensor_core_bridge_tx_mailbox_heap_init();
    sensor_core_bridge_tx_mailbox_init();
    if (sensor_core_bridge_tx_thread_id == NULL) {
        sensor_core_bridge_tx_thread_id =
            osThreadNew((osThreadFunc_t)sensor_core_bridge_tx_thread,
                        NULL, &ThreadAttr_Sensor_Core_Bridge_Tx);
    }
}

int32_t sensor_core_bridge_send_cmd(uint8_t *p_buff, uint16_t length, uint16_t timeout_ms)
{
    if (!sensor_core_bridge_inited) {
        return 0;
    }

    SENSOR_CORE_BRIDGE_TX_MAILBOX_PARAM_T msg = {0};
    int ret = 0;
    if (length > SENSOR_CORE_BRIDGE_MAX_XFER_DATA_SIZE) {
        SENSORHUB_D("%s p_buff overflow %d", __func__, length);
        return -1;
    }

    msg.timeout = timeout_ms;
    msg.cmd_len = length;

    if (length > 0) {
        msg.cmd_buffer = (uint8_t *)sensor_core_bridge_tx_mailbox_heap_malloc(msg.cmd_len);
        memcpy((void *)msg.cmd_buffer, (void *)p_buff, msg.cmd_len);
    } else {
        msg.cmd_buffer = NULL;
    }

    osMutexAcquire(sensor_core_bridge_tx_mutex_id, osWaitForever);
    ret = sensor_core_bridge_tx_mailbox_put(&msg);
    osMutexRelease(sensor_core_bridge_tx_mutex_id);

    return ret;
}

void sensor_core_bridge_cmd_rsp_handler(void)
{
    if (osOK == osSemaphoreRelease(sensor_core_bridge_wait_rsp_id)) {
        SENSORHUB_D("core bridge:<-----------RCV_RSP-------");
    }
}

static void sensor_core_bridge_rx_handler(uint8_t* p_data_buff, uint16_t length)
{
    sensor_hub_app_msg_received(p_data_buff, length);
}

static uint8_t read_data_from_ipc_queue(uint16_t size, uint8_t *data)
{
    uint32_t receive_size = ipc_queue_get_rx_size(ipc_sensorhub_queue);
    //SENSORHUB_D("%s enter rx_size = %d, except_size = %d", __func__, receive_size, size);
    if (receive_size < size) {
        do {
            if (RT_EOK == rt_sem_take(ipc_sem_sensorhub, RT_WAITING_FOREVER)) {
                receive_size = ipc_queue_get_rx_size(ipc_sensorhub_queue);
            }
        } while(receive_size < size);
    }
    ipc_queue_read(ipc_sensorhub_queue, data, size);
    //SENSORHUB_D("%s exit rx_size = %d", __func__, ipc_queue_get_rx_size(ipc_sensorhub_queue));

    return 0;
}

static void sensor_core_bridge_rx_thread(const void *arg)
{
    uint8_t rcv_tmp_buffer[512];
    sensorhub_msg_head_t msg_header;
    uint8_t ret;
    uint16_t crc_temp;
    memset(rcv_tmp_buffer, 0, sizeof(rcv_tmp_buffer));

    while (1) {
        ret = read_data_from_ipc_queue(sizeof(sensorhub_msg_head_t), rcv_tmp_buffer);
        if (ret == 0) {
            memcpy(&msg_header, rcv_tmp_buffer, sizeof(sensorhub_msg_head_t));
            crc_temp = crc_test((uint8_t *)&msg_header.data_len, sizeof(uint16_t));
            if (crc_temp == msg_header.crc) {
                memset(rcv_tmp_buffer, 0, sizeof(rcv_tmp_buffer));
                read_data_from_ipc_queue(msg_header.data_len, rcv_tmp_buffer);
                sensor_core_bridge_rx_handler(rcv_tmp_buffer, msg_header.data_len);
            } else {
                SENSORHUB_E("Lsensorhub rx err %d-%d", msg_header.data_len, msg_header.crc);
            }
        }
    }
}

static void sensor_core_bridge_rx_thread_init(void)
{
    if (sensor_core_bridge_rx_thread_id == NULL) {
        sensor_core_bridge_rx_thread_id =
            osThreadNew((osThreadFunc_t)sensor_core_bridge_rx_thread,
                        NULL, &ThreadAttr_Sensor_Core_Bridge_Rx);
    }
}

static int32_t ipc_sensor_hub_app_rx_ind(ipc_queue_handle_t handle, size_t size)
{
    if (NULL != ipc_sem_sensorhub && size != 0) {
        rt_sem_release(ipc_sem_sensorhub);
    }

    return 0;
}

void sensor_hub_app_init(void)
{
    rt_err_t result;
    ipc_queue_cfg_t q_cfg;

    q_cfg.qid = IPC_HL4_QUEUE;
    q_cfg.tx_buf_size = SENSORHUB_MSG_DATA_LEN;
    q_cfg.tx_buf_addr = LH4_IPC_TX_BUF_ADDR;
    q_cfg.tx_buf_addr_alias = LH4_IPC_TX_BUF_ADDR_ALIAS;
    q_cfg.rx_buf_addr = LH4_IPC_RX_BUF_ADDR;
    q_cfg.rx_ind = ipc_sensor_hub_app_rx_ind;
    q_cfg.user_data = 0;

    ipc_sem_sensorhub = (rt_sem_t)rt_sem_create("senmsg", 0, RT_IPC_FLAG_FIFO);
    ipc_sensorhub_queue = ipc_queue_init(&q_cfg);
    result = ipc_queue_open(ipc_sensorhub_queue);
    RT_ASSERT(0 == result);
}

int sensor_core_bridge_init(void)
{
    if (sensor_core_bridge_inited) {
        return 0;
    }

    sensor_core_bridge_wait_rsp_id = osSemaphoreNew(65535U, (uint32_t)0, NULL);
    sensor_hub_app_init();
    sensor_core_bridge_tx_thread_init();
    sensor_core_bridge_rx_thread_init();

    sensor_core_bridge_inited = true;

    return 0;
}
INIT_PREV_EXPORT(sensor_core_bridge_init);


