/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwHalfScreen.h
@Time    :   2024/12/23 19:32:29
*
**************************************************************************/


#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "Image/images.h"
#include "qw_os_gui.h"
#include "QwMenuItem/ItemBaseCtrl.hpp"
#include "QwHalfScreenType.h"

//文本类型
typedef enum {
    QHSTT_NUMBER = 0, //数字
    QHSTT_TEXT, //文字
} QHSTT_SUB_TITLE_TYPE;

class QwHalfScreen : public ItemBaseCtrl
{
private:
    Box high_bg_;
    Box low_bg_;
    QwHalfScreenUnion half_screen_union_;
    Container* high_contaner_;
    TextArea sub_title_;
    TextArea sub_text_;

    lv_font_t *title_font_;//副区域标题字体
    lv_font_t *text_font_;//副区域文本字体

    WideTextAction sub_text_wide_text_action_;//副文本的换行模式
    
    QW_HALF_SCREEN_TYPE type_;

    QHSTT_SUB_TITLE_TYPE sub_title_type_;

    QwCtrlEvent event_poster_;      				// 操作事件管理

public:
    QwHalfScreen();
    ~QwHalfScreen();


    /**
     * @brief 生成提示框
     */
    virtual void setup();

    /**
     * @brief 刷新
     */
    virtual void on_notify() override;

	void handleTickEvent() override;
	void handleKeyEvent(uint8_t c) override;
	void handleClickEvent(const ClickEvent& evt) override;
	void handleDragEvent(const DragEvent& evt) override;
	void handleGestureEvent(const GestureEvent& evt) override;

    /**
     * @brief 设置类型
     * @param type 类型
     */
    void set_type(QW_HALF_SCREEN_TYPE type);

    /**
     * @brief 设置类型
     * @param type 类型
     */
    Container* get_high_contaner();

    /**
     * @brief 设置主文本类型
     * @param t 文本内容
     */
    void set_sub_title_type(QHSTT_SUB_TITLE_TYPE t);

    /**
     * @brief 设置主文本内容
     * @param t 文本内容
     */
    void set_typed_dynamic_sub_title(TypedTextId t);

     /**
     * @brief 设置主文本内容
     * @param t 文本内容
     */
    void set_typed_dynamic_sub_text(TypedTextId t);

     /**
     * @brief 重置该组件
     */
    void set_reset();

    /**
     * @brief 设置副区域标题字体
     * @param font_ 字体
     */
    void set_sub_title_font(lv_font_t *font_);  

    /**
     * @brief 设置副区域文本字体
     * @param font_ 字体
     */
    void set_sub_text_font(lv_font_t *font_);

    /**
     * @brief 设置副文本的换行模式
     * @param action 换行模式
     */
    void set_sub_text_wide_text_action(WideTextAction action);
};

