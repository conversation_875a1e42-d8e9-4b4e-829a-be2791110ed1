/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AutoPageSetting.cpp
@Time    :   2024/12/13 19:12:47
*
**************************************************************************/
#include "SportsMenuModel.h"
#include "../navi_srv_module/navi_srv_module.h"

SportsMenuModel::SportsMenuModel() :
    return_page_(nullptr),
    sports_type_(TOTAL_SPORTS_TYPE_ALL),
	on_sports_select_(this, &SportsMenuModel::on_sports_select),
	on_set_sports_type_(this, &SportsMenuModel::on_set_sports_type),
	on_set_return_page_(this, &SportsMenuModel::on_set_return_page),
	on_set_refresh_flag_(this, &SportsMenuModel::on_set_refresh_flag)
{

}

SportsMenuModel::~SportsMenuModel()
{

}


/**
 * @brief PageModel::setup() 进入页面通知
 */
void SportsMenuModel::setup()
{
    if (navi_srv_get_prepare_status() >= NAVI_PREPARE_STATUS_READY)
    {
        sports_type_.get_val() = TOTAL_SPORTS_TYPE_NAVI;
    }
    for (int i = 0; i < SPORTS_MENU_SPORTS_NUM; i++)
    {
        sports_custom_[i] = get_sport_type_by_total_type(sports_type_.get_val(), i);
    }
    sports_count_ = get_valid_sports_count();
    playground_track_info_ = get_sport_track_info();
    pool_swimming_info_ = get_sport_swim_info();
}

/**
 * @brief PageModel::quit() 退出页面通知
 */
void SportsMenuModel::quit()
{

}

/**
 * @brief PageModel::notify() 更新检查, 在 PageBase::notify() 中调用
 */
void SportsMenuModel::notify()
{

}


// Notification
void SportsMenuModel::on_sports_select(uint8_t pressed)
{
    item_select_ = pressed;
    set_current_sport_mode((SPORTTYPE)sports_custom_[pressed].get_val());
}

void SportsMenuModel::on_set_sports_type(uint8_t t1)
{
    sports_type_ = t1;
}

void SportsMenuModel::on_set_return_page(char* t1)
{
    return_page_ = t1;
}

void SportsMenuModel::on_set_refresh_flag(uint8_t t1)
{
    refresh_flag_ = t1;// 0:不刷新 1:刷新
    if(t1)
    {
        item_select_ = 0;
        setup();
    }
}

// Parameters function
Parameters<uint8_t>& SportsMenuModel::get_sports_custom_data(int idx)
{
	if (idx < SPORTS_MENU_SPORTS_NUM)
    {
        return sports_custom_[idx];
    }
    else
    {
        return sports_custom_[0];
    }
}

uint8_t SportsMenuModel::get_valid_sports_count()
{
    uint8_t valid_count = 0;

    for(int i = 0; i < SPORTS_MENU_SPORTS_NUM; i++) {
        if(sports_custom_[i].get_val() != SPORTSTYPE__MAX) {
            valid_count++;
        }
    }

    return valid_count;
}

