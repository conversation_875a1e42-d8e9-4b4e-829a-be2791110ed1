{"Version": 1, "WorkspaceRootPath": "E:\\greeit_Qwos\\app\\project\\WR02_Win32\\project\\simulator\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\MvcApp.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Application.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Callback.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekPointChart.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\Navigation\\NavigationRoute\\NavigationRoute.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos_app\\sports_data\\sports_data_show.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\gui\\CtrlBase\\MvcAppEvent.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwAppScrollList\\QwAppScrollList.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\Summary.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\sifli\\rtos\\rtthread\\libcpu\\sim\\win32\\cpu_port.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummarySport\\SummarySportContainer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Tools\\FlashLight\\FlashLightView.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\FactoryPage\\FactoryGpsStartTest\\FactoryGpsStartTestModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepDailyPageView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Image\\images.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollList.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollBase.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\DrawableList.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\containers\\scrollers\\ScrollList.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\TestScrollListContainer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 5, "Title": "Application.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Application.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Application.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Application.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Application.cpp", "ViewState": "AgIAAGsBAAAAAAAAAAAUwHsBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-11T08:43:55.664Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "MvcAppEvent.hpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\gui\\CtrlBase\\MvcAppEvent.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\CtrlBase\\MvcAppEvent.hpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\gui\\CtrlBase\\MvcAppEvent.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\CtrlBase\\MvcAppEvent.hpp", "ViewState": "AgIAAF8AAAAAAAAAAAAUwGoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-11T07:26:33.525Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MvcApp.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\MvcApp.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\MvcApp.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\MvcApp.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\MvcApp.cpp", "ViewState": "AgIAAPsAAAAAAAAAAAAUwAgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-11T07:26:31.377Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "QwVerticalSwipeContainer.h", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.h", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.h", "ViewState": "AgIAAN4AAAAAAAAAAAAvwPQAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-06T06:08:23.969Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "SummaryView.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "ViewState": "AgIAACkAAAAAAAAAAAAewDwAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-09T13:45:51.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "QwVerticalSwipeContainer.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwVerticalSwipe\\QwVerticalSwipeContainer.cpp", "ViewState": "AgIAAD8EAAAAAAAAAAAvwGwEAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-09T13:35:49.487Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Callback.hpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Callback.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Callback.hpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Callback.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Callback.hpp", "ViewState": "AgIAAJwBAAAAAAAAAAAUwKgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-07T09:24:10.087Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Container.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "ViewState": "AgIAAFEBAAAAAAAAAAAUwGIBAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T06:02:33.19Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "QwWeekPointChart.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekPointChart.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekPointChart.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekPointChart.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekPointChart.cpp", "ViewState": "AgIAAN0AAAAAAAAAAAAUwOMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-12T02:39:01.549Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "NavigationRoute.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\Navigation\\NavigationRoute\\NavigationRoute.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\Navigation\\NavigationRoute\\NavigationRoute.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\Navigation\\NavigationRoute\\NavigationRoute.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\Navigation\\NavigationRoute\\NavigationRoute.cpp", "ViewState": "AgIAADEAAAAAAAAAAAAewD8AAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-12T02:29:22.02Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "sports_data_show.c", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\sports_data\\sports_data_show.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\sports_data\\sports_data_show.c", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\sports_data\\sports_data_show.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\sports_data\\sports_data_show.c", "ViewState": "AgIAACAAAAAAAAAAAAAAADMAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-06-11T09:27:06.805Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "SummaryDetailContainer.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.cpp", "ViewState": "AgIAAFEAAAAAAAAAAAAawF4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-09T05:43:52.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "SummaryView.h", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.h", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.h", "ViewState": "AgIAACgAAAAAAAAAAAAAACwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-10T12:55:43.694Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "lv_draw_engine.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "ViewState": "AgIAAJkBAAAAAAAAAAAawKkBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T05:59:29.58Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Screen.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "ViewState": "AgIAADkAAAAAAAAAAAAawEkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-09T09:28:38.672Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "QwAppScrollList.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwAppScrollList\\QwAppScrollList.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwAppScrollList\\QwAppScrollList.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwAppScrollList\\QwAppScrollList.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwAppScrollList\\QwAppScrollList.cpp", "ViewState": "AgIAAFMCAAAAAAAAAAAawGwCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-09T06:28:49.661Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "cpu_port.c", "DocumentMoniker": "E:\\greeit_Qwos\\sifli\\rtos\\rtthread\\libcpu\\sim\\win32\\cpu_port.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\sifli\\rtos\\rtthread\\libcpu\\sim\\win32\\cpu_port.c", "ToolTip": "E:\\greeit_Qwos\\sifli\\rtos\\rtthread\\libcpu\\sim\\win32\\cpu_port.c", "RelativeToolTip": "..\\..\\..\\..\\..\\sifli\\rtos\\rtthread\\libcpu\\sim\\win32\\cpu_port.c", "ViewState": "AgIAAIsAAAAAAAAAAAAawJsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-06-09T09:41:06.788Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "Summary.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\Summary.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\Summary.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\Summary.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\Summary.cpp", "ViewState": "AgIAAA4AAAAAAAAAAAAQwB4AAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T08:11:49.957Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "MenuCardModel.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "ViewState": "AgIAAGwEAAAAAAAAAAAawHsEAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T03:12:31.651Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "SummaryDetailContainer.h", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.h", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryDetail\\SummaryDetailContainer.h", "ViewState": "AgIAAAQAAAAAAAAAAAAawBQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-04T09:34:30.598Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "SummarySportContainer.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummarySport\\SummarySportContainer.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummarySport\\SummarySportContainer.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\SummarySport\\SummarySportContainer.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummarySport\\SummarySportContainer.cpp", "ViewState": "AgIAAHkAAAAAAAAAAAAawIkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-07T06:24:18.257Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "DrawableList.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\DrawableList.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\DrawableList.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\DrawableList.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\DrawableList.cpp", "ViewState": "AgIAAPYAAAAAAAAAAAAawAYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T07:16:14.809Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "images.h", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Image\\images.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Image\\images.h", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Image\\images.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Image\\images.h", "ViewState": "AgIAAAAAAAAAAAAAAAAQwBAAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-05T08:11:19.286Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "FlashLightView.h", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Tools\\FlashLight\\FlashLightView.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\FlashLight\\FlashLightView.h", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Tools\\FlashLight\\FlashLightView.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\FlashLight\\FlashLightView.h", "ViewState": "AgIAAAYAAAAAAAAAAAAQwAkAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-07T10:23:36.763Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "TestScrollListContainer.h", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\TestScrollListContainer.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\TestScrollListContainer.h", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\TestScrollListContainer.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\TestScrollListContainer.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-08T08:29:38.804Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "FactoryGpsStartTestModel.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\FactoryPage\\FactoryGpsStartTest\\FactoryGpsStartTestModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\FactoryPage\\FactoryGpsStartTest\\FactoryGpsStartTestModel.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\FactoryPage\\FactoryGpsStartTest\\FactoryGpsStartTestModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\FactoryPage\\FactoryGpsStartTest\\FactoryGpsStartTestModel.cpp", "ViewState": "AgIAAAkAAAAAAAAAAAAQwBsAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T03:25:09.02Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "SleepDailyPageView.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepDailyPageView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepDailyPageView.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepDailyPageView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepDailyPageView.cpp", "ViewState": "AgIAACoAAAAAAAAAAADwvzYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T12:50:14.698Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "ScrollBase.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollBase.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollBase.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollBase.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollBase.cpp", "ViewState": "AgIAADkBAAAAAAAAAAAawFIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T05:58:18.851Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "DialDataSelectView.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "ToolTip": "E:\\greeit_Qwos\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "ViewState": "AgIAAJAAAAAAAAAAAAAawKAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-05T02:14:06.032Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ScrollList.cpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollList.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollList.cpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollList.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\scrollers\\ScrollList.cpp", "ViewState": "AgIAACACAAAAAAAAAAAawDACAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-04T12:23:18.794Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "ScrollList.hpp", "DocumentMoniker": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\containers\\scrollers\\ScrollList.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\containers\\scrollers\\ScrollList.hpp", "ToolTip": "E:\\greeit_Qwos\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\containers\\scrollers\\ScrollList.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\containers\\scrollers\\ScrollList.hpp", "ViewState": "AgIAAPwAAAAAAAAAAAAowAgBAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-04T08:37:24.741Z"}]}]}]}