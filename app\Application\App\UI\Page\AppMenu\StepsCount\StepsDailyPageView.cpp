/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   StepsDailyPageView.cpp
@Time    :   2024/12/10 19:09:36
* 
**************************************************************************/

#include "stepsDailyPageView.h"
#include "Image/images.h"
#include "qw_os_gui.h"
#include "../../qwos_app/GUI/QwGUITheme.h"

static const char g_step_count[] = "_step_count";
TM_KEY(g_step_count)

static const char g_step[] = "_step";
TM_KEY(g_step)



void StepsDailyPageView::setup(int today_data, int *hours_data, int today_goal)
{
    setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);

    add(excel_daily_view_);
    excel_daily_view_.setExcelShowData(hours_data, 24);
    excel_daily_view_.setDefaultValue(200);//设置默认值
    excel_daily_view_.setExcelColor(get_menu_card_color(enum_menucard_step));
    excel_daily_view_.setIntegerMultiples(100);//设置整数倍 
    excel_daily_view_.setTitleAndUnit(g_step_count, g_step);
    excel_daily_view_.setTodayDataAndGoal(today_data, today_goal);
    excel_daily_view_.setup(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);

}

// 先setUp再刷新页面ui
void StepsDailyPageView::updateDailyPageData(int today_data, int *hours_data, int today_goal)
{
    excel_daily_view_.setExcelShowData(hours_data, 24);
    excel_daily_view_.setTodayDataAndGoal(today_data, today_goal);
    excel_daily_view_.updateDailyView();
   
}