/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   cfg_sportspage.h
@Time    :   2025/04/16 14:37:13
<AUTHOR>   lxin
*
**************************************************************************/
#ifndef __CFG_SPORTSPAGE_H__
#define __CFG_SPORTSPAGE_H__

#include "stdint.h"
#include "stdbool.h"
#include "qw_dev_cfg.h"

#if defined(__cplusplus)
extern "C"
{
#endif

    typedef enum {
        TOTAL_SPORTS_TYPE_RUNNING,             //全部的跑步运动
        TOTAL_SPORTS_TYPE_TREADMILL,           //全部的骑行运动
        TOTAL_SPORTS_TYPE_ALL,                 //全部的运动
        TOTAL_SPORTS_TYPE_NAVI,                 //全部的导航运动
    } TOTAL_SPORTS_TYPE;

    /**
     * @brief 网格布局样式
     */
    typedef enum {
        LAYOUT_1,
        LAYOUT_2,
        LAYOUT_3A,
        LAYOUT_3B,
        LAYOUT_4A,
        LAYOUT_4B,
        LAYOUT_4C,
        LAYOUT_5,
        LAYOUT_6,

        LAYOUT_GRID_1,      // 图形化样式1 3行 整行 * 2 + 整行 * 1
        LAYOUT_GRID_2,      // 图形化样式2 4行 整行 * 3 + 整行 * 1

        LAYOUT_TOTAL,
    } WEBGRID_LAYOUT;

    /**
     * @brief 页面状态(禁用/关闭/开启)
     */
    typedef enum
    {
        PAGE_STUTAS_HIDE,
        PAGE_STUTAS_SHOW,
        PAGE_STUTAS_DISABLE,
    } WEBGRID_PAGE_STUTAS;

    /**
     * @brief 页面状态(禁用/关闭/开启)
     */
    typedef enum
    {
        AUTO_PAGE_CLOSE,            //关闭
        AUTO_PAGE_FAST,             //快 2s
        AUTO_PAGE_MEDIUM,           //中 4s
        AUTO_PAGE_SLOW,             //慢 6s
        AUTO_PAGE_MAIN_PAGE,        //回到主页
    } WEBGRID_AUTO_PAGE_TYPE;

    /**
     * @brief 布局子项样式
     */
    typedef enum
    {
        LAYOUT_INVALID,         // 无效值
        LAYOUT_SINGLE,          // 整行
        LAYOUT_MUTI,            // 半行

        LAYOUT_SINGLE_2,        // 整行, 占两行
        LAYOUT_SINGLE_3,        // 整行, 占三行
    } WEBGRID_LAYOUT_LINE;

    /**
     * @brief 初始化
     * @param cfg 参数指针
     */
    void cfg_sport_webgrid_default(cfg_sports_webgrid_t* cfg);

	/**
     * @brief 根据运动类型获取页面个数
     * @param sport_type 运动类型
     * @return 个数 0 = 入参无效
     */
    uint8_t get_sport_webgrid_page_num(SPORTTYPE sport_type);

    /**
     * @brief 根据运动类型获取已开启的页面个数
     * @param sport_type 运动类型
     * @return 个数 0 = 入参无效
     */
    uint8_t get_sport_webgrid_page_show_num(SPORTTYPE sport_type);

    /**
     * @brief 根据 get_sport_webgrid_page_show_num 得到的个数序号获取 page_type
     * @param sport_type 运动类型
     * @param count 序号
     * @return WEBGRID_GRAPH_PAGE_TYPE UINT8_MAX = 入参无效
     */
    uint8_t get_sport_webgrid_page_type_show_by_count(SPORTTYPE sport_type, int count);

    /**
     * @brief 开启页面
     * @param sport_type 运动类型
     * @param page_type 页面类型
     */
    void open_sport_webgrid_page(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 关闭页面
     * @param sport_type 运动类型
     * @param page_type 页面类型
     */
    void close_sport_webgrid_page(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 检查页面是否显示
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return true
     */
    uint8_t is_sport_webgrid_page_open(SPORTTYPE sport_type, uint8_t page_type);

	/**
     * @brief 根据页面排序index(菜单index)得到页面类型
     * @param sport_type 运动类型
     * @param index 菜单index
     * @return WEBGRID_GRAPH_PAGE_TYPE UINT8_MAX = 入参无效
     */
    uint8_t get_sport_webgrid_page_type_by_order(SPORTTYPE sport_type, uint8_t index);

    /**
     * @brief 根据页面类型得到页面排序index(菜单index)
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return 页面排序index(菜单index) UINT8_MAX = 入参无效
     */
    uint8_t get_sport_webgrid_page_index_by_order(SPORTTYPE sport_type, uint8_t page_type);

	/**
     * @brief 根据页面类型获取布局样式
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return 布局样式
     */
    WEBGRID_LAYOUT get_sport_webgrid_page_layout(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 获取布局行数
     * @param layout_type 布局样式
     * @return 行数 0 = 无效值, 入参错误
     */
    uint8_t get_sport_webgrid_layout_line_count(WEBGRID_LAYOUT layout_type);

    /**
     * @brief 获取布局行样式
     * @param layout_type 布局样式
     * @param index 行号
     * @return WEBGRID_LAYOUT_LINE 0 = 无效值, 入参错误
     */
    uint8_t get_sport_webgrid_layout_line_style(WEBGRID_LAYOUT layout_type, uint8_t line);

    /**
     * @brief 获取页面数据个数
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return 个数
     */
    uint8_t get_sport_webgrid_page_item_num(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 获取数据项枚举
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @param index 序号
     * @return 枚举值
     */
    uint16_t get_sport_webgrid_page_item_data(SPORTTYPE sport_type, uint8_t page_type, uint8_t index);

    /**
     * @brief 获取自动翻页时间
     * @param sport_type 运动类型
     * @return cfg_sports_webgrid_t->auto_page_time 定义
     */
    uint8_t get_sport_auto_page_time(SPORTTYPE sport_type);

    /**
     * @brief 获取自动翻页时间
     * @param sport_type 运动类型
     * @param type WEBGRID_AUTO_PAGE_TYPE 定义
     */
    void set_sport_auto_page_time(SPORTTYPE sport_type, WEBGRID_AUTO_PAGE_TYPE type);

    /**
     * @brief 获取自动翻页时间
     * @param sport_type 运动类型
     * @return bool
     */
    uint8_t get_sport_auto_page_enable(SPORTTYPE sport_type);

    /**
     * @brief 获取自动翻页时间
     * @param sport_type 运动类型
     * @param enable bool
     */
    void set_sport_auto_page_enable(SPORTTYPE sport_type, bool enable);

    /**
     * @brief 获取主页page_type
     * @param sport_type 运动类型
     * @return 主页page_type
     */
    uint8_t get_sport_home_page(SPORTTYPE sport_type);

    /**
     * @brief 获取主页page_type
     * @param sport_type 运动类型
     * @param page_type 主页page_type
     */
    void set_sport_home_page(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 获取当前运动类型
     * @return 运动类型
     */
    SPORTTYPE get_current_sport_mode();

    /**
     * @brief 设置当前运动类型
     * @param sport_type 运动类型
     */
    void set_current_sport_mode(SPORTTYPE sport_type);

    /**
     * @brief 重置当前页面到第一个有效页面, 进入运动时调用
     */
    void reset_current_webgrid_page();

    /**
     * @brief 获取当前页面类型
     * @return 页面类型page_type
     */
    uint8_t get_current_webgrid_page_type();

    /**
     * @brief 根据页面类型设置当前页面, 设置之后需要刷新前端才能显示
     * @param page_type 页面类型page_type
     */
    void set_current_webgrid_page_type(uint8_t page_type);

    /**
     * @brief 获取当前页面参数数组序号
     * @return 页面参数数组序号
     */
    uint8_t get_current_webgrid_page_index();

    /**
     * @brief 根据页面参数数组序号设置当前页面, 设置之后需要刷新前端才能显示
     * @param index 页面参数数组序号
     */
    void set_current_webgrid_page_index(uint8_t index);

    /**
     * @brief 获取当前页面菜单序号
     * @return 页面菜单序号
     */
    uint8_t get_current_webgrid_page_by_count();

    /**
     * @brief 根据页面菜单序号设置当前页面, 设置之后需要刷新前端才能显示
     * @param index 页面菜单序号
     */
    void set_current_webgrid_page_by_count(uint8_t count);

    /**
     * @brief 根据当前设置的运动类型切下一个可显示页面(循环), 切换之后需要刷新前端才能显示
     * @return true = 页面需要立即刷新   false = 页面不需要刷新
     */
    bool switch_to_next_page();

    /**
     * @brief 根据当前设置的运动类型切上一个可显示页面(循环), 切换之后需要刷新前端才能显示
     * @return true = 页面需要立即刷新   false = 页面不需要刷新
     */
    bool switch_to_previous_page();

    /**
     * @brief 自动翻页和自动返回主页的更新, 在循环中调用
     * @param tick_ms 经过时间ms
     * @return true = 页面需要立即刷新   false = 页面不需要刷新
     */
    bool time_tick_to_switch_page(uint32_t tick_ms);

    /**
     * @brief 自动翻页计时重置
     */
    void reset_time_tick_to_switch_page();

    /**
     * @brief 获取数据项枚举
     * @param sport_type 运动类型
     * @param wkt_type 训练页面类型 DATATYPE_GRAPH_WK_BASE ~ DATATYPE_GRAPH_WK_END
     * @return 枚举值
     */
    void set_sport_webgrid_style_wkt_type(SPORTTYPE sport_type, uint8_t wkt_data_type);

    /**
     * @brief 判断当前运动类型是否为跑步
     * @return 是否为跑步
     */
    bool is_current_sport_running(void);

    /**
     * @brief 判断当前运动类型是否为骑行
     * @return 是否为骑行
     */
    bool is_current_sport_cycling(void);

    /**
     * @brief 设置某个运动类型下特定页面的显示状态
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @param enable 是否启用 true:显示 false:隐藏
     */
    void set_sport_page_status(SPORTTYPE sport_type, uint8_t page_type, bool enable);

    /**
     * @brief 获取某个运动类型下特定页面的显示状态
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return 页面显示状态 true:显示 false:隐藏
     */
    bool get_sport_page_status(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 设置某个运动类型下特定页面的布局
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @param layout 布局类型 WEBGRID_LAYOUT
     * @return 是否设置成功
     */
    bool set_sport_page_layout(SPORTTYPE sport_type, uint8_t page_type, uint8_t layout);

    /**
     * @brief 获取某个运动类型下特定页面的布局
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @return 布局类型 WEBGRID_LAYOUT，无效时返回LAYOUT_1
     */
    uint8_t get_sport_page_layout(SPORTTYPE sport_type, uint8_t page_type);

    /**
     * @brief 设置某个运动类型下特定页面的数据类型
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @param index 数据项索引
     * @param data_type 数据类型
     */
    bool set_sport_page_data_type(SPORTTYPE sport_type, uint8_t page_type, uint8_t index, uint16_t data_type);

    /**
     * @brief 获取某个运动类型下特定页面的数据类型
     * @param sport_type 运动类型
     * @param page_type 页面类型
     * @param index 数据项索引
     * @return 数据类型
     */
    uint16_t get_sport_page_data_type(SPORTTYPE sport_type, uint8_t page_type, uint8_t index);

    /**
     * @brief 设置页面顺序
     * @param sport_type 运动类型
     * @param page_order 页面顺序数组，数组内容为page_type
     * @param count 数组长度
     * @return 是否设置成功
     */
    bool set_sport_page_order(SPORTTYPE sport_type, const uint8_t* page_order, uint8_t count);

#ifdef __cplusplus
}
#endif

#endif /* __CFG_SPORTSPAGE_H__ */
