/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   IntelligentNotificationView.cpp
@Time    :   2025-04-07 15:04:34
* 
**************************************************************************/

#include "IntelligentNotificationView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"

static const char g_intelligent_notification_title[] = "_smart_notifications";
TM_KEY(g_intelligent_notification_title)

static const char g_intelligent_notification_clear_all[] = "_clear_all";
TM_KEY(g_intelligent_notification_clear_all)

static const char g_no_intelligent_notification[] = "_no_notifications";
TM_KEY(g_no_intelligent_notification)

static const char g_yesterday[] = "_yesterday";
TM_KEY(g_yesterday)

IntelligentNotificationView::IntelligentNotificationView(PageManager* manager) :
    QwMenuView(manager),
    p_notification_num_(nullptr),p_item_select_(nullptr),p_set_item_select_(nullptr),
    p_notification_brief_info_(nullptr),
    update_item_select_(this, &IntelligentNotificationView::update_item_select),
    update_notification_num_(this, &IntelligentNotificationView::update_notification_num),
    on_select_item_(this, &IntelligentNotificationView::on_select_item)
{

}

IntelligentNotificationView::~IntelligentNotificationView()
{

}

void IntelligentNotificationView::setup()
{
	if (*p_notification_num_->get_val(0) == 0) 
    {
		clear_all();
        show_no_notification();
    }
    else
    {
        QwMenuView::show_menu(*p_notification_num_->get_val(0) + 1, 1, _TM(g_intelligent_notification_title));
    }
}

void IntelligentNotificationView::quit()
{

}

void IntelligentNotificationView::handleTickEvent()
{
    QwMenuView::list_.handleTickEvent();
}

void IntelligentNotificationView::handleKeyEvent(uint8_t c)
{
    QwMenuView::list_.handleKeyEvent(c);
    if (c == KEY_CLK_BACK)
    {
        manager_->push("MenuCard");
    }
    else if (c == KEY_CLK_START)
    {
        if (*p_notification_num_->get_val(0) > 0)
        {
            int select_index = get_select_app();
            switch_to_app(select_index);
        }
    }
}

void IntelligentNotificationView::handleClickEvent(const ClickEvent& evt)
{
    QwMenuView::list_.handleClickEvent(evt);
}

void IntelligentNotificationView::handleDragEvent(const DragEvent& evt)
{
    QwMenuView::list_.handleDragEvent(evt);
}

void IntelligentNotificationView::handleGestureEvent(const GestureEvent& evt)
{
    QwMenuView::list_.handleGestureEvent(evt);
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL
        && evt.getVelocity() > 10)
    {
        manager_->push("MenuCard");
    }
}

// Notification Callback function
void IntelligentNotificationView::set_on_set_item_select(Notification<uint8_t>* command)
{
    p_set_item_select_ = command;
}

void IntelligentNotificationView::set_on_notification_brief_info(Notification<ins_data_t*, uint8_t>* command)
{
    p_notification_brief_info_ = command;
}

// ObserverDrawable Callback function
void IntelligentNotificationView::set_update_notification_num(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
    if (observer != nullptr)
    {
        p_notification_num_ = observer;
        observer->bind_ctrl(0, QwMenuView::list_);
        observer->bind_notify(update_notification_num_);
    }
}

void IntelligentNotificationView::update_notification_num(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        uint8_t count = 0;
        uint8_t number = 0;
        uint8_t i = 0;

        // 保存当前选中的位置
        number = data->get_val();
        count = list_.get_items_count();
        int current_focus = list_.get_focus_item_index();
        // 清除所有列表项
        for (i = 0; i < count; i++)
        {
            if (list_.get_list_item(i) != nullptr)
            {
                list_.get_list_item(i)->quit();
            }
            if (list_.get_list_item_from_focus(i) != nullptr)
            {
                list_.get_list_item_from_focus(i)->quit();
            }
        }
        list_.restore_initial_state();

        bg_.invalidate();
        clear_all();
        
        if (number > 0)//有通知
        {
            if (current_focus >= 0 && current_focus < number + 1)
            {
                QwMenuView::show_menu(number + 1, current_focus, _TM(g_intelligent_notification_title));
            }
            else
            {
                QwMenuView::show_menu(number + 1, 1, _TM(g_intelligent_notification_title));
            }
            
        }
        else//无通知
        {
            show_no_notification();
        }
    }
}

void IntelligentNotificationView::set_update_item_select(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
    if (observer != nullptr)
    {
        p_item_select_ = observer;
        observer->bind_ctrl(0, QwMenuView::list_);
        observer->bind_notify(update_item_select_);
    }
}

void IntelligentNotificationView::update_item_select(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {

    }
}

// custom function

void IntelligentNotificationView::set_item_info(item_info_t* info, int index)
{
    if (info == nullptr)
    {
        GUI_VIEW_LOG_E("[view] @%s@ set_item_info index is out, info = 0x%08x index = %d", __FUNCTION__, info, index);
            return;
    }

    if (index == 0)
    {
        info->item_index = -1;
        info->type = ITEM_TYPES::ITEM_TEXT;
        memset(info->title, 0, sizeof(info->title));
        memcpy(info->title, _TM(g_intelligent_notification_clear_all), strlen(_TM(g_intelligent_notification_clear_all)));
        memset(info->subtitle, 0, sizeof(info->subtitle));

        info->item_text_info.text_align = 2;
    }
#ifndef SIMULATOR
    else
    {
        ins_data_t ins_data = {0};
        p_notification_brief_info_->notify(&ins_data, index - 1);
        info->item_index = index - 1;
        memset(info->title, 0, sizeof(info->title));
        memset(info->subtitle, 0, sizeof(info->subtitle));
        if (ins_data.msg_type == enumMSG_INCOMING_TYPE)//来电
        {
            info->type = ITEM_TYPES::ITEM_NOTIFICATION_CALL;
            info->img_path = &notification_call_40;
            memset(time_str, 0, sizeof(time_str));
            snprintf(info->title, sizeof(info->title), "%s", ins_data.title);
            snprintf(time_str, sizeof(time_str), "%s", format_time_str((const char*)ins_data.date));
            info->long_subtitle = time_str;
        }
        else
        {
            if (ins_data.msg_type == enumMSG_NOTE_TYPE)//短信
            {
                info->img_path = &notification_message_40;//短信
            }
            else
            {
                info->img_path = &notification_app_40;//APP
            }
            info->title_font = &TEXT_NO38_M_ALL_FONT;
            info->type = ITEM_TYPES::ITEM_NOTIFICATION_NOTE;
            snprintf(info->title, sizeof(info->title), "%s", ins_data.title);
            snprintf(time_str, sizeof(time_str), "%s", format_time_str((const char*)ins_data.date));
            snprintf(info->subtitle, sizeof(info->subtitle), "%s", ins_data.message);
            info->long_subtitle = time_str;
        }
    }
#else
    else
    {
        info->item_index = index;
        memset(info->title, 0, sizeof(info->title));
        memset(info->subtitle, 0, sizeof(info->subtitle));
        memset(time_str, 0, sizeof(time_str));

        if(index % 3 == 1) { // Incoming call
            info->type = ITEM_TYPES::ITEM_NOTIFICATION_CALL;
            info->img_path = &notification_call_40;
            snprintf(info->title, sizeof(info->title), "Caller %d", index);
            snprintf(time_str, sizeof(time_str), "2024-01-01 12:00");
            info->long_subtitle = time_str;
        }
        else if(index % 3 == 2) { // SMS
            info->type = ITEM_TYPES::ITEM_NOTIFICATION_NOTE;
            info->img_path = &notification_message_40;
            info->title_font = &TEXT_NO38_M_ALL_FONT;
            snprintf(info->title, sizeof(info->title), "SMS Contact %d", index);
            snprintf(time_str, sizeof(time_str), "2024-01-01 12:00");
            snprintf(info->subtitle, sizeof(info->subtitle), "This is a test SMS message");
            info->long_subtitle = time_str;
        }
        else { // APP message
            info->type = ITEM_TYPES::ITEM_NOTIFICATION_NOTE;
            info->img_path = &notification_app_40;
            info->title_font = &TEXT_NO38_M_ALL_FONT;
            snprintf(info->title, sizeof(info->title), "APP %d", index);
            snprintf(time_str, sizeof(time_str), "2024-01-01 12:00");
            snprintf(info->subtitle, sizeof(info->subtitle), "This is a test APP notification content");
            info->long_subtitle = time_str;
        }
    }
#endif
}

void IntelligentNotificationView::fill_item_info(item_info_t* info, int index)
{

}

void IntelligentNotificationView::set_item_notify(QwMenuItem* item, int index)
{
    if (item == nullptr)
    {
        GUI_VIEW_LOG_E("[view] @%s@ item is nullptr", __FUNCTION__);
        return;
    }

    item->set_select_handle(on_select_item_);
}

void IntelligentNotificationView::on_select_item(void* item)
{
	if (item == nullptr)
	{
        GUI_VIEW_LOG_E("[view] @%s@ item is nullptr", __FUNCTION__);
		return;
	}

	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	if (item_ == nullptr)
	{
        GUI_VIEW_LOG_E("[view] @%s@ item_ not is nullptr", __FUNCTION__);
		return;
	}

	int select_index = (int)item_->get_user_data();
    switch_to_app(select_index);
}

void IntelligentNotificationView::switch_to_app(int index)
{
#ifndef SIMULATOR
    if (index != -1)
    {
        manager_->page_command("NotificationDetails", 0, &index);
        manager_->push("NotificationDetails");
    }
    else
    {
        ins_clean_all();
        manager_->push("IntelligentNotification");
    }
#endif
}

/**
 * @brief 根据时间字符串格式化显示
 * @param time_str 输入的时间字符串，格式为"YYYY-MM-DD HH:MM"
 * @return 格式化后的字符串
 *         当天: "HH:MM" (根据12/24小时制)
 *         昨天: "昨天"
 *         其他: "MM/DD" (根据日期格式设置)
 */
char* IntelligentNotificationView::format_time_str(const char* time_str)
{
    static char result[16] = {0};
    qw_tm_t input_time = {0};
    qw_tm_t current_time = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int16_t timezone = service_datetime_get_timezone();
    
    // 解析输入时间
    sscanf(time_str, "%d-%d-%d %d:%d", 
           &input_time.tm_year, &input_time.tm_mon, &input_time.tm_mday,
           &input_time.tm_hour, &input_time.tm_min);
    
    // 获取当前时间
    service_datetime_gmt2datetime(gmt, &current_time, timezone);
    
    // 判断是否是同一天
    if (input_time.tm_year == current_time.tm_year &&
        input_time.tm_mon == current_time.tm_mon &&
        input_time.tm_mday == current_time.tm_mday)
    {
        // 当天，只显示时间
        if(get_time_style() == TIMTSTYLE_TIME12) {
            uint8_t h = input_time.tm_hour;
            const char* suffix = "AM";
            
            if(h >= 12) {
                suffix = "PM";
                if(h > 12) {
                    h -= 12;
                }
            } else if(h == 0) {
                h = 12;
            }
            
            snprintf(result, sizeof(result), "%02d:%02d%s", h, input_time.tm_min, suffix);
        } else {
            snprintf(result, sizeof(result), "%02d:%02d", 
                    input_time.tm_hour, input_time.tm_min);
        }
    }
    // 判断是否是昨天
    else if (input_time.tm_year == current_time.tm_year &&
             input_time.tm_mon == current_time.tm_mon &&
             input_time.tm_mday == current_time.tm_mday - 1)
    {
        const char* yesterday_str = _TM(g_yesterday);
        snprintf(result, sizeof(result), "%s", yesterday_str);
    }
    else
    {
        // 其他日期，显示月日
        switch(get_real_date_style()) {
            case DATESTYLE_YMD:
                snprintf(result, sizeof(result), "%02d/%02d", 
                        input_time.tm_mon, input_time.tm_mday);
                break;
            case DATESTYLE_MDY:
                snprintf(result, sizeof(result), "%02d/%02d", 
                        input_time.tm_mon, input_time.tm_mday);
                break;
            case DATESTYLE_DMY:
                snprintf(result, sizeof(result), "%02d/%02d", 
                        input_time.tm_mday, input_time.tm_mon);
                break;
            default:
                snprintf(result, sizeof(result), "%02d/%02d", 
                        input_time.tm_mon, input_time.tm_mday);
                break;
        }
    }
    
    return result;
}

void IntelligentNotificationView::show_no_notification()
{
    add(bg_);
    add(title_);
    add(data_);
    add(folder_);

    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(lv_color_black());

    title_.setWidthHeight(HAL::DISPLAY_WIDTH, MENU_FOCUS_TITLE_HEIGHT);
    title_.setTextFont(&PUBLIC_NO_38_M_FONT);
    title_.setTextAlignment(CENTER);
    title_.setColor(lv_color_white());
    title_.setTypedDynamicText(_TM(g_intelligent_notification_title));
    title_.setLabelAlpha(LV_OPA_TRANSP);
    title_.resizeHeightToCurrentText();
    title_.setAlign(ALIGN_IN_TM, 0, 42);

    folder_.setBitmap(Bitmap(&notification_no_message_128));
    folder_.setAlign(ALIGN_IN_TM, 0, 135);

    data_.setWidthHeight(HAL::DISPLAY_WIDTH, MENU_FOCUS_TITLE_HEIGHT);
    data_.setTextFont(&PUBLIC_NO_32_M_FONT);
    data_.setTextAlignment(CENTER);
    data_.setColor(lv_color_white());
    data_.setTypedDynamicText(_TM(g_no_intelligent_notification));
    data_.setLabelAlpha(LV_OPA_TRANSP);
    data_.resizeHeightToCurrentText();
    data_.setAlignTo(folder_, ALIGN_OUT_BM, 0, 24);
}

