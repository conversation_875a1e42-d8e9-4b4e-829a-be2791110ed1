/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   NavigationRoute.cpp
@Time    :   2025-04-10 14:58:22
*
**************************************************************************/

#include <new>
#include "NavigationRoute.h"

/**
 * @brief 生成页面对象, 申请内存, 初始化绘制页面, 数据绑定
 */
void NavigationRoute::setup(void* view_buffer)
{
    p_view_model_ = &view_model_;

    view_model_.setup();

    p_view_ = new (view_buffer) NavigationRouteView(PageBase::manager_);

    assert(p_view_ != nullptr && "[NavigationRoute::create_view:8001] malloc p_view_ == nullptr");

    auto p_context = static_cast<NavigationRouteView*>(p_view_);

    // bind data
    p_context->set_update_m_select_index(view_model_.get_m_select_index_observer());
    p_context->set_update_m_file_num(view_model_.get_m_file_num_observer());
    p_context->set_update_m_file_index(view_model_.get_m_file_index_observer());
    p_context->set_update_m_file_info(view_model_.get_m_file_info_observer());
    p_context->set_update_m_page_mode(view_model_.get_m_page_mode_observer());

    // bind ctrl
    p_context->set_on_v_file_index(view_model_.get_on_v_file_index_command());
    p_context->set_on_v_file_cmd(view_model_.get_on_v_file_cmd_command());
    p_context->set_fit_decode_cmd(view_model_.get_fit_decode_command());

    p_context->setup();
}

/**
 * @brief Notify 所有数据项
 */
void NavigationRoute::notify()
{
    view_model_.notify();
}

/**
 * @brief 响应事件
 * @param cmd 事件类型, 根据派生页面自己定义
 */
void NavigationRoute::handle_command(int cmd, void* user_data)
{
    switch(cmd)
    {
        case static_cast<int> (NavigationRouteCmd::SetPageMode):
        {
            view_model_.get_on_v_file_cmd_command()->notify(*static_cast<int*>(user_data));
            break;
        }
        case static_cast<int> (NavigationRouteCmd::SetRouteFitDecode):
        {
            view_model_.get_fit_decode_command()->notify(*static_cast<int*>(user_data));
            break;
        }
        case static_cast<int> (NavigationRouteCmd::setUpdateRoute):
        {
            const char* cur_page = manager_->get_cur_page();
            if(strcmp(cur_page, "NavigationRoute") == 0)
            {
                if(p_view_!=nullptr)
                {
                    view_model_.get_update_route_command()->notify();
                    static_cast<NavigationRouteView*>(p_view_)->update_route_num();
                }
            }
            break;
        }
		default:
		{
			break;
		}
	}
}

