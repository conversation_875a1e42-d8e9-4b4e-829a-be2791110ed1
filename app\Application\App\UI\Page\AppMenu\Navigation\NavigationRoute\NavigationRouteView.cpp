/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   NavigationRouteView.cpp
@Time    :   2025-04-10 14:58:22
<AUTHOR>   nullptyr
*
**************************************************************************/

#include "NavigationRouteView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "GUI/QwGUIKey.h"
#include "Image/images.h"
#include "gui_event_service.h"

const char text_navi_route_title[] = "_route";
TM_KEY(text_navi_route_title)

const char text_navi_route_del[] = "_delete_route";
TM_KEY(text_navi_route_del)

const char text_navi_route_no[] = "_download_route_from_app";
TM_KEY(text_navi_route_no)

static int del_inx = 0;   //需要删除的文件的 index

NavigationRouteView::NavigationRouteView(PageManager *manager)
    : QwMenuView(manager)
    , p_m_file_num_(nullptr)
    , p_m_file_index_(nullptr)
    , p_m_file_info_(nullptr)
    , p_m_page_mode_(nullptr)
    , p_v_file_index_(nullptr)
    , update_m_file_num_(this, &NavigationRouteView::update_m_file_num)
    , update_m_file_index_(this, &NavigationRouteView::update_m_file_index)
    , update_m_file_info_(this, &NavigationRouteView::update_m_file_info)
    , update_m_page_mode_(this, &NavigationRouteView::update_m_page_mode)
    , custom_user_select_(this, &NavigationRouteView::custom_user_select)
{}

NavigationRouteView::~NavigationRouteView()
{}

void NavigationRouteView::setup()
{
    //获取导航路线文件数量
    file_num = *p_m_file_num_->get_val(0);

    if (file_num == 0)
    {
        add(bg_);
        bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
        bg_.setColor(lv_color_hex(0x000000));

        add(topStatus_);
        topStatus_.set_show_type(TOP_STATUS_TYPE_DEFAULT);
        topStatus_.setup();

        add(title_);
        title_.setTextFont(&PUBLIC_NO_38_M_FONT);
        title_.setTextAlignment(CENTER);
        title_.setColor(lv_color_white());
        title_.setLabelAlpha(LV_OPA_TRANSP);
        title_.setTypedDynamicText(_TM(text_navi_route_title));
        title_.setAlign(ALIGN_IN_TM, 0, 79);

        add(text_);
        text_.setTextFont(&PUBLIC_NO_32_M_FONT);
        text_.setColor(lv_color_white());
        text_.setWidthHeight(338, 338);
        text_.setTextAlignment(CENTER);
		text_.setTextVerticalAlignment(ALIGN_Y_MID);
        text_.setLabelAlpha(LV_OPA_TRANSP);
        text_.setTypedDynamicText(_TM(text_navi_route_no));
        text_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		text_.resizeHeightToCurrentText();
        text_.setAlign(ALIGN_IN_TM, 0, 189);

    }
    else
    {
        if (*p_m_page_mode_->get_val(0) == 0)
        {   //正常页面
            QwMenuView::show_menu(file_num + 1, (*p_m_select_index_->get_val(0)), _TM(text_navi_route_title));
        }
        else
        {   //编辑页面（删除页面）
            QwMenuView::show_menu(file_num, 0, _TM(text_navi_route_del));
        }
    }
}

void NavigationRouteView::quit()
{}

void NavigationRouteView::handleTickEvent()
{
    if (file_num != 0)
    {
        QwMenuView::list_.handleTickEvent();
    }
    else
    {
        topStatus_.handleTickEvent();
    }
}

void NavigationRouteView::handleKeyEvent(uint8_t c)
{
    if (file_num != 0)
    {
        QwMenuView::list_.handleKeyEvent(c);
        if (c == KEY_CLK_START)
        {
            switch_to_app(get_select_app());
        }
        else if (c == KEY_CLK_BACK)
        {
            p_fit_decode_->notify(0);   // 清除焦点
            if (*p_m_page_mode_->get_val(0) == 0)
            {
                manager_->push("NavigationMenus");
            }
            else
            {
                //切换为正常页面
                uint16_t page_mode = 0;
                manager_->page_command("NavigationRoute", static_cast<int>(NavigationRouteCmd::SetPageMode), &page_mode);
                manager_->push("NavigationRoute");
            }
        }
    }
    else
    {
        if (c == KEY_CLK_BACK)
        {
            p_fit_decode_->notify(0);   // 清除焦点
            manager_->push("NavigationMenus");
        }
    }
}

void NavigationRouteView::handleClickEvent(const ClickEvent &evt)
{
    if (file_num != 0)
    {
        QwMenuView::list_.handleClickEvent(evt);
    }
}

void NavigationRouteView::handleDragEvent(const DragEvent &evt)
{
    if (file_num != 0)
    {
        QwMenuView::list_.handleDragEvent(evt);
    }
}

void NavigationRouteView::handleGestureEvent(const GestureEvent &evt)
{
    if (file_num != 0)
    {
        QwMenuView::list_.handleGestureEvent(evt);
        if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL && evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
            p_fit_decode_->notify(0);   // 清除焦点
            if (*p_m_page_mode_->get_val(0) == 0)
            {
                manager_->push("NavigationMenus");
            }
            else
            {
                //切换为正常页面
                uint16_t page_mode = 0;
                manager_->page_command("NavigationRoute", static_cast<int>(NavigationRouteCmd::SetPageMode), &page_mode);
                manager_->push("NavigationRoute");
            }
        }
    }
    else
    {
        p_fit_decode_->notify(0);   // 清除焦点
        manager_->push("NavigationMenus");
    }
}

// Notification Callback function
void NavigationRouteView::set_on_v_file_index(Notification<uint16_t> *command)
{
    p_v_file_index_ = command;
}

void NavigationRouteView::set_on_v_file_cmd(Notification<uint16_t> *command)
{
    //p_v_file_cmd_ = command;
}

void NavigationRouteView::set_fit_decode_cmd(Notification<int> *command)
{
    p_fit_decode_ = command;
}

// ObserverDrawable Callback function
void NavigationRouteView::set_update_m_select_index(ObserverDrawable<Drawable, int, 1> *observer)
{
    if (observer != nullptr)
    {
        p_m_select_index_ = observer;
    }
}

void NavigationRouteView::update_m_select_index(Drawable *ctrl, Parameters<int> *data, int idx)
{

}

void NavigationRouteView::set_update_m_file_num(ObserverDrawable<Drawable, uint16_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_m_file_num_ = observer;
        // observer->bind_ctrl(0, QwMenuView::list_);
        // observer->bind_notify(update_m_file_num_);
    }
}

void NavigationRouteView::update_m_file_num(Drawable *ctrl, Parameters<uint16_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void NavigationRouteView::set_update_m_file_index(ObserverDrawable<Drawable, uint16_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_m_file_index_ = observer;
        // observer->bind_ctrl(0, QwMenuView::list_);
        // observer->bind_notify(update_m_file_index_);
    }
}

void NavigationRouteView::update_m_file_index(Drawable *ctrl, Parameters<uint16_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void NavigationRouteView::set_update_m_page_mode(ObserverDrawable<Drawable, uint16_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_m_page_mode_ = observer;
        // observer->bind_ctrl(0, QwMenuView::list_);
        // observer->bind_notify(update_m_page_mode_);
    }
}

void NavigationRouteView::update_m_page_mode(Drawable *ctrl, Parameters<uint16_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void NavigationRouteView::set_update_m_file_info(ObserverDrawable<Drawable, NavigationRouteModel::ui_navi_brief_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_m_file_info_ = observer;
        // observer->bind_ctrl(0, QwMenuView::list_);
        // observer->bind_notify(update_m_file_info_);
    }
}

void NavigationRouteView::update_m_file_info(Drawable *ctrl, Parameters<NavigationRouteModel::ui_navi_brief_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
    }
}

void NavigationRouteView::process_filename(char name[])
{
    char *last_dot = strchr(name, '.');   // 找到第一个点号的位置

    if (last_dot == NULL)
    {
        // 没有找到点号，直接返回
        return;
    }

    // 检查点号是否在文件名末尾（如“file.”）
    if (last_dot[1] == '\0')
    {
        *last_dot = '\0';
        return;
    }

    // 去掉最后一个点号及其后面的部分
    *last_dot = '\0';
}

// custom function
void NavigationRouteView::set_item_info(item_info_t *info, int index)
{
    if (info == nullptr || index >= (*p_m_file_num_->get_val(0) + 1))
    {
        assert(false && "[NavigationRouteView:8001]set_item_info index is out");
        return;
    }

    info->item_index = index;
    info->type = ITEM_TYPES::ITEM_TEXT;
    memset(info->title, 0, sizeof(info->title));
    memset(info->subtitle, 0, sizeof(info->subtitle));
    if (*p_m_page_mode_->get_val(0) == 0)   //正常页面
    {
        if (index == 0)                     //第一项是删除项
        {
            memcpy(info->title, _TM(text_navi_route_del), strlen(_TM(text_navi_route_del)));
        }
        else                                      //剩下的项是正常文件信息项
        {
            p_v_file_index_->notify(index - 1);   //文件 index 从 0 开始

            auto file_info = static_cast<NavigationRouteModel::ui_navi_brief_t>(*p_m_file_info_->get_val(0));
            process_filename(file_info.name);
            memcpy(str_file_name, file_info.name, sizeof(file_info.name));
            info->title_font = &TEXT_NO44_M_ALL_FONT;
            info->long_title = str_file_name;

            char sub_str[50];
            sprintf(sub_str, "%s%s %s%s", file_info.info1, file_info.unit1, file_info.info2, file_info.unit2);
            memcpy(str_file_info, sub_str, sizeof(sub_str));
            QW_LOG_D("NAVI:","FILE_NAME:%s",str_file_info);
            info->long_subtitle = str_file_info;
        }
    }
    else                                  //编辑页面（删除页面）
    {
        p_v_file_index_->notify(index);   //文件 index 从 0 开始

        auto file_info = static_cast<NavigationRouteModel::ui_navi_brief_t>(*p_m_file_info_->get_val(0));
        process_filename(file_info.name);
        memcpy(str_file_name, file_info.name, sizeof(file_info.name));
        info->title_font = &TEXT_NO44_M_ALL_FONT;
        info->long_title = str_file_name;

        char sub_str[50];
        sprintf(sub_str, "%s%s %s%s", file_info.info1, file_info.unit1, file_info.info2, file_info.unit2);
        memcpy(str_file_info, sub_str, sizeof(sub_str));
        info->long_subtitle = str_file_info;
    }
    info->item_text_info.text_align = 1;
}

void NavigationRouteView::set_item_notify(QwMenuItem *item, int index)
{
    item->set_select_pos_handle(custom_user_select_);
}

void NavigationRouteView::custom_user_select(void *item, int x, int y)
{
    if (item == nullptr)
    {
        assert("[NavigationRouteView:8001]custom_dnd_on_select item is "
               "nullptr");
        return;
    }

    QwMenuItem *item_ = dynamic_cast<QwMenuItem *>((ItemBaseCtrl *) item);
    if (item_ == nullptr)
    {
        assert("[NavigationRouteView:8001]custom_dnd_on_select item_ not is "
               "QwMenuItem");
        return;
    }

    int select_index = (int) item_->get_user_data();
    switch_to_app(select_index);
}

void NavigationRouteView::switch_to_app(int index)
{
    if (*p_m_page_mode_->get_val(0) == 0)   //正常页面
    {
        if (index == 0)                     //第一项是删除项
        {
            //点击后切换为编辑页面
            uint16_t page_mode = 1;
            manager_->page_command("NavigationRoute", static_cast<int>(NavigationRouteCmd::SetPageMode), &page_mode);
            manager_->push("NavigationRoute");
            return;
        }
        else
        {
            //剩下的项点击后进入预览页面
        }
        p_fit_decode_->notify(index);
    }
    else   //编辑页面（删除页面）
    {
        //弹窗 传入要删除的文件索引
        del_inx = index;
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_DELETE_NAVIGATION, &del_inx);
    }
}

void NavigationRouteView::update_route_num(void)
{
    if(strcmp( manager_->get_cur_page(),"NavigationRoute") == 0)
    {
        list_.restore_initial_state();
        setup();
    }
}
