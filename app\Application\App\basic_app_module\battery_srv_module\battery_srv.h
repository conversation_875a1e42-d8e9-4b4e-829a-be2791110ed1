/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   battery_srv.h
@Time    :   2024/12/23 15:21:27
*
**************************************************************************/

#ifndef __BATTERY_SRV_H
#define __BATTERY_SRV_H

#include <rtthread.h>
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

uint8_t battery_srv_init_start(void);

uint8_t battery_srv_uninit_stop(void);

int battery_get_level(void);

bool battery_get_status(void);

//gui准备好以后，发通知给battery_srv,开始更新。
void gui_notify_battery_run(void);

//亮屏后，发通知给battery_srv,弹窗显示电量
void battery_show_backlight_on(void);

//模拟测试电量：手动测试
void test_simulate_battery_level(uint8_t level);
//模拟测试电量：定时器10s减少1
void test_simulate_battery_start(bool s);

#ifdef __cplusplus
}
#endif
#endif
