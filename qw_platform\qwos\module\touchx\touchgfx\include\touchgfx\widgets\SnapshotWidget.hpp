/**
  ******************************************************************************
  * This file is part of the TouchGFX 4.13.0 distribution.
  *
  * <h2><center>&copy; Copyright (c) 2019 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */

#ifndef SNAPSHOTWIDGET_HPP
#define SNAPSHOTWIDGET_HPP

#include <touchgfx/widgets/Widget.hpp>
#include <touchgfx/widgets/Image.hpp>
#include <touchgfx/containers/Container.hpp>

namespace touchgfx
{
    
constexpr auto MAX_SNAPSHOT_DIRTY_AREA_SIZE = 20;

/**
 * @class SnapshotWidget SnapshotWidget.hpp touchgfx/widgets/SnapshotWidget.hpp
 *
 * @brief A widget that is able to make a snapshot of the area the SnapshotWidget covers.
 *
 *        A widget that is able to make a snapshot of the area the SnapshotWidget covers. The
 *        SnapshotWidget will show the snapshot captured when it is drawn.
 *        Note: The snapshot must be taken from a byte aligned position.
 *        On BPP=4, this means on even positions, x=0, 2, 4, 8,...
 *        On BPP=2, this means on positions, x= 0, 4, 8, 12,...
 *        On BPP=1, this means on positions, x= 0, 8, 16,...
 *
 * @see Widget
 */
class SnapshotWidget : public Image
{
public:

    /**
     * @fn SnapshotWidget::SnapshotWidget();
     *
     * @brief Default constructor.
     *
     *        Default constructor.
     */
    SnapshotWidget();

    /**
     * @fn virtual SnapshotWidget::~SnapshotWidget();
     *
     * @brief Destructor.
     *
     *        Destructor.
     */
    virtual ~SnapshotWidget();

    /**
     * @fn virtual void SnapshotWidget::draw(const Rect& invalidatedArea) const;
     *
     * @brief Draws the SnapshotWidget.
     *
     *        Draws the SnapshotWidget. It supports partial drawing, so it only redraws the
     *        area described by invalidatedArea.
     *
     * @param invalidatedArea The rectangle to draw, with coordinates relative to this drawable.
     */
    virtual void draw(const Rect& invalidatedArea) const;

    /**
     * @fn virtual Rect SnapshotWidget::getSolidRect() const;
     *
     * @brief Gets solid rectangle.
     *
     *        Gets solid rectangle.
     *
     * @return The solid rectangle.
     */
    virtual Rect getSolidRect() const;

    /**
     * @fn virtual void SnapshotWidget::makeSnapshot();
     *
     * @brief Makes a snapshot of the area the SnapshotWidget currently covers.
     *
     *        Makes a snapshot of the area the SnapshotWidget currently covers. This area is
     *        defined by setting the dimensions and the position of the SnapshotWidget.
     *        The snapshot is stored in Animation Storage.
     */
    virtual void makeSnapshot();

    /**
     * @fn virtual void SnapshotWidget::makeSnapshot(const BitmapId bmp);
     *
     * @brief Makes a snapshot of the area the SnapshotWidget currently to a bitmap.
     *
     *        Makes a snapshot of the area the SnapshotWidget
     *        currently covers. This area is defined by setting the
     *        dimensions and the position of the SnapshotWidget. The
     *        snapshot is stored in the provided dynamic bitmap.
     *
     * @param bmp The target dynamic bitmap.
     */
    virtual void makeSnapshot(void *buf);

    virtual void makeSnapshot(Container& container, void* buf, bool with_alpha = false);

    virtual void makeSnapshot(Container& container, void* buf, Rect rc, bool with_alpha = false);
    
    virtual void updateSnapshot(Container& container, Rect& dirty_rc);
    
    virtual void updateSnapshot(Container& container, Vector<Rect, MAX_SNAPSHOT_DIRTY_AREA_SIZE>& dirty_list);

    lv_img_dsc_t* getCachedDsc()
    {
        return &canvas.dsc;
    }

protected:
    lv_canvas_t canvas;
    bool static_buffer;
};
} // namespace touchgfx

#endif // SNAPSHOTWIDGET_HPP
