/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-12-06 10:55:26: Lxin 原始版本
*
*********************************************************************************************/
#ifndef LV_FONT_FREETYPE_TOOLS_H
#define LV_FONT_FREETYPE_TOOLS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include <lvgl.h>

/**********************
 * GLOBAL PROTOTYPES
 **********************/

typedef struct {
    uint32_t buf_size;
    lv_font_glyph_dsc_t dsc;
} lv_ft_tools_info_t;

typedef struct {
    // lv_font_t里的内容
    lv_coord_t line_height;         /**< The real line height where any text fits*/
    lv_coord_t base_line;           /**< Base line measured from the top of the line_height*/
    uint8_t subpx;             /**< An element of `lv_font_subpx_t`*/

    int8_t underline_position;      /**< Distance between the top of the underline and base line (< 0 means below the base line)*/
    int8_t underline_thickness;     /**< Thickness of the underline*/
} lv_ft_tools_head_t;

/********************************************************************************************
* Function/Macro Name : lv_ft_font_tool_init
* Purpose :文字缓存初始化
* Param[in] :
* param ---
* Param[out] :
* param ---
* Return type :
* Comment : 2023-12-06 19:27:02
********************************************************************************************/
bool lv_ft_font_tool_init(const char* path, uint16_t size);

bool lv_ft_font_tool_head_get(lv_ft_tools_head_t* p_info);

bool lv_ft_font_tool_ch_get(uint32_t unicode_letter, lv_ft_tools_info_t* p_info, unsigned char** buf);

void lv_ft_font_tool_destroy();

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* LV_FONT_FREETYPE_TOOLS_H */
