/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   task_thread_helper.cpp
@Time    :   2024/12/10 11:07:26
*
**************************************************************************/

#include "gui_event_service.h"
#include "../basic_app_module/backlight_module/backlight_module.h"   // 背光控制
#include "../qwos_app/GUI/PageManager/PageCommand.h"
#include "GUIMsg/MsgBoxService.h"                                    // 弹框
#include "MvcApp.h"                                                  // 页面管理器
#include "activity_record/activity_fit_app.h"
#include "algo_service_sport_status.h"
#include "fit_workout.h"
#include "focus_mode_srv/focus_mode_srv.h"   // 专注模式
#include "gui_thread.h"                      // GUI线程
#include "navi_file_manager.h"
#include "navi_port.h"
#include "navi_srv_module.h"
#include "qw_log.h"
#include "qw_time_service.h"
#include "touchgfx_js/touchgfx_js_api.h"
#define GUI_MSG_QUEUE_SIZE 24
#define GUI_MSG_EVENT      0x1000

static os_QueueHandle g_gui_msg_queue = NULL;
static osThreadId_t g_gui_thread = NULL;

static bool msg_gui_event_handle(EventData *eventData, void *data)
{
    uint32_t evt = eventData->arg1;
    uint32_t arg = eventData->arg2;
    void *p_context = eventData->data;

    switch (evt)
    {
    case GUI_EVT_SERVICE_LCD_SET:   // 背光应用调整背光亮度
        if (arg <= 255)
        {
            backlight_set_lcd_pct((uint8_t) arg, false);
        }
        break;
    case GUI_EVT_SERVICE_LCD_SETTING:   // 页面背光设置时调整背光亮度
        if (arg <= 255)
        {
            backlight_percent_set_app((uint8_t) arg);
        }
        break;
    case GUI_EVT_SERVICE_LCD_HBM_SETTING:   // 工模页面背光设置时调整背光亮度
        if (arg <= 255)
        {
            backlight_hbm_percent_set_app((uint8_t) arg);
        }
        break;
    case GUI_EVT_SERVICE_GUI_SLEEP:
#ifndef SIMULATOR
        GUI_VIEW_LOG_D("GUI_EVT_SERVICE_GUI_SLEEP");
        gui_sleep_prepare();
#endif
        break;
    case GUI_EVT_SERVICE_GUI_AOD:
        GUI_VIEW_LOG_D("GUI_EVT_SERVICE_GUI_AOD");
#ifndef SIMULATOR
        touch_device_control(false);
        knob_device_control(false);
        gui_aod_prepare();
#endif
        break;
    case GUI_EVT_SERVICE_GUI_WEAKUP:
#ifndef SIMULATOR
        GUI_VIEW_LOG_D("GUI_EVT_SERVICE_GUI_WEAKUP");
        touch_device_control(true);
        knob_device_control(true);
        gui_wakeup_prepare();
#endif
        break;
    case GUI_EVT_SERVICE_DEVICE_CONTROL:
    {
#ifndef SIMULATOR
        int device_evt = (int) (p_context);
        if (device_evt & DEVICE_CONTROL_TOUCH)
        {
            touch_device_control((bool) arg);
        }
        if (device_evt & DEVICE_CONTROL_KNOB)
        {
            knob_device_control((bool) arg);
        }
#endif
        break;
    }
    case GUI_EVT_SERVICE_FOCUS_ON:
    {
        founc_evt_service_cmd_t *info = static_cast<founc_evt_service_cmd_t *>(p_context);
        if (info->evt == FOCUS_ON_SLEEP)
        {
            sleep_sumit_work(SLEEP_OPS_UPDATE, &info->state);
        }
        else if (info->evt == FOUCS_ON_DND)
        {
            dnd_sumit_work(DND_OPS_UPDATE, &info->state);
        }
        break;
    }
    case GUI_EVT_SERVICE_POPOUT:
        msg_gui_popup_msgbox((uint16_t) (arg & 0xFFFF), p_context);
        break;
    case GUI_EVT_SERVICE_PAGE_COMMAND:
    {
        gui_evt_service_page_command_t *info = static_cast<gui_evt_service_page_command_t *>(p_context);
        MvcApp::get_mvc_app()->getPageManager()->page_command(info->page, info->cmd, info->user_data);
        break;
    }
    case GUI_EVT_SERVICE_PAGE_PUSH:
    {
        MvcApp::get_mvc_app()->getPageManager()->push(static_cast<char *>(p_context));
        break;
    }
    case GUI_EVT_SERVICE_GUI_SLEEP_TIMEOUT:
        GUI_VIEW_LOG_D("GUI_EVT_SERVICE_GUI_SLEEP_TIMEOUT");
#ifndef SIMULATOR
        gui_powersave_push_page();
#else
        MvcApp::get_mvc_app()->sleep_push_page();
#endif
        break;
    case GUI_EVT_SERVICE_AOD_TIMEOUT:
        GUI_VIEW_LOG_D("GUI_EVT_SERVICE_AOD_TIMEOUT");
#ifndef SIMULATOR
        aod_timeout();
#endif
        break;
    case GUI_EVT_SERVICE_START_WORKOUT:
    {
#ifndef SIMULATOR
        int8_t sport_status = get_sport_status();
        char *current_using_training_file_name = static_cast<char *>(p_context);
        if (true == get_workout_started())
        {
            workout_reset_param();
        }
        int result = fit_workout_start_by_filename(current_using_training_file_name);
        if (result == 0)
        {
            backlight_open_app();   // 激活背光
            uint8_t sport_type = fit_workout_get_current_course_sport_type();
            //当前没有在运动中
            if (sport_status <= enum_status_ready)
            {
                if (sport_type == SPORTSTYPE_CYCLING)
                {
                    set_current_sport_mode(SPORTSTYPE_CYCLING);
                }
                else
                {
                    set_current_sport_mode(SPORTSTYPE_RUNNING);
                }
                if (sport_status == enum_status_ready)   //当前在运动准备页
                {
                    //Do nothing
                }
                else   //当前不在运动准备页
                {
                    MvcApp::get_mvc_app()->getPageManager()->page_command("SportStart", (int) SportStart_CMD::SET_READY_SPORT, NULL);
                    MvcApp::get_mvc_app()->getPageManager()->push("SportStart");
                }
            }
            else   //当前在运动中
            {
                int current_page = GRAPH_PAGE_WKT;
                open_sport_webgrid_page(get_current_sport_mode(), GRAPH_PAGE_WKT);
                MvcApp::get_mvc_app()->getPageManager()->page_command("WebGrid", static_cast<int>(WebGrid_CMD::SET_CURRENT_PAGE),
                                                                      static_cast<void *>(&current_page));
                if (sport_status == enum_status_pause_manul)   //手动暂停
                {
                    activity_resume();
                    MvcApp::get_mvc_app()->getPageManager()->push("WebGrid");
                }
            }
        }
#endif
        break;
    }
    case GUI_EVT_SERVICE_START_ROUTE:
    {
#ifndef SIMULATOR
        int8_t sport_status = get_sport_status();
        uint32_t file_index = 0;
        char *route_file_name = static_cast<char *>(p_context);
        if (QW_OK == navi_search_file(route_file_name, &file_index))
        {
            GUI_VIEW_LOG_D("GUI_EVT_SERVICE_START_ROUTE: found %d", file_index);

            //路书解码预览
            // uint32_t route_index = file_index + 1;
            // MvcApp::get_mvc_app()->getPageManager()->page_command("NavigationRoute",
            //                      static_cast<int>(NavigationRouteCmd::SetRouteFitDecode),
            //                      &route_index);
            navi_srv_set_preview_file_index(file_index, 0);
            navi_srv_set_prepare_status(NAVI_PREPARE_STATUS_RUNNING);
            //设置选中的方向 0：正向 1：反向
            uint8_t direction = 0;
            MvcApp::get_mvc_app()->getPageManager()->page_command("NavigationDirection", static_cast<int>(NavigationDirectionCmd::SetEnterDirection),
                                                                  &direction);

            //开启背光
            backlight_open_app();

            //运动列表页面
            if (sport_status == enum_status_free)
            {
                static const char *page = "Launcher";
                MvcApp::get_mvc_app()->getPageManager()->page_command("SportsMenu", 1, const_cast<void *>(static_cast<const void *>(page)));
                MvcApp::get_mvc_app()->getPageManager()->push("SportsMenu");
            }
            else
            {
                if (sport_status == enum_status_ready)
                {
                    uint8_t refresh_flag = 1;   //刷新标志
                    MvcApp::get_mvc_app()->getPageManager()->page_command("SportStart", static_cast<int>(SportStart_CMD::SET_REFRESH_FLAG),
                                                                          const_cast<void *>(static_cast<const void *>(&refresh_flag)));
                }
                else
                {
                    reset_current_webgrid_page();
                    if (sport_status == enum_status_pause_manul)
                    {
                        activity_resume();
                    }
                    MvcApp::get_mvc_app()->getPageManager()->push("WebGrid");
                }
            }
        }
        else
        {
            GUI_VIEW_LOG_E("GUI_EVT_SERVICE_START_ROUTE: %s not found", route_file_name);
        }
#endif
    }
    break;
    case GUI_EVT_SYNC_DIAL:
    {
        // 如果在launcher界面进行app下载删除表盘动作，进入DialLoading界面
        if (get_app_operate_type() != APP_DO_NOTHING)
        {
            MvcApp::get_mvc_app()->getPageManager()->push("DialLoading");
        }
        break;
    }
    case GUI_EVT_START_UPGRADE:
    {
        MvcApp::get_mvc_app()->getPageManager()->push(OTA_PAGE);        
        break;
    }
    default:
        break;
    }

    return true;
}

os_QueueHandle init_gui_event(osThreadId_t gui_thread)
{
    assert(gui_thread != NULL);

    g_gui_thread = gui_thread;
    g_gui_msg_queue = user_msg_queue_create(GUI_MSG_QUEUE_SIZE);
    message_registerTask(g_gui_thread, g_gui_msg_queue);
    system_bindlistener(g_gui_thread, GUI_MSG_EVENT, msg_gui_event_handle, NULL);
    return g_gui_msg_queue;
}

void submit_gui_event(GUI_EVT_SERVICE evt, int32_t arg, void *context)
{
    if (g_gui_thread != NULL)
    {
        EventData *pEvent = message_allocEventData();
        message_eventDataInit(pEvent, evt, arg, context);
        system_send_event(g_gui_thread, GUI_MSG_EVENT, pEvent);
    }
}

void submit_gui_ipc_tick(void)
{
    qw_tm_t _time;
    service_get_rtctime(&_time);
    time_t now = service_rtctime_2_fittime(&_time);

    // todo: submit ipc tick
}
