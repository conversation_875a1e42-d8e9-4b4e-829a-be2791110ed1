/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   LanguageSelectionMenuModel.h
@Time    :   2025-05-15 15:51:30
*
**************************************************************************/

#pragma once
#include "qw_os_gui.h"
#include "../../qwos_app/GUI/PageBase.h"
#include "view_page_model.h"


class LanguageSelectionMenuModel : public PageModel
{
private:

    // Parameters variables
    Parameters<uint8_t> m_language_type_;

    // Notification Callback variables
    Callback<LanguageSelectionMenuModel, uint8_t> on_v_language_type_;

public:
    LanguageSelectionMenuModel();
    virtual ~LanguageSelectionMenuModel();

    /**
     * @brief PageModel::setup() 进入页面通知
     */
    void setup() override;

    /**
     * @brief PageModel::quit() 退出页面通知
     */
    void quit() override;

    /**
     * @brief PageModel::notify() 更新检查, 在 PageBase::notify() 中调用
     */
    void notify() override;


    // Notification

    /**
     * @brief
     * @param
     */
    void on_v_language_type(uint8_t t1);

    // Parameters function
    Parameters<uint8_t>& get_m_language_type_data() { return m_language_type_; }

    // Notification Callback function
    Callback<LanguageSelectionMenuModel, uint8_t>* get_on_v_language_type_cmd() { return &on_v_language_type_; }


};

