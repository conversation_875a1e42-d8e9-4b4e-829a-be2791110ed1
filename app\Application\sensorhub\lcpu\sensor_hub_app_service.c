/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   sensor_hub_app_service.c
@Time    :   2024/12/14 14:13:38
*
************************************************************/
#include "sensor_hub_app_service.h"
#include "sensor_hub.h"
#include <rtthread.h>
#include <string.h>
#include "subscribe_commu.h"
#include "algo_service_component_common.h"
#include "algo_service_raise_wrist.h"
#include "algo_service_task.h"
#include "subscribe_service.h"
#include "algo_service_adapter.h"
#include "bf0_hal.h"
#include "service_daily_activity.h"
#include "ble_lb55x_macros.h"
#include <sys/time.h>
#include "gps_module.h"

struct rt_timer timer_test;
struct rt_timer timer_rec_test;
static sync_app_time_t g_app_time = {0};
static daily_target_t s_sync_target = {0};
static sync_daily_data_t g_daily_data = {0};
static cfg_user_information_t g_user_info = {0};

extern uint32_t HAL_GetTick(void);

uint32_t sensor_core_get_gmt_time(void)
{
     uint32_t secs = 0;

     secs = HAL_GetTick()/RT_TICK_PER_SECOND;
     secs += g_app_time.gmt;

     return secs;
}

int32_t sensor_core_get_time_zone(void)
{
    return g_app_time.timezone;
}

uint32_t sensor_core_get_datetime(void)
{
    uint32_t secs = 0;

    secs = sensor_core_get_gmt_time();
    secs += g_app_time.timezone;
    rt_kprintf("sensor_core datetime is %u\n", secs);

    return secs;
}

cfg_user_information_t* sensor_core_get_user_info(void)
{
    return &g_user_info;
}

sync_daily_data_t *sensor_core_get_daily_sync_data(void)
{
    return &g_daily_data;
}


int32_t sensor_hub_send_msg_to_app(uint8_t *data, uint16_t len, uint16_t timeout_ms)
{
    int32_t ret;
    sensorhub_msg_head_t msg_header = {0};

    msg_header.data_len = len;
    msg_header.crc = crc_test((uint8_t *)&len, sizeof(uint16_t));
    ret = sensor_core_bridge_send_cmd((uint8_t *)&msg_header, sizeof(sensorhub_msg_head_t), 0);
    if (ret == 0) {
        ret = sensor_core_bridge_send_cmd(data, len, timeout_ms);
    }

    return ret;
}

int32_t sensorhub_event_notify(uint8_t type, uint32_t module_id,
    uint16_t data_len, const uint8_t *pdata, uint16_t timeout_ms)
{
    int32_t ret;
    uint16_t len;
    app_sensorhub_msg_t p_msg;
    memset((void *)&p_msg, 0, sizeof(app_sensorhub_msg_t));

    p_msg.msg_head.msg_type = type;
    p_msg.module_id = module_id;
    p_msg.msg_head.len = data_len;
    memcpy((void *)p_msg.data, pdata, data_len);
    len = data_len + sizeof(p_msg.msg_head) + sizeof(p_msg.module_id);
    //SENSORHUB_D("type: %d module id: %d len:%d", type, module_id, len);
    if (data_len != 0) {
        ret = sensor_hub_send_msg_to_app((uint8_t *)&p_msg, len, timeout_ms);
        if (ret != 0) {
            SENSORHUB_D("app send msg failed %d", ret);
        }
    } else {
        ret = -1;
        SENSORHUB_D("data_len is 0 !! type: %d module id: %d len:%d", type, module_id, len);
    }

    return ret;
}

static bool app_test_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    //SENSORHUB_D("LCPU received module id: %d data: %d data_len:%d", module_id, data[0], len);

    return true;
}

static bool app_rsp_test_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }
    //uint8_t rsp = 0xAA;

    //SENSORHUB_D("LCPU received module id: %d data: %d data_len:%d", module_id, data[0], len);
    //sensorhub_event_notify(EVT_SENSORHUB_SEND_RSP_TEST, 0, sizeof(rsp), &rsp, 0);

    return true;
}

static bool app_rsp_recv_test_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    //SENSORHUB_D("LCPU received module id: %d rsp: %d data_len:%d", module_id, data[0], len);
    //sensor_core_bridge_cmd_rsp_handler();

    return true;
}

/**
 * @brief 异核订阅事件处理
 *
 * @param module_id 模块id
 * @param data 数据
 * @param len 数据长度
 * @return true 处理完成
 * @return false 处理中有异常
 */
static bool app_sub_event_recv_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data && len == 0) {
        return false;
    }
    (void)sub_commu_rcv_data_from_src_core(module_id, data, len);
    return true;
}

/**
 * @brief rtc时间变化通知定时器进行校准
 *
 */
static void rtc_sync_notify_algo_timer(void)
{
    // 输入数据发布后通知算法线程
    //SENSORHUB_D("rtc_sync_notify_algo_timer");
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_EVENT_TIMER;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_CAILBRA;
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("rtc_sync_notify_algo_timer erro");
    }
}

/**
 * @brief 重启恢复通知日常统计算法进行校准
 *
 * @param data 通知数据
 * @param len 数据长度
 */
static void reboot_sync_notify_algo_accum(uint8_t *data, uint32_t len)
{
    if (len > ALGO_CONFIG_DATA_LEN) {
        SENSORHUB_E("reboot_sync_notify_algo_accum erro len:%d", len);
        return;
    }

    // 配置通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_DAILY_ACCUM;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_REBOOT_SET;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("reboot_sync_notify_algo_accum erro");
    }
}

/**
 * @brief 睡眠主动退出
 *
 * @param data 通知数据
 * @param len 数据长度
 */
static void sync_notify_algo_sleep_manual_exit(uint8_t *data, uint32_t len)
{
    if (len > ALGO_CONFIG_DATA_LEN) {
        SENSORHUB_E("sync_notify_algo_sleep_manual_exit erro len:%d", len);
        return;
    }

    // 配置通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_SLEEP_STAGE;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_SLEEP_MANUAL;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("sync_notify_algo_sleep_manual_exit erro");
    }
}

/**
 * @brief 用户信息改变通知算法进行重置
 *
 */
static void reset_user_info_sync_notify_algo(void)
{
    // 第一次不通知，由算法初始化直接处理
    static bool is_first = true;
    if (is_first) {
        is_first = false;
        return;
    }

    // 配置通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_STEP_COUNT;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_RESET_USER_INFO;
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("reset_user_info_sync_notify_algo erro");
    }
}

static bool app_sync_time_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    uint32_t start_time = HAL_GetTick() / RT_TICK_PER_SECOND;
    memcpy(&g_app_time, data, sizeof(sync_app_time_t));
    SENSORHUB_D("app_sync gmt is %u", g_app_time.gmt);

    g_app_time.gmt -= start_time;

    struct timezone tmp_tz = {0};
    tmp_tz.tz_minuteswest = g_app_time.timezone / 60;
    tz_set(&tmp_tz);

    // 定时器校准
    rtc_sync_notify_algo_timer();

    // 通知算法线程
    algo_service_raise_wrist_time_sync();
    return true;
}

static bool app_sync_daily_target_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    memcpy(&s_sync_target, data, sizeof(daily_target_t));
    SENSORHUB_D("app_sync target is %d-%d-%d-%d", s_sync_target.step_goal, s_sync_target.cal_goal,
                s_sync_target.intense_time_goal,s_sync_target.active_hour_goal);

    return true;
}

/**
 * @brief 获取当前日常目标
 *
 * @return daily_target_t* 日常目标
 */
daily_target_t *app_get_daily_target(void)
{
    return &s_sync_target;
}

/**
 * @brief 设置目标值，用于测试
 *
 * @param target 目标值
 */
void app_daily_target_sim_set(daily_target_t *target)
{
    s_sync_target = *target;
    SENSORHUB_D("app_daily_target_sim_set target is %d-%d-%d-%d", s_sync_target.step_goal, s_sync_target.cal_goal,
                s_sync_target.intense_time_goal,s_sync_target.active_hour_goal);
}

static bool app_sync_daily_report_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    memcpy(&g_daily_data, data, sizeof(sync_daily_data_t));
    SENSORHUB_D("app sync daily data is %d-%.2f-%d", g_daily_data.step_count, g_daily_data.calories,
                g_daily_data.intense_time);
    reboot_sync_notify_algo_accum(data, len);
    return true;
}

static bool app_sync_ble_handle(uint32_t module_id, uint8_t *data, uint16_t len)
{
    // if (NULL == data) {
    //     return false;
    // }
    extern void ble_app_lcpu_rsp_exec(uint32_t func_type, uint8_t *data, uint16_t len);
    ble_app_lcpu_rsp_exec(module_id, data, len);

    // sensor_core_bridge_cmd_rsp_handler();

    return true;
}

static void hr_mode_sync_algo_hr(uint8_t *data, uint16_t len)
{
    if(ALGO_CONFIG_DATA_LEN < len)
    {
        SENSORHUB_E("hr_mode_sync_algo_hr len erro");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_HRM;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_HR_MODE;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("hr_mode_sync_algo_hr erro");
    }
    SENSORHUB_D("app set hr mode %d", *data);
}

static bool app_hr_mode_msg_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    hr_mode_sync_algo_hr(data,len);

    return true;
}

static void hr_remind_sync_algo_hr(uint8_t *data, uint16_t len)
{
    if(ALGO_CONFIG_DATA_LEN < len)
    {
        SENSORHUB_E("hr_remind_sync_algo_hr len erro");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_HRM;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_HR_REMIND;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("hr_remind_sync_algo_hr erro");
    }
}

static bool app_hr_remind_config_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }
    hr_remind_sync_algo_hr(data, len);

    return true;
}

static bool app_user_info_msg_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    memcpy(&g_user_info, data, sizeof(cfg_user_information_t));
    SENSORHUB_D("app user bitrh %d-%d-%d", g_user_info.birth.year,
                                           g_user_info.birth.mon,
                                           g_user_info.birth.day);
    // 通知算法重置用户信息
    reset_user_info_sync_notify_algo();

    return true;
}

static void spo2_mode_sync_algo_spo2(uint8_t *data, uint16_t len)
{
    if(ALGO_CONFIG_DATA_LEN < len)
    {
        SENSORHUB_E("spo2_mode_sync_algo_spo2 len erro");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_SPO2;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_SPO2_MODE;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("spo2_mode_sync_algo_spo2 erro");
    }
}

static bool app_spo2_mode_msg_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    SENSORHUB_D("app set spo2 mode %d", *data);
    spo2_mode_sync_algo_spo2(data,len);
    return true;
}

static void stress_mode_sync_algo_stress(uint8_t *data, uint16_t len)
{
    if(ALGO_CONFIG_DATA_LEN < len)
    {
        SENSORHUB_E("stress_mode_sync_algo_stress len erro");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_STRESS;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_STRESS_MODE;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("stress_mode_sync_algo_stress erro");
    }
}

static bool app_stress_mode_msg_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    SENSORHUB_D("app set stress mode %d", *data);
    stress_mode_sync_algo_stress(data,len);
    return true;
}

static void stress_remind_sync_algo_stress(uint8_t *data, uint16_t len)
{
    if(ALGO_CONFIG_DATA_LEN < len)
    {
        SENSORHUB_E("stress_remind_sync_algo_stress len erro");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_CONFIG;
    head.algo_type = ALGO_TYPE_STRESS;
    head.input_type = 0;
    algo_config_t config = {0};
    config.type = ALGO_CONFIG_STRESS_REMIND;
    memcpy(config.args, data, len);
    if (send_msg_to_algo_fwk(head, &config, sizeof(algo_config_t)) != 0)
    {
        SENSORHUB_E("stress_remind_sync_algo_stress erro");
    }
}

static bool app_stress_remind_config_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }
    // SENSORHUB_D("app set stress remind %d", data[0]);
    stress_remind_sync_algo_stress(data, len);

    return true;
}

static bool app_sleep_force_update_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data) {
        return false;
    }

    SENSORHUB_D("app sleep force update flag %d", *data);
    sync_notify_algo_sleep_manual_exit(data, len);

    return true;
}

static bool app_gps_start_msg_handler(uint32_t module_id, uint8_t *data, uint16_t len)
{
    if (NULL == data || len < 1) {
        return false;
    }
    // rt_kprintf("%s data:%x\n", __func__, *data);
    if (*data == REMOTE_GPS_CMD_START) {
        SENSORHUB_D("GPS Start Command Received");
        gps_irq_init();
    } else if (*data == REMOTE_GPS_CMD_STOP) {
        SENSORHUB_D("GPS Stop Command Received");
        gps_irq_uninit();
    } else {
        SENSORHUB_E("Unknown GPS Command Received: %x", *data);
        return false;
    }
    return true;
}

const static sensorhub_msg_func_t sensorhub_msg_cb_array[] =
{
    [EVT_APP_TEST]                          = {app_test_handle},
    [EVT_APP_RSP_TEST]                      = {app_rsp_test_handle},
    [EVT_APP_SEND_RSP_TEST]                 = {app_rsp_recv_test_handle},
    [EVT_APP_SUBSCRIBE_DATA]                = {app_sub_event_recv_handle},
    [EVT_APP_SYNC_TIME]                     = {app_sync_time_handle},
    [EVT_APP_DAILY_TARGET_MSG]              = {app_sync_daily_target_handle},
    [EVT_APP_DAILY_SYNC_MSG]                = {app_sync_daily_report_handle},
    [EVT_APP_BLE]                           = {app_sync_ble_handle},
    [EVT_APP_HR_MODE_MSG]                   = {app_hr_mode_msg_handler},
    [EVT_APP_HR_REMIND_CONFIG_MSG]          = {app_hr_remind_config_handler},
    [EVT_APP_USER_INFO_MSG]                 = {app_user_info_msg_handler},
    [EVT_APP_SPO2_MODE_MSG]                 = {app_spo2_mode_msg_handler},
    [EVT_APP_STRESS_MODE_MSG]               = {app_stress_mode_msg_handler},
    [EVT_APP_STRESS_REMIND_CONFIG_MSG]      = {app_stress_remind_config_handler},
    [EVT_APP_SLEEP_FORCE_UPDATE]            = {app_sleep_force_update_handler},
    [EVT_APP_GPS_START_MSG]                 = {app_gps_start_msg_handler},

    [EVT_APP_END]                           = {NULL},
};

void sensor_hub_app_msg_received(uint8_t *ptr, uint16_t len)
{
    uint16_t index;
    app_sensorhub_msg_t *pMsg = (app_sensorhub_msg_t *)ptr;
    if (len == 0) {
        SENSORHUB_E("%s len is 0!! msg type: %d-%d", __FUNCTION__, pMsg->msg_head.msg_type, pMsg->module_id);
        return;
    }
    if(pMsg->msg_head.msg_type >= EVT_APP_END) {
        SENSORHUB_E("msg type error %d", pMsg->msg_head.msg_type);
        return;
    }
    index = pMsg->msg_head.msg_type;
    if (NULL != sensorhub_msg_cb_array[index].msg_handle) {
        sensorhub_msg_cb_array[index].msg_handle(pMsg->module_id, pMsg->data, pMsg->msg_head.len);
    }
}

uint16_t crc_test(uint8_t const *p_data, uint32_t size)
{
    uint16_t crc = 0;
    while (size--) {
        crc = (uint8_t)(crc >> 8) | (crc << 8);
        crc ^= (*p_data++);
        crc ^= (uint8_t)(crc & 0xFF) >> 4;
        crc ^= (crc << 8) << 4;
        crc ^= ((crc & 0xFF) << 4) << 1;
    }
    return crc;
}


#if defined(RT_USING_FINSH)

#include <finsh.h>

void msh_sensorhub_send_msg_to_app(int32_t argc, char **argv)
{
    SENSORHUB_D("CMD: send_msg_from_sensorhub_to_app!");
    uint16_t i = 0;

    if (argc < 3)
    {
        SENSORHUB_D("Wrong argument");
    }

    if (0 == strcmp(argv[1], "test")) {
        i = (uint8_t)atoi(argv[2]);
        sensorhub_event_notify(EVT_SENSORHUB_TEST, 0, sizeof(i), (uint8_t *)&i, 0);
    } else if (0 == strcmp(argv[1], "test_rsp")) {
        i = (uint8_t)atoi(argv[2]);
        sensorhub_event_notify(EVT_SENSORHUB_RSP_TEST, 0, sizeof(i), (uint8_t *)&i, 500);
    } else if (0 == strcmp(argv[1], "read_uuid")) {
        i = (uint8_t)atoi(argv[2]);
        sensorhub_event_notify(EVT_SENSORHUB_BLE, 0, sizeof(i), (uint8_t *)&i, 500);
    }
}

MSH_CMD_EXPORT_ALIAS(msh_sensorhub_send_msg_to_app, l2h, l2h: lcpu message to hcpu);
#endif


