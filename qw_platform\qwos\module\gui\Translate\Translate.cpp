/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   Translate.cpp
@Time    :   2024/12/11 13:57:16
*
**************************************************************************/

#include "Translate.h"
#include "qw_os_gui.h"
#include "../../qwos/module/gui/GUICtrl/QwUtility/PM_List.hpp"

#ifdef EXPORT_TRANSLATE_KEY
#include "qw_fs_api.h"
#include "igs_dev_config.h"

static bool list_cmp(const char** t1, const char** t2)
{
	return (strcmp((*t1), (*t2)) == 0);
}

static bool list_sort(const char** t1, const char** t2)
{
	return (strcmp((*t1), (*t2)) < 0);
}

static void list_swap(const char** t1, const char** t2)
{
	const char* tmp = (*t1);
	*t1 = *t2;
	*t2 = tmp;
}
#else
// translate list
#if ONLY_ENABLE_CN_ENG
static const char* g_tran_text_cn[] = {
	#include "TEXT_CN.inc"
};
static const char* g_tran_text_en[] = {
#include "TEXT_US.inc"
};
#else
static const char* g_tran_text_cn[] = {
	#include "TEXT_CN.inc"
};

static const char* g_tran_text_en[] = {
#include "TEXT_US.inc"
};

static const char* g_tran_text_sp[] = {
#include "TEXT_ES.inc"
};

static const char* g_tran_text_jp[] = {
#include "TEXT_JP.inc"
};

static const char* g_tran_text_kr[] = {
#include "TEXT_KR.inc"
};

static const char* g_tran_text_fr[] = {
#include "TEXT_FR.inc"
};

static const char* g_tran_text_pt[] = {
#include "TEXT_PT.inc"
};

static const char* g_tran_text_pl[] = {
#include "TEXT_PL.inc"
};

// static const char* g_tran_text_th[] = {
// #include "TEXT_TH.inc"
// };

static const char* g_tran_text_ru[] = {
#include "TEXT_RU.inc"
};

static const char* g_tran_text_ge[] = {
#include "TEXT_GE.inc"
};

static const char* g_tran_text_it[] = {
#include "TEXT_IT.inc"
};
#endif

static const char* g_tran_text_key[] = {
#include "KEY_ALL.inc"
};

// code
static uint16_t g_ap_begin[26] = { 0 };
static int KEY_INC_NUM = - 1;
static language_type g_translate_type = enum_qw_language_en;
static int find_translate(const char* key)
{
	if (KEY_INC_NUM <= 0)
	{
		KEY_INC_NUM = sizeof(g_tran_text_key) / sizeof(const char*);
	}

	if (key != nullptr && strlen(key) > 0)
	{
		int num = key[0] - 'a';
		int begin = (num >= 0 && num < 26) ? g_ap_begin[num] : 0;
		for (int i = begin; i < KEY_INC_NUM; i++)
		{
			if (strcmp(g_tran_text_key[i], key) == 0)
			{
				return i;
			}
		}

		for (int i = 0; i < KEY_INC_NUM; i++)
		{
			if (strcmp(g_tran_text_key[i], key) == 0)
			{
				return i;
			}
		}
	}
	return -1;
}

#endif // EXPORT_TRANSLATE_KEY

void declare_list(const char** list, int count)
{
#ifdef EXPORT_TRANSLATE_KEY
	static PMList<const char*> g_translate_stash;

	if (count > 0)
	{
		for (int i = 0; i < count; i++)
		{
			if (strlen(list[i]) > 0 && list[i][0] == '_' && g_translate_stash.find(list[i], list_cmp) == nullptr)
			{
				g_translate_stash.push_back(list[i]);
			}
		}
	}
	else
	{
		const char* key = reinterpret_cast<const char *>(list);

		if (strcmp(key, TM_DECLARE_END_STR) == 0)
		{
			if (g_translate_stash.size() > 0)
			{
				char text_path[150] = { 0 };
				sprintf(text_path, "%sKEY.inc", SYSTEM_PATH);
				QW_FIL* fp = nullptr;
				PMNode_t<const char*>* pNode = nullptr;

				g_translate_stash.sort_func_set(list_sort, list_swap);
				g_translate_stash.sort();

				if (QW_OK != qw_f_open(&fp, text_path, QW_FA_CREATE_ALWAYS | QW_FA_WRITE))
				{
					return;
				}

				int list_size = g_translate_stash.size();
				for (int i = 0; i < list_size; i++)
				{
					pNode = g_translate_stash[i];
					if (nullptr != pNode)
					{
						qw_f_puts(pNode->val, fp);
						qw_f_puts("\n", fp);
					}
				}

				qw_f_close(fp);
			}

			return;
		}

		if (strlen(key) > 0 && key[0] == '_' && g_translate_stash.find(key, list_cmp) == nullptr)
		{
			g_translate_stash.push_back(key);
		}
	}
#endif // EXPORT_TRANSLATE_KEY
}

language_type switch_language_value_kv_to_show(LANGUAGE_TYPE type)
{
    //切换语言
    language_type lan = enum_qw_language_cn;
#ifndef FACTORY_MODE_ENABLE
    switch (type)
    {
    case LANGUAGE_ENG:
        lan = enum_qw_language_en;
        break;
    case LANGUAGE_ESP:
        lan = enum_qw_language_sp;
        break;
    case LANGUAGE_CHI:
        lan = enum_qw_language_cn;
        break;
    case LANGUAGE_JAP:
        lan = enum_qw_language_jp;
        break;
    case LANGUAGE_KOR:
        lan = enum_qw_language_kr;
        break;
    case LANGUAGE_FRA:
        lan = enum_qw_language_fr;
        break;
    case LANGUAGE_POR:
        lan = enum_qw_language_pt;
        break;
    case LANGUAGE_POL:
        lan = enum_qw_language_pl;
        break;
    case LANGUAGE_PYC:
        lan = enum_qw_language_ru;
        break;
    case LANGUAGE_DEU:
        lan = enum_qw_language_ge;
        break;
    case LANGUAGE_ITA:
        lan = enum_qw_language_it;
        break;
    default:
        break;
    }
#endif
    return lan;
}


void set_translate_language(language_type lan)
{
#ifndef EXPORT_TRANSLATE_KEY
	g_translate_type = lan;

	if (KEY_INC_NUM <= 0)
	{
		KEY_INC_NUM = sizeof(g_tran_text_key) / sizeof(const char*);

		for (int i = 0; i < 26; i++)
		{
			g_ap_begin[i] = UINT16_MAX;
		}

		int num = 0;
		for (uint16_t i = 0; i < KEY_INC_NUM; i++)
		{
			num = g_tran_text_key[i][0] - 'a';
			if (num >= 0 && num < 26)
			{
				if (g_ap_begin[num] == UINT16_MAX)
				{
					g_ap_begin[num] = i;
				}
			}
		}
	}
#endif // EXPORT_TRANSLATE_KEY
}

const char* translate_text(const char* key)
{
#ifndef EXPORT_TRANSLATE_KEY
	int index = find_translate(key);
	if (index >= 0 && KEY_INC_NUM > index)
	{
        g_translate_type = switch_language_value_kv_to_show(get_language_type());
		switch (g_translate_type)
		{
#if ONLY_ENABLE_CN_ENG
		case enum_qw_language_cn:
			return g_tran_text_cn[index];
		case enum_qw_language_en:
			return g_tran_text_en[index];
#else
		case enum_qw_language_ge:
			return g_tran_text_ge[index];
		case enum_qw_language_en:
			return g_tran_text_en[index];
		case enum_qw_language_sp:
			return g_tran_text_sp[index];
		case enum_qw_language_fr:
			return g_tran_text_fr[index];
		case enum_qw_language_it:
			return g_tran_text_it[index];
		case enum_qw_language_pl:
			return g_tran_text_pl[index];
		case enum_qw_language_pt:
			return g_tran_text_pt[index];
		case enum_qw_language_ru:
			return g_tran_text_ru[index];
		//case enum_qw_language_th:
		//	return g_tran_text_th[index];
		case enum_qw_language_cn:
			return g_tran_text_cn[index];
		case enum_qw_language_jp:
			return g_tran_text_jp[index];
		case enum_qw_language_kr:
			return g_tran_text_kr[index];
#endif
		default:
			break;
		}
	}
#endif
	return key;
}

extern "C" const char* translate_text(const char* key);
