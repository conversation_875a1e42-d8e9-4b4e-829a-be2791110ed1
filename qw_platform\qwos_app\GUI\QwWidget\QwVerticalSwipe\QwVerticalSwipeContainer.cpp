/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwVerticalSwipeContainer.cpp
@Time    :   2025/01/03 15:18:58
*
**************************************************************************/


#include "QwVerticalSwipeContainer.h"
#include "../module/touchx_external/canvas/qw_gui.h"
#include "../module/gui/GUICtrl/touchgfx.h"

namespace touchgfx
{
    // QwSwipePageBase事件处理实现
    void  QwSwipePageBase::handleTickEvent()
    {
        QwSwipePageBase* child = dynamic_cast<QwSwipePageBase*>(getFirstChild());
        if(child == NULL) 
        {
            // printf("child type is not QwSwipePageBase\n");
            return;
        }
        // 传递事件给子控件处理
        child->handleTickEvent();
    }
    void QwSwipePageBase::handleDragEvent(const DragEvent& event)
    {
        // 获取swipe的子控件QwSwipePageBase的子控件，也就是swipe的孙控件，将事件传递给孙控件，让子控件内部处理
        // 处理完成之后置false，让子控件处理
        QwSwipePageBase* child = dynamic_cast<QwSwipePageBase*>(getFirstChild());
        if(child == NULL) 
        {
            // printf("child type is not QwSwipePageBase\n");
            return;
        }

        // 传递事件给子控件处理
        child->handleDragEvent(event);

        // 检查孙控件是否处理了事件
        if (child->get_used_drag())
        {
            child->set_used_drag(false);
            set_used_drag(true);
            // rt_kprintf("QwSwipePageBase::handleDragEvent: child control handled drag event\n");
            return;
        }

        // rt_kprintf("QwSwipePageBase::handleDragEvent: no child handled drag, using default behavior\n");
        // 如果没有子控件处理事件，则使用默认行为
    }

    void QwSwipePageBase::handleKeyEvent(uint8_t key)
    {
        // 将事件传递给直接子控件，让子控件内部处理
        QwSwipePageBase* child = dynamic_cast<QwSwipePageBase*>(getFirstChild());
        if(child == NULL) 
        {
            // printf("child type is not QwSwipePageBase\n");
            return;
        }
        // 传递事件给子控件处理
        child->handleKeyEvent(key);
        // 检查子控件是否处理了事件
        if (child->get_used_key())
        {
            child->set_used_key(false);
            set_used_key(true);
            // rt_kprintf("QwSwipePageBase::handleKeyEvent: child control handled key event: %d\n", key);
            return;
        }

        // rt_kprintf("QwSwipePageBase::handleKeyEvent: no child handled key event: %d\n", key);
    }

    void QwSwipePageBase::handleGestureEvent(const GestureEvent& event)
    {
        // 将事件传递给直接子控件，让子控件内部处理
        
        QwSwipePageBase* child = dynamic_cast<QwSwipePageBase*>(getFirstChild());
        if(child == NULL) 
        {
            // printf("child type is not QwSwipePageBase\n");
            return;
        }
        
        // 传递事件给子控件处理
        child->handleGestureEvent(event);

        // 检查子控件是否处理了事件
        if (child->get_used_gesture())
        {
            child->set_used_gesture(false);
            set_used_gesture(true);
            // rt_kprintf("QwSwipePageBase::handleGestureEvent: child control handled gesture event\n");
            return;
        }
        // rt_kprintf("QwSwipePageBase::handleGestureEvent: no child handled gesture event\n");
    }

    QwVerticalSwipeContainer::QwVerticalSwipeContainer()
        : Container(),
        currentState(NO_ANIMATION),
        animationCounter(0),
        swipeCutoff(80),
        dragX(0),
        animateDistance(0),
        startX(0),
        endElasticWidth(30),
        pages(),//EAST
        pageIndicator(),
        dynamicCachingEnabled(false),
        totalPages(0),
        currentPageIndex_(0),
        pageUpdateCallback(nullptr)
    {
        Application::getInstance()->registerTimerWidget(this, true);

        setTouchable(true);
        pageIndicator.setVisible(false);
        Container::add(pages);
        Container::add(pageIndicator);
    }

    QwVerticalSwipeContainer::~QwVerticalSwipeContainer()
    {
        Application::getInstance()->unregisterTimerWidget(this);
        cleanupDynamicCache();
    }

    void QwVerticalSwipeContainer::add(Drawable& page)
    {
        pages.add(page);

        pageIndicator.setNumberOfPages(getNumberOfPages() + 1);
        setSelectedPage(getSelectedPage());

        //setWidthHeight(page);
    }

    void QwVerticalSwipeContainer::remove(Drawable& page)
    {
        Drawable* tmp = pages.getFirstChild();

        if (pageIndicator.getNumberOfPages() == 0)
        {
            return;
        }

        // looks for the child matching page
        // to ensure that the page indicator only counts down if a page is removed
        while (tmp)
        {
            if (tmp == &page)
            {
                pages.remove(page);
                pageIndicator.setNumberOfPages(getNumberOfPages() - 1);

                const uint8_t numPages = getNumberOfPages();
                if (numPages == 0)
                {
                    invalidate();
                    setWidthHeight(0, 0);
                }
                else
                {
                    const uint8_t curPage = getSelectedPage();
                    setSelectedPage(MIN(curPage, numPages - 1));
                }
                return;
            }
            else
            {
                tmp = tmp->getNextSibling();
            }
        }
    }

    void QwVerticalSwipeContainer::setEndSwipeElasticWidth(uint16_t width)
    {
        endElasticWidth = width;
    }

    void QwVerticalSwipeContainer::setSwipeCutoff(uint16_t cutoff)
    {
        swipeCutoff = cutoff;
    }

    void QwVerticalSwipeContainer::setPageIndicatorBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage)
    {
        pageIndicator.setBitmaps(normalPage, highlightedPage);
    }

    void QwVerticalSwipeContainer::setPageIndicatorXY(int16_t x, int16_t y)
    {
        pageIndicator.setXY(x, y);
    }

    void QwVerticalSwipeContainer::setPageIndicatorXYWithCenteredX(int16_t x, int16_t y)
    {
        setPageIndicatorCenteredX(x);
        pageIndicator.setY(y);
    }

    void QwVerticalSwipeContainer::setPageIndicatorCenteredX()
    {
        setPageIndicatorCenteredX(getWidth() / 2);
    }

    void QwVerticalSwipeContainer::setPageIndicatorCenteredX(int16_t x)
    {
        pageIndicator.setX(x - pageIndicator.getWidth() / 2);
    }

    void QwVerticalSwipeContainer::setSelectedPage(uint8_t pageIndex)
    {
        uint8_t maxPages = dynamicCachingEnabled ? totalPages : getNumberOfPages();
        if (pageIndex < maxPages)
        {
            if (dynamicCachingEnabled)
            {
                currentPageIndex_ = pageIndex;
            }

            pageIndicator.setCurrentPage(pageIndex);
            adjustPages();

            //显示page指示器
            pageIndicator.setVisible(true);
            pageIndicator.invalidate();
            pageIndicator_show = true;
            tick_ = 0;
        }
    }

    void QwVerticalSwipeContainer::setPageIndicatorNumber(uint8_t num)
    {
        pageIndicator.setNumberOfPages(num);
    }

    uint8_t QwVerticalSwipeContainer::getSelectedPage() const
    {
        return pageIndicator.getCurrentPage();
    }

    void QwVerticalSwipeContainer::AutoAnimateUp()
    {
        currentState = ANIMATE_UP;

        pageIndicator.setVisible(true);
        pageIndicator.invalidate();
        pageIndicator_show = true;
        tick_ = 0;
    }

    void QwVerticalSwipeContainer::AutoAnimateDown()
    {
        currentState = ANIMATE_DOWN;

        pageIndicator.setVisible(true);
        pageIndicator.invalidate();
        pageIndicator_show = true;
        tick_ = 0;
    }

    void QwVerticalSwipeContainer::handleTickEvent()
    {
        QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
        if (CurrentSwipePageBase != nullptr)
        {
            CurrentSwipePageBase->handleTickEvent();
        }

        if (currentState == ANIMATE_SWIPE_CANCELLED_UP)
        {
            animateSwipeCancelledUp();
        }
        else if (currentState == ANIMATE_SWIPE_CANCELLED_DOWN)
        {
            animateSwipeCancelledDown();
        }
        else if (currentState == ANIMATE_UP)
        {
            animateUp();
        }
        else if (currentState == ANIMATE_DOWN)
        {
            animateDown();
        }
        else if (currentState == ANIMATE_SWIPE_OUT_UP)
        {
            animateSwipeOutUp();
        }
        else if (currentState == ANIMATE_SWIPE_OUT_DOWN)
        {
            animateSwipeOutDown();
        }
        if (pageIndicator_show && UI_TICK_TO_MS_AUTO(tick_, 2000))
        {
            pageIndicator.setVisible(false);
            pageIndicator.invalidate();
            pageIndicator_show = false;
        }
    }

    void QwVerticalSwipeContainer::handleClickEvent(const ClickEvent& event)
    {
        // If an animation is already in progress do not
        // react to clicks

        if (event.getType() == ClickEvent::PRESSED)
        {
            pressed_page_index_ = getSelectedPage();
            QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
            if (CurrentSwipePageBase)
            {
                CurrentSwipePageBase->handleClickEvent(event);
            }
            // rt_kprintf("@@@@@@PRESSED curPage:%d\n", pressed_page_index_);
        }
        else
        {
            uint8_t curPage = getSelectedPage();
            if (pressed_page_index_ == curPage)
            {
                QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
                if (CurrentSwipePageBase)
                {
                    CurrentSwipePageBase->handleClickEvent(event);
                }
            }
            else
            {
                QwSwipePageBase *last_page = getQwSwipePageBaseFromIndex(pressed_page_index_);
                ClickEvent evt_tmp(ClickEvent::CANCEL, event.getX(), event.getY());
                if (last_page)
                {
                    last_page->handleClickEvent(evt_tmp);
                }
            }
            // rt_kprintf("@@@@@@RELEASED curPage:%d\n", pressed_page_index_);
        }

        if (event.getType() == ClickEvent::RELEASED)
        {
            // Save current position for use during animation
            animateDistance = dragX;
            startX = pages.getY();
            const uint8_t curPage = getSelectedPage();
            if (dragX < 0)
            {
                if (curPage == getNumberOfPages() - 1 || dragX > -swipeCutoff)
                {
                    currentState = ANIMATE_SWIPE_CANCELLED_UP;
                }
                else
                {
                    currentState = ANIMATE_UP;
                }
            }
            else if (dragX > 0)
            {
                if (curPage == 0 || dragX < swipeCutoff)
                {
                    currentState = ANIMATE_SWIPE_CANCELLED_DOWN;
                }
                else
                {
                    currentState = ANIMATE_DOWN;
                }
            }
            dragX = 0;
        }
    }

    void QwVerticalSwipeContainer::handleDragEvent(const DragEvent& event)
    {
        // If an animation is already in progress do not
        // react to drags

        if (currentState != NO_ANIMATION)
        {
            return;
        }

        pageIndicator.setVisible(true);
        pageIndicator.invalidate();
        pageIndicator_show = true;
        tick_ = 0;

        int y_tmp = 0;
        QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
        if(CurrentSwipePageBase != nullptr)
        {
            Rect rc = CurrentSwipePageBase->getAbsoluteRect();
            if(rc.y == 0)
            {
                CurrentSwipePageBase->handleDragEvent(event);
                if (CurrentSwipePageBase->get_used_drag())
                {
                    CurrentSwipePageBase->set_used_drag(false);
                    return;
                }
            }
            else if(rc.y > 0 && rc.y + event.getDeltaY() < 0)
            {
                dragX += -1 * rc.y;
                y_tmp = event.getDeltaY() + rc.y;
            }
            else if(rc.y + rc.height < getHeight() && rc.y + rc.height + event.getDeltaY() > getHeight())
            {
                dragX += getHeight() - rc.y - rc.height;
                y_tmp = event.getDeltaY() + rc.y + rc.height - getHeight();
            }
        }

        if (y_tmp == 0)
        {
            dragX += event.getDeltaY();
        }

        // Do not show too much background next to end pages
        const uint8_t curPage = getSelectedPage();
        if (curPage == 0 && dragX > endElasticWidth)
        {
            dragX = static_cast<int16_t>(endElasticWidth);
        }
        else if (curPage == getNumberOfPages() - 1 && dragX < -endElasticWidth)
        {
            dragX = -static_cast<int16_t>(endElasticWidth);
        }

        adjustPages();

        if(CurrentSwipePageBase != nullptr && y_tmp != 0)
        {
            DragEvent evt(event.getType(), event.getNewX(), event.getNewY(), event.getOldX(), event.getNewY() - y_tmp);
            CurrentSwipePageBase->handleDragEvent(evt);
            CurrentSwipePageBase->set_used_drag(false);
        }
    }

    void QwVerticalSwipeContainer::handleGestureEvent(const GestureEvent& event)
    {
        // Do not accept gestures while animating

        if (currentState != NO_ANIMATION)
        {
            return;
        }

        QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
        if (CurrentSwipePageBase != nullptr && CurrentSwipePageBase->getAbsoluteRect().y == 0)
        {
            CurrentSwipePageBase->handleGestureEvent(event);
            if (CurrentSwipePageBase->get_used_gesture())
            {
                CurrentSwipePageBase->set_used_gesture(false);
                return;
            }
        }

        if (event.getType() == GestureEvent::SWIPE_VERTICAL)
        {
            animateDistance = dragX;
            startX = pages.getY();

            const uint8_t curPage = getSelectedPage();
            if (event.getVelocity() < 0 && curPage < getNumberOfPages() - 1)
            {
                currentState = ANIMATE_UP;
            }
            else if (event.getVelocity() > 0 && curPage > 0)
            {
                currentState = ANIMATE_DOWN;
            }
        }
    }

    void QwVerticalSwipeContainer::handleKeyEvent(uint8_t key)
    {
        if (currentState != NO_ANIMATION)
        {
            return;
        }

        pageIndicator.setVisible(true);
        pageIndicator.invalidate();
        pageIndicator_show = true;
        tick_ = 0;

        QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();
        if (CurrentSwipePageBase != nullptr && CurrentSwipePageBase->getAbsoluteRect().y == 0)
        {
            CurrentSwipePageBase->handleKeyEvent(key);
            if (CurrentSwipePageBase->get_used_key())
            {
                CurrentSwipePageBase->set_used_key(false);
                return;
            }
        }

        const uint8_t curPage = getSelectedPage();

        if (key == SWIP_FOCUS_WATCHES_UP_KEY)
        {
            if(curPage < getNumberOfPages() - 1)
            {
                currentState = ANIMATE_UP;
            }
            else
            {
                animateDistance = swipeCutoff;
                currentState = ANIMATE_SWIPE_OUT_UP;
            }
        }
        else if (key == SWIP_FOCUS_WATCHES_DOWN_KEY)
        {
            if(curPage >0)
            {
                currentState = ANIMATE_DOWN;
            }
            else
            {
                animateDistance = swipeCutoff;
                currentState = ANIMATE_SWIPE_OUT_DOWN;
            }
        }

    }

    // void QwVerticalSwipeContainer::notify_status_event(StatusEvt* evt)
    // {
    //     QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();

    //     CurrentSwipePageBase->notify_status_event(evt);

    //     switch (evt->evt)
    //     {
    //     case STATUS_EVT::TP_PRESS:
    //     {
    //         ClickEvent click_event_(ClickEvent::PRESSED, evt->x, evt->y);
    //         handleClickEvent(click_event_);
    //         break;
    //     }
    //     case STATUS_EVT::TP_DRAG:
    //     {
    //         DragEvent drag_event_(DragEvent::DRAGGED, evt->oldx, evt->oldy, evt->x, evt->y);
    //         handleDragEvent(drag_event_);
    //         break;
    //     }
    //     case STATUS_EVT::TP_RELEASE:
    //     {
    //         ClickEvent click_event_(ClickEvent::RELEASED, evt->x, evt->y);
    //         handleClickEvent(click_event_);
    //         break;
    //     }
    //     default:
    //         break;
    //     }
    // }

    // void QwVerticalSwipeContainer::notify_operate_event(OperateEvt* evt)
    // {
    //     QwSwipePageBase* CurrentSwipePageBase = getCurrentQwSwipePageBase();

    //     CurrentSwipePageBase->notify_operate_event(evt);

    //     if (evt == nullptr)
    //     {
    //         return;
    //     }

    //     if (evt->evt == OPT_EVT::TP_TICK)
    //     {
    //     }
    //     else
    //     {
    //         switch (evt->evt)
    //         {
    //         case OPT_EVT::KEY:
    //             if (evt->key == KEY_KNOB_UP)
    //             {
    //                 setSelectedPage(getSelectedPage() + 1);
    //             }
    //             else if (evt->key == KEY_KNOB_DOWN)
    //             {
    //                 setSelectedPage(getSelectedPage() - 1);
    //             }
    //             break;
    //         case OPT_EVT::KEY_HOLD:
    //             break;
    //         case OPT_EVT::TP_CLICK:
    //             break;
    //         case OPT_EVT::TP_HOLD:
    //             break;
    //         case OPT_EVT::TP_GESTURE:
    //         {
    //             GestureEvent gesture_event_(GestureEvent::SWIPE_VERTICAL, evt->vct_y, evt->x, evt->y);
    //             handleGestureEvent(gesture_event_);
    //             break;
    //         }
    //         break;
    //         case OPT_EVT::TP_ZOOM:
    //             break;
    //         default:
    //             break;
    //         }
    //     }
    // }

    void QwVerticalSwipeContainer::adjustPages()
    {
        if (dynamicCachingEnabled)
        {
            // 根据当前页面和拖拽偏移预测需要显示的页面
            int16_t currentPage = getSelectedPage();

            // 预测页面切换
            int16_t visiblePage = currentPage;
            if (dragX < -30 && currentPage < totalPages - 1)
            {
                // 向下拖拽超过30像素，预测下一页
                visiblePage = currentPage + 1;
            }
            else if (dragX > 30 && currentPage > 0)
            {
                // 向上拖拽超过30像素，预测上一页
                visiblePage = currentPage - 1;
            }

            // 滑动过程中完全不创建新缓存，只有滑动停止后才重新创建
            if (currentState == NO_ANIMATION && dragX == 0)
            {
                // 确保当前页面和预测页面都在缓存中
                if (currentPageIndex_ != currentPage)
                {
                    // rt_kprintf("adjustPages: page changed from %d to %d, updating cache\n",
                    //        currentPageIndex_, currentPage);
                    currentPageIndex_ = currentPage;
                    updateCacheMap();
                }
            }

            // 计算当前页面在缓存中的相对位置
            int16_t startPage, endPage;
            calculateRequiredPages(currentPageIndex_, startPage, endPage);
            // 聚焦页面在缓存中的相对位置
            int16_t focusedPageRelativeY = (currentPageIndex_ - startPage) * getHeight();
            // 容器偏移 = -聚焦页面的相对位置 + 拖拽偏移
            int16_t containerOffset = -focusedPageRelativeY + dragX;
            // rt_kprintf("adjustPages: dragX=%d, containerOffset=%d, getHeight()=%d\n",
            //        dragX, containerOffset, getHeight());
            pages.moveTo(0, containerOffset);
        }
        else
        {
            pages.moveTo(0, -static_cast<int16_t>(getSelectedPage() * getHeight()) + dragX);
        }
    }

    void QwVerticalSwipeContainer::animateSwipeCancelledUp()
    {
        const uint8_t duration = 14;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::backEaseOut(animationCounter, 0, -animateDistance, duration);
            dragX = animateDistance + delta;

            adjustPages();
        }
        else
        {
            // Final step: stop the animation
            currentState = NO_ANIMATION;
            animationCounter = 0;
            dragX = 0;
            adjustPages();
        }
        animationCounter++;
    }

    void QwVerticalSwipeContainer::animateSwipeCancelledDown()
    {
        const uint8_t duration = 14;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::backEaseOut(animationCounter, 0, animateDistance, duration);
            dragX = animateDistance - delta;

            adjustPages();
        }
        else
        {
            // Final step: stop the animation
            currentState = NO_ANIMATION;
            animationCounter = 0;
            dragX = 0;
            adjustPages();
        }
        animationCounter++;
    }

    void QwVerticalSwipeContainer::animateUp()
    {
        const uint8_t duration = 10;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::cubicEaseOut(animationCounter, 0, getWidth() + animateDistance, duration);
            dragX = animateDistance - delta;
        }
        else
        {
            // Final step: stop the animation
            currentState = NO_ANIMATION;
            animationCounter = 0;
            dragX = 0;
            pageIndicator.goUp();

            // 主要处理按键事件，动画完成后更新动态缓存
            if (dynamicCachingEnabled)
            {
                adjustPages();
                rebuildCacheAfterScroll();
                animationCounter++;;
                return;
            }
        }
        adjustPages();
        animationCounter++;
    }

    void QwVerticalSwipeContainer::animateDown()
    {
        const uint8_t duration = 10;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::cubicEaseOut(animationCounter, 0, getWidth() - animateDistance, duration);
            dragX = animateDistance + delta;
        }
        else
        {
            // Final step: stop the animation
            currentState = NO_ANIMATION;
            animationCounter = 0;
            dragX = 0;
            pageIndicator.goDown();

            // 主要处理按键事件，动画完成后更新动态缓存
            if (dynamicCachingEnabled)
            {
                // 如果不加adjust会有下面覆盖上面页面的现象
                adjustPages();
                rebuildCacheAfterScroll();
                animationCounter++;;
                return;
            }
        }
        adjustPages();
        animationCounter++;
    }

    void QwVerticalSwipeContainer::animateSwipeOutUp()
    {
        const uint8_t duration = 14;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::cubicEaseOut(animationCounter, 0, -animateDistance, duration);
            dragX =  delta;

            adjustPages();
        }
        else
        {
            // Final step: stop the animation
            currentState = ANIMATE_SWIPE_CANCELLED_UP;
            animationCounter = 0;
            animateDistance = dragX;

        }
        animationCounter++;
    }

    void QwVerticalSwipeContainer::animateSwipeOutDown()
    {
        const uint8_t duration = 14;

        if (animationCounter <= duration)
        {
            const int16_t delta = EasingEquations::cubicEaseOut(animationCounter, 0, animateDistance, duration);
            dragX = delta;

            adjustPages();
        }
        else
        {
            // Final step: stop the animation
            currentState = ANIMATE_SWIPE_CANCELLED_DOWN;
            animationCounter = 0;
            animateDistance = dragX;
        }
        animationCounter++;
    }

    QwSwipePageBase* QwVerticalSwipeContainer::getCurrentQwSwipePageBase()
    {
        uint8_t index = getSelectedPage();

        if (dynamicCachingEnabled)
        {
            return getCachedPage(index);
        }

        QwSwipePageBase* ret = nullptr;

        for (Drawable* d = pages.getFirstChild(); d; d = d->getNextSibling())
        {
            if (d->getX() == 0 && d->getY() == index * getHeight())
            {
                ret = static_cast<QwSwipePageBase*>(d);
                if (ret == nullptr)
                {
                    assert(false && "get QwSwipePageBase fail");
                }
                break;
            }
        }

        return ret;
    }

    QwSwipePageBase* QwVerticalSwipeContainer::getQwSwipePageBaseFromIndex(int index)
    {
        if (dynamicCachingEnabled)
        {
            return getCachedPage(index);
        }

        QwSwipePageBase* ret = nullptr;

        for (Drawable* d = pages.getFirstChild(); d; d = d->getNextSibling())
        {
            if (d->getX() == 0 && d->getY() == index * getHeight())
            {
                ret = static_cast<QwSwipePageBase*>(d);
                if (ret == nullptr)
                {
                    assert(false && "get QwSwipePageBase fail");
                }
                break;
            }
        }

        return ret;
    }


    void QwVerticalSwipeContainer::cleanupDynamicCache()
    {
        if(!dynamicCachingEnabled) return;
        // for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        // {
        //     if (cachedPages_[i] != nullptr) {
        //         // 调用每个对象的析构函数
        //         //cachedPages_[i].quit();
        //         //cachedPages_[i].~QwWorkoutStepInfo();
        //         // 释放内存
        //         lv_mem_free(cachedPages_[i]);
        //         cachedPages_[i] = nullptr;
        //     }
        // }
    }

    void QwVerticalSwipeContainer::calculateRequiredPages(int16_t currentPage, int16_t& startPage, int16_t& endPage)
    {
        // 动态缓存策略：根据缓存大小确定显示范围
        int16_t halfCache = SEIP_DYNAMIC_CACHING_NUM / 2;

        // 修复：确保页面范围计算一致
        // 对于3个缓存页面，应该是 [currentPage-1, currentPage, currentPage+1]
        startPage = currentPage - halfCache;
        endPage = currentPage + (SEIP_DYNAMIC_CACHING_NUM - halfCache - 1);

        // 计算页面范围

        // 边界处理 - 修复逻辑
        if (startPage < 0)
        {
            // 开始页面小于0，向右移动范围
            int16_t shift = -startPage;
            startPage = 0;
            endPage = MIN(endPage + shift, totalPages - 1);
            // 左边界修正
        }
        else if (endPage >= totalPages)
        {
            // 结束页面超出范围，保持当前页面在缓存中心，向左调整
            endPage = totalPages - 1;

            // 重新计算startPage，确保当前页面仍在缓存范围内
            // 优先保持当前页面的位置
            if (currentPage == endPage)
            {
                // 当前页面是最后一页，缓存应该是最后几页
                startPage = MAX(0, endPage - SEIP_DYNAMIC_CACHING_NUM + 1);
                // 最后一页处理
            }
            else
            {
                // 当前页面不是最后一页，尽量保持当前页面在中心
                startPage = MAX(0, MIN(currentPage - halfCache, endPage - SEIP_DYNAMIC_CACHING_NUM + 1));
                // 接近末尾处理
            }
        }
        // 页面范围计算完成
    }

    void QwVerticalSwipeContainer::updateCachedPages()
    {
        if (!dynamicCachingEnabled)
        {
            return;
        }
        int16_t startPage, endPage;
        calculateRequiredPages(currentPageIndex_, startPage, endPage);

        // 重新分配缓存对象给需要的页面
        uint8_t cacheIndex = 0;
        for (int16_t pageIndex = startPage; pageIndex <= endPage; pageIndex++)
        {
            if (pageIndex >= 0 && pageIndex < totalPages && cacheIndex < SEIP_DYNAMIC_CACHING_NUM)
            {
                QwSwipePageBase* page = cachedPages_[cacheIndex];

                // 检查是否需要更新内容
                int16_t oldPageIndex = pageMapping[cacheIndex];
                bool needUpdate = (oldPageIndex != pageIndex);

                if (needUpdate)
                {
                    
                    // 清除旧内容
                    page->removeAll();
                    // 更新页面映射
                    pageMapping[cacheIndex] = pageIndex;

                    // 更新页面内容
                    updatePageContent(page, pageIndex);

                }
                cacheIndex++;
            }
        }
        // 只重置页面映射，但保持对象可见
        for (uint8_t i = cacheIndex; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            pageMapping[i] = -1;
        }

    }

    void QwVerticalSwipeContainer::updateCacheMap()
    {
         int16_t startPage, endPage;
        calculateRequiredPages(currentPageIndex_, startPage, endPage);
        // 重新分配缓存对象给需要的页面
        uint8_t cacheIndex = 0;
        for (int16_t pageIndex = startPage; pageIndex <= endPage; pageIndex++)
        {
            if (pageIndex >= 0 && pageIndex < totalPages && cacheIndex < SEIP_DYNAMIC_CACHING_NUM)
            {
                QwSwipePageBase* page = cachedPages_[cacheIndex];

                // 检查是否需要更新内容
                int16_t oldPageIndex = pageMapping[cacheIndex];
                bool needUpdate = (oldPageIndex != pageIndex);

                if (needUpdate)
                {
                    // 清除旧内容
                    page->removeAll();
                    // 更新页面映射
                    pageMapping[cacheIndex] = pageIndex;

                }
                cacheIndex++;
            }
        }
        // 只重置页面映射，但保持对象可见
        for (uint8_t i = cacheIndex; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            pageMapping[i] = -1;
        }
    }

    QwSwipePageBase* QwVerticalSwipeContainer::getCachedPage(int16_t pageIndex)
    {
        if (!dynamicCachingEnabled)
        {
            return nullptr;
        }

        // 使用页面映射查找缓存对象
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            if (pageMapping[i] == pageIndex)
            {
                return cachedPages_[i];
            }
        }

        // rt_kprintf("getCachedPage: page %d not found in cache\n", pageIndex);
        return nullptr;
    }
    void QwVerticalSwipeContainer::rebuildCacheAfterScroll()
    {
        if (!dynamicCachingEnabled)
        {
            return;
        }

        // 获取当前页面
        int16_t currentPage = getSelectedPage();
        // 完全清空所有缓存映射
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            pageMapping[i] = -1;
        }

        // 重新设置当前页面索引
        currentPageIndex_ = currentPage;
        // rt_kprintf("Set currentPageIndex_ to %d\n", currentPageIndex_);

        // 根据当前页面重新创建缓存
        updateCachedPages();

        // 调整页面位置
        adjustPages();

        // 缓存重建后刷新页面容器
        // refreshPagesContainer();
    }

    void QwVerticalSwipeContainer::updatePageContent(QwSwipePageBase* page, int16_t pageIndex)
    {
        if (pageUpdateCallback != nullptr && pageUpdateCallback->isValid())
        {
            pageUpdateCallback->execute(page, pageIndex);
        }
    }
    
    // Dynamic caching implementation
    void QwVerticalSwipeContainer::setDynamicCaching(bool enable, uint8_t totalPages)
    {
        if (enable == dynamicCachingEnabled && totalPages == this->totalPages)
        {
            return; // No change needed
        }

        // Clean up existing cache if any
        cleanupDynamicCache();

        dynamicCachingEnabled = enable;
        this->totalPages = totalPages;

        if (enable && totalPages > 0)
        {
            initializeDynamicCache();
            pageIndicator.setNumberOfPages(totalPages);
        }
    }

    void QwVerticalSwipeContainer::setPageUpdateCallback(GenericCallback<QwSwipePageBase*, int16_t>& callback)
    {
        pageUpdateCallback = &callback;

        // 如果动态缓存已启用，立即更新缓存页面
        if (dynamicCachingEnabled)
        {
            updateCachedPages();
        }
    }


    void QwVerticalSwipeContainer::initializeDynamicCache()
    {
        if (!dynamicCachingEnabled || totalPages == 0)
        {
            return;
        }
        uint8_t cacheSize = SEIP_DYNAMIC_CACHING_NUM;
        // 如果总页面数少于SEIP_DYNAMIC_CACHING_NUM，则缓存大小等于总页面数
        if (totalPages < SEIP_DYNAMIC_CACHING_NUM)
        {
            cacheSize = totalPages;
        }

        for(int i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            cachedPages_[i] = nullptr;
            pageMapping[i] = -1;
        }

        for (uint8_t i = 0; i < cacheSize; i++)
        {
            // cachedPages_[i] = static_cast<QwSwipePageBase*>(lv_mem_alloc(sizeof(QwSwipePageBase)));
            // new (cachedPages_[i]) QwSwipePageBase();
            cachedPages_[i].setWidthHeight(getWidth(), getHeight());
            pages.add(cachedPages_[i]);
            pageMapping[i] = -1;  // 初始时未分配
        }

        currentPageIndex_ = 0;

    }

    void QwVerticalSwipeContainer::refreshPagesContainer()
    {
        if (!dynamicCachingEnabled)
        {
            return;
        }

        // 临时存储当前的缓存页面
        QwSwipePageBase* tempPages[SEIP_DYNAMIC_CACHING_NUM];
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            tempPages[i] = cachedPages_[i];
        }

        // 从pages容器中移除所有页面
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            if (tempPages[i] != nullptr)
            {
                pages.remove(*tempPages[i]);
                // rt_kprintf("Removed cache[%d] from pages container\n", i);
            }
        }

        // 重新添加所有页面到pages容器
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            if (tempPages[i] != nullptr)
            {
                pages.add(*tempPages[i]);
                // rt_kprintf("Re-added cache[%d] to pages container\n", i);
            }
        }

        // 输出三块缓存的坐标信息
        for (uint8_t i = 0; i < SEIP_DYNAMIC_CACHING_NUM; i++)
        {
            if (tempPages[i] != nullptr)
            {
                int16_t pageX = tempPages[i]->getX();
                int16_t pageY = tempPages[i]->getY();
                int16_t mappedPageIndex = pageMapping[i];
                bool isVisible = tempPages[i]->isVisible();

                // 获取绝对坐标
                Rect absoluteRect = tempPages[i]->getAbsoluteRect();

                // rt_kprintf("Cache[%d] -> Page %d: relative(%d,%d) absolute(%d,%d) visible=%s\n",
                    //    i, mappedPageIndex, pageX, pageY,
                    //    absoluteRect.x, absoluteRect.y,
                    //    isVisible ? "true" : "false");
            }
            else
            {
                rt_kprintf("Cache[%d] -> NULL or no mapping\n", i);
            }
        }
    }

} // namespace touchgfx
