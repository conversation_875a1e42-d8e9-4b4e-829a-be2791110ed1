/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   service_sport_ability.c
@Time    :   2024/12/23 13:37:54
*
************************************************************/
#ifndef SIMULATOR
#include "string.h"
#include "qw_fs.h"
#include "service_config.h"
#include "service_event.h"
#include "service_sport_ability.h"
#include "app_sensor_hub_service.h"
#include "qw_system_params.h"
#include "poweronoff_cb.h"
#include "thread_pool.h"
#include "cfg_header_def.h"
#include "developer_mode.h"
// 运动中自动开启数据采集控制
#define FITNESS_AUTO_ENABLE_WITH_SPORT 1
#endif

#define LIB_GM_SDK_VERSION_MAJOR                        0               //GoMore SDK主版本号
#define LIB_GM_SDK_VERSION_MINOR                        15              //GoMore SDK次版本号
#define LIB_GM_UPDATE_TIMESTAMP_MAX_DIFF                30              //GoMore算法执行中时时间戳最大变化量
#define SPORT_ABILITY_ANALYSE_TIMEOUT       (50)
#define GOMORE_FITNESS_HISTORY_DATA_LENTH 1344
static int lib_gm_persistent_data_load(void);
typedef struct _lib_gm
{
    sport_type_t sport_type;
    sport_status_t sport_status;
    uint8_t should_resume;
    uint32_t last_time;
    lib_gm_rt_outputs_t rt_outputs;
    lib_gm_summary_outputs_t summary;
    lib_ride_statistical_outputs_t ride_statistical;
    lib_run_statistical_outputs_t run_statistical;
    lib_adaptability_fitness_outputs_t adaptability_fitness;
    lib_gm_historical_data_t historical;
    lib_gm_accumulated_t accumulated;
    lib_gm_cache_t cache;
} lib_gm_t;

static lib_gm_t s_lib_run = { 0 };
static lib_gm_t *p_lib_gm = &s_lib_run;
static sensorhub_pack_data_t g_gm_fitness_analyzer_pack = {0};
static uint8_t sport_start_type = 0;
static uint8_t last_fitnessNotifierGPS = 0;
rt_timer_t g_fitness_analyzer_timer = NULL;
static bool g_fitness_auto_enable = true; // 是否自动开启GoMore算法数据采集

//实时输出的生理数据初始化
static inline void lib_gm_rt_outputs_init(lib_gm_rt_outputs_t *rt_outputs)
{
    if (rt_outputs != NULL) {
        rt_outputs->stamina = 0xff;
        rt_outputs->stamina_start = 0xff;
        rt_outputs->stamina_min = 0xff;
        rt_outputs->teAerobic = 0xff;
        rt_outputs->teAnaerobic = 0xff;
        rt_outputs->trainingLoad = 0xffff;
        rt_outputs->auto_pause_event = 0xff;
    }
}

//活动结束时输出的生理数据初始化
static inline void lib_gm_summary_outputs_init(lib_gm_summary_outputs_t *summary)
{
    if (summary != NULL) {
        summary->lt_speed = 0x7fffffff;
        summary->teAerobic = 0xff;
        summary->teAnaerobic = 0xff;
        summary->trainingLoad = 0xffff;
        summary->run_VO2max = 0xffff;
        summary->ride_VO2max = 0xffff;
        summary->stamina_start = 0xff;
        summary->stamina_end = 0xff;
        summary->stamina_min = 0xff;
        summary->fatPct = 0xff;
        summary->carbPct = 0xff;
        summary->lt_hr = 0xff;
        summary->max_hr = 0xff;
        summary->FTP = 0xffff;
        summary->efficiency = 0xff;
    }
}

//活动中累积数据初始化
static inline void lib_gm_accumulated_init(lib_gm_accumulated_t *accumulated)
{
    if (accumulated != NULL) {
        accumulated->kcal = -1.0f;
        accumulated->fatOut = -1.0f;
        accumulated->carbOut = -1.0f;
        accumulated->stamina_start = -1.0f;
        accumulated->stamina_min = -1.0f;
        accumulated->teAerobic = -1.0f;
        accumulated->teAnaerobic = -1.0f;
        accumulated->trainingLoad = 0;
        accumulated->step_count = 0;
    }
}

//跑步生理参数统计数据初始化
static inline void lib_run_statistical_outputs_init(lib_run_statistical_outputs_t *statistical)
{
    if (statistical != NULL)
    {
        statistical->stamina_level = 0xffff;
        statistical->power_ability = 0xffff;
        statistical->anaerobic_ability = 0xffff;
        statistical->aerobic_power_ability = 0xffff;
        statistical->aerobic_abiltiy = 0xffff;
        statistical->endurance_abiltiy = 0xffff;
        statistical->ultra_endurance_ability = 0xffff;
        statistical->VO2max = 0xffff;
        statistical->bestRunTime_5 = 0x7fffffff;
        statistical->bestRunTime_10 = 0x7fffffff;
        statistical->bestRunTime_half = 0x7fffffff;
        statistical->bestRunTime_full = 0x7fffffff;
        statistical->lt_hr = 0xff;
        statistical->lt_speed= 0x7fffffff;
        statistical->personalZone = 0xff;
        statistical->max_hr = 0xff;
    }
}

//骑行生理参数统计数据初始化
static inline void lib_ride_statistical_outputs_init(lib_ride_statistical_outputs_t *statistical)
{
    if (statistical != NULL)
    {
        statistical->stamina_level = 0xffff;
        statistical->power_ability = 0xffff;
        statistical->anaerobic_ability = 0xffff;
        statistical->aerobic_power_ability = 0xffff;
        statistical->aerobic_abiltiy = 0xffff;
        statistical->endurance_abiltiy = 0xffff;
        statistical->ultra_endurance_ability = 0xffff;
        statistical->VO2max = 0xffff;
        statistical->FTP = 0xffff;
        statistical->PWR = 0xffff;
        statistical->MAP = 0xffff;
    }
}

//骑行生理参数统计数据初始化
static inline void lib_adaptability_statistical_outputs_init(lib_adaptability_fitness_outputs_t *statistical)
{
    if (statistical != NULL)
    {
        statistical->recovery_time = 0x7fffffff;
        statistical->end_time = 0x7fffffff;
        statistical->stamina = 0xff;
        statistical->start_time = 0x7fffffff;
        statistical->tlTrend = 0xff;
        statistical->training_load = 0xffff;
        statistical->training_status = 0xff;
    }
}

//个人生理历史数据初始化
static inline void lib_gm_historical_data_init(lib_gm_historical_data_t *historical)
{
    if (historical != NULL)
    {
        historical->ride_num = 0;
        historical->run_num = 0;
        historical->workout_num = 0;
    }
}

uint8_t read_gm_previoud_data()
{
    QW_FIL *fp = NULL;
    UINT br = 0;
    uint32_t header = 0;
    uint32_t curtimes = service_datetime_get_gmt_time();
    uint32_t timestamp = 0;
    char *p_header = get_algo_share_mem();
    if (qw_f_open(&fp, LIB_GM_FITNESS_DATA, QW_FA_READ) == QW_OK) {
        if (qw_f_read(fp, &timestamp, sizeof(timestamp), &br) != QW_OK || br != sizeof(timestamp)) {
            RUN_ABIL_LOG_E("read gm previoud timestamp data error");
            qw_f_close(fp);
            return false;
        } else {
            RUN_ABIL_LOG_I("read gomore historical data sync time filetime=%d curtimes=%d",timestamp,curtimes);
            if (timestamp > curtimes) {
                memset(p_header, 0, 2048);
                return false;
            } else {
                p_header=p_header+4;
            }
        }
        //检查GoMore SDK版本，版本不一致则清除previous data
        if (qw_f_read(fp, &header, sizeof(header), &br) != QW_OK || br != sizeof(header))
        {
            RUN_ABIL_LOG_E("read gm previoud header data error\n");
            qw_f_close(fp);
            return false;
        } else {
            RUN_ABIL_LOG_D("read gm previoud header data =%d\n",header);
            memcpy(p_header, &header, 4);
            p_header=p_header+4;
        }
        if (qw_f_read(fp, p_header, header, &br) != QW_OK || br != header)
        {
            RUN_ABIL_LOG_E("read gm previoud header data error\n");
            qw_f_close(fp);
            return false;
        }
    } else {
        RUN_ABIL_LOG_E("read gm previoud data sdk version error\n");
        return false;
    }

    return true;
}
//通知gomore时间更改
uint8_t gm_fitness_time_change_notify()
{
    read_gm_previoud_data();
    uint8_t data = 0;
    if (qw_dataserver_publish(CONFIG_QW_EVENT_NAME_SYNC_TIME, &data, sizeof(data)) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("publish CONFIG_QW_EVENT_NAME_SYNC_TIME error");
    }
    return 0;
}

//同步用户历史数据给算法
void gm_fitness_init()
{
    uint8_t ret = read_gm_previoud_data();

    lib_run_statistical_outputs_init(&p_lib_gm->run_statistical);
    lib_ride_statistical_outputs_init(&p_lib_gm->ride_statistical);
    lib_adaptability_statistical_outputs_init(&p_lib_gm->adaptability_fitness);
    lib_gm_historical_data_init(&p_lib_gm->historical);
    lib_gm_persistent_data_load();
    p_lib_gm->sport_status = SPORT_INVALID;
}

//将previousData保存到Flash，返回值：0 - 成功，-1 - 失败  运动结束与关机调用
static int LIB_GM_FITNESS_DATA_save(void)
{
    //p_lib_gm->cache.p_previoud_data 暂时没地方赋值  需要算法回传
    QW_FIL *fp = NULL;
    UINT bw = 0;
    if (qw_f_open(&fp, LIB_GM_FITNESS_BAK_DATA, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK) {
        return -1;
    }

    const uint8_t header[4] = { 'Q', 'W', LIB_GM_SDK_VERSION_MAJOR, LIB_GM_SDK_VERSION_MINOR };
    if (qw_f_write(fp, header, 4, &bw) != QW_OK || bw != 4) {
        qw_f_close(fp);
        return -1;
    }

    if (qw_f_write(fp, p_lib_gm->cache.p_previoud_data, p_lib_gm->cache.previous_data_size, &bw) != QW_OK || bw != p_lib_gm->cache.previous_data_size) {
        qw_f_close(fp);
        return -1;
    }

    qw_f_close(fp);
    qw_f_unlink(LIB_GM_FITNESS_DATA);
    qw_f_rename(LIB_GM_FITNESS_BAK_DATA, LIB_GM_FITNESS_DATA);

    return 0;
}

//骑行生理参数统计数据更新
static void lib_ride_statistical_outputs_update(lib_ride_statistical_outputs_t *statistical, const fitness_summary_t *p_fitness)
{
    if (statistical != NULL && p_fitness != NULL) {
        if (p_fitness->cyclingLevelOut[0] >= 0.0f && p_fitness->cyclingLevelOut[0] <= 80.0f) {
            statistical->stamina_level = (uint16_t)(p_fitness->cyclingLevelOut[0] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[3] >= 0.0f && p_fitness->cyclingLevelOut[3] <= 80.0f) {
            statistical->power_ability = (uint16_t)(p_fitness->cyclingLevelOut[3] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[4] >= 0.0f && p_fitness->cyclingLevelOut[4] <= 80.0f) {
            statistical->anaerobic_ability = (uint16_t)(p_fitness->cyclingLevelOut[4] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[5] >= 0.0f && p_fitness->cyclingLevelOut[5] <= 80.0f) {
            statistical->aerobic_power_ability = (uint16_t)(p_fitness->cyclingLevelOut[5] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[6] >= 0.0f && p_fitness->cyclingLevelOut[6] <= 80.0f) {
            statistical->aerobic_abiltiy = (uint16_t)(p_fitness->cyclingLevelOut[6] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[7] >= 0.0f && p_fitness->cyclingLevelOut[7] <= 80.0f) {
            statistical->endurance_abiltiy = (uint16_t)(p_fitness->cyclingLevelOut[7] * 10.0f);
        }

        if (p_fitness->cyclingLevelOut[8] >= 0.0f && p_fitness->cyclingLevelOut[8] <= 80.0f) {
            statistical->ultra_endurance_ability = (uint16_t)(p_fitness->cyclingLevelOut[8] * 10.0f);
        }

        if (p_fitness->cValueOut[0] >= 0.0f) {
            statistical->VO2max = (uint16_t)(p_fitness->cValueOut[0] * 10.0f);
            p_lib_gm->summary.ride_VO2max = (uint16_t)(p_fitness->cValueOut[0] * 10.0f);
        }

        if (p_fitness->cyclingIndex[0] >= 0.0f) {
            statistical->FTP = (uint16_t)(p_fitness->cyclingIndex[0]);
            p_lib_gm->summary.FTP = (uint16_t)(p_fitness->cyclingIndex[0]);
            //计算工体比 PWR
            uint16_t user_weight = get_user_info_weight();
            if (user_weight) { // 除0保护
                statistical->PWR = (uint16_t)(p_fitness->cyclingIndex[0] / (user_weight/10) * (100));
            }
        }

        if (p_fitness->cyclingIndex[1] >= 0.0f) {
            statistical->MAP = (uint16_t)(p_fitness->cyclingIndex[1]);
        }
    }
}

//跑步生理参数统计数据更新
static void lib_run_statistical_outputs_update(lib_run_statistical_outputs_t *statistical, const fitness_summary_t *p_fitness)
{
    if (statistical != NULL && p_fitness != NULL) {
        if (p_fitness->runLevelOut[0] >= 0.0f && p_fitness->runLevelOut[0] <= 80.0f) {
            statistical->stamina_level = (uint16_t)(p_fitness->runLevelOut[0] * 10.0f);
        }

        if (p_fitness->runLevelOut[3] >= 0.0f && p_fitness->runLevelOut[3] <= 80.0f) {
            statistical->power_ability = (uint16_t)(p_fitness->runLevelOut[3] * 10.0f);
        }

        if (p_fitness->runLevelOut[4] >= 0.0f && p_fitness->runLevelOut[4] <= 80.0f) {
            statistical->anaerobic_ability = (uint16_t)(p_fitness->runLevelOut[4] * 10.0f);
        }

        if (p_fitness->runLevelOut[5] >= 0.0f && p_fitness->runLevelOut[5] <= 80.0f) {
            statistical->aerobic_power_ability = (uint16_t)(p_fitness->runLevelOut[5] * 10.0f);
        }

        if (p_fitness->runLevelOut[6] >= 0.0f && p_fitness->runLevelOut[6] <= 80.0f) {
            statistical->aerobic_abiltiy = (uint16_t)(p_fitness->runLevelOut[6] * 10.0f);
        }

        if (p_fitness->runLevelOut[7] >= 0.0f && p_fitness->runLevelOut[7] <= 80.0f) {
            statistical->endurance_abiltiy = (uint16_t)(p_fitness->runLevelOut[7] * 10.0f);
        }

        if (p_fitness->runLevelOut[8] >= 0.0f && p_fitness->runLevelOut[8] <= 80.0f) {
            statistical->ultra_endurance_ability = (uint16_t)(p_fitness->runLevelOut[8] * 10.0f);
        }

        if (p_fitness->vValueOut[0] >= 0.0f) {
            statistical->VO2max = (uint16_t)(p_fitness->vValueOut[0] * 10.0f);
            p_lib_gm->summary.run_VO2max = (uint16_t)(p_fitness->vValueOut[0] * 10.0f);
        }

        if (p_fitness->pace[0] >= 0.0f) {
            statistical->lt_speed = (uint32_t)(60.00f/p_fitness->pace[0]*100.0f);
            p_lib_gm->summary.lt_speed = (uint32_t)(p_fitness->pace[0]*100.0f);
        }

        if (p_fitness->pace[1] >= 0.0f) {
            statistical->lt_hr = (uint8_t)(p_fitness->pace[1]);
            p_lib_gm->summary.lt_hr = (uint8_t)(p_fitness->pace[1]);
        }

        if (p_fitness->bestRunTime[1] >= 0) {
            statistical->bestRunTime_5 = (uint32_t)(p_fitness->bestRunTime[1]);
        }

        if (p_fitness->bestRunTime[2] >= 0) {
            statistical->bestRunTime_10 = (uint32_t)(p_fitness->bestRunTime[2]);
        }

        if (p_fitness->bestRunTime[5] >= 0) {
            statistical->bestRunTime_half = (uint32_t)(p_fitness->bestRunTime[5]);
        }

        if (p_fitness->bestRunTime[6] >= 0) {
            statistical->bestRunTime_full = (uint32_t)(p_fitness->bestRunTime[6]);
        }

        if (p_fitness->personalZone[3] >= 0.0f) {
            statistical->personalZone = (uint8_t)(p_fitness->personalZone[3]);
        }

        if (p_fitness->hrEstorOut[0] >= 0.0f) {
            statistical->max_hr = (uint8_t)(p_fitness->hrEstorOut[0]);
            p_lib_gm->summary.max_hr = (uint8_t)(p_fitness->hrEstorOut[0]);
        }

        if (p_fitness->efficiency[0] >= 0.0f) {
            p_lib_gm->summary.efficiency = (uint8_t)(p_fitness->efficiency[0] * 100.0f);
        }
    }
}

//保存持久性的数据：statistical、historical
static bool lib_gm_persistent_data_save()
{
    QW_FIL *fp = NULL;
    UINT bw = 0;
    if (qw_f_open(&fp, LIB_GM_PERSISTENT_DATA, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK) {
        return 0;
    }

    //文件头：共4字节，前两个字节组成“QW”，后两个字节是主版本号和次版本号
    const uint8_t header[4] = { 'Q', 'W', 1, 0 };

    if (qw_f_write(fp, header, 4, &bw) != QW_OK || bw != 4) {
        goto err_handler;
    }

    //数据格式：[tag] + [num] + [size] + [data]

    //statistical：tag = 1, num = 1, size = sizeof(lib_gm_statistical_outputs_t), data = p_lib_gm->statistical
    const uint8_t run_statistical_size = sizeof(lib_run_statistical_outputs_t);
    const uint8_t ride_statistical_size = sizeof(lib_ride_statistical_outputs_t);
    const uint8_t adaptability_statistical_size = sizeof(lib_adaptability_fitness_outputs_t);
    const uint8_t statistical_header[5] = { 1, 1, run_statistical_size, ride_statistical_size, adaptability_statistical_size};

    if (qw_f_write(fp, statistical_header, 5, &bw) != QW_OK || bw != 5) {
        goto err_handler;
    }

    if (qw_f_write(fp, &p_lib_gm->run_statistical, run_statistical_size, &bw) != QW_OK ||
        bw != run_statistical_size) {

        goto err_handler;
    }

    if (qw_f_write(fp, &p_lib_gm->ride_statistical, ride_statistical_size, &bw) != QW_OK ||
        bw != ride_statistical_size) {
        goto err_handler;
    }

    if (qw_f_write(fp, &p_lib_gm->adaptability_fitness, adaptability_statistical_size, &bw) != QW_OK ||
        bw != adaptability_statistical_size) {
        goto err_handler;
    }
    //historical: tag = 2, num, size = sizeof(lib_gm_historical_record_t), data = p_lib_gm->historical

    const uint8_t run_historical_record_size = sizeof(lib_run_historical_record_t);
    const uint8_t run_historical_header[3] = { 2, p_lib_gm->historical.run_num, run_historical_record_size};

    if (qw_f_write(fp, run_historical_header, 3, &bw) != QW_OK || bw != 3) {
        goto err_handler;
    }

    for (uint8_t i = 0; i < p_lib_gm->historical.run_num; i++) {
        if (qw_f_write(fp, p_lib_gm->historical.run_records + i, run_historical_record_size, &bw) != QW_OK ||
            bw != run_historical_record_size) {
            goto err_handler;
        }
    }

    const uint8_t ride_historical_record_size = sizeof(lib_ride_historical_record_t);
    const uint8_t ride_historical_header[3] = { 2, p_lib_gm->historical.ride_num, ride_historical_record_size};

    if (qw_f_write(fp, ride_historical_header, 3, &bw) != QW_OK || bw != 3) {
        goto err_handler;
    }

    for (uint8_t i = 0; i < p_lib_gm->historical.ride_num; i++) {
        if (qw_f_write(fp, p_lib_gm->historical.ride_records + i, ride_historical_record_size, &bw) != QW_OK ||
             bw != ride_historical_record_size) {
            goto err_handler;
        }
    }

    const uint8_t workout_historical_record_size = sizeof(historical_workout_status_t);
    const uint8_t workout_historical_header[3] = { 2, p_lib_gm->historical.workout_num, workout_historical_record_size};

    if (qw_f_write(fp, workout_historical_header, 3, &bw) != QW_OK || bw != 3) {
        goto err_handler;
    }

    for (uint8_t i = 0; i < p_lib_gm->historical.workout_num; i++) {
        if (qw_f_write(fp, p_lib_gm->historical.workout_status + i, workout_historical_record_size, &bw) != QW_OK ||
            bw != workout_historical_record_size) {
            goto err_handler;
        }
    }

    qw_f_close(fp);

    return 0;

err_handler:
    qw_f_close(fp);
    return 0;
}

//加载持久性的数据：statistical、historical
static int lib_gm_persistent_data_load(void)
{
    QW_FIL *fp = NULL;
    UINT br = 0;
    if (qw_f_open(&fp, LIB_GM_PERSISTENT_DATA, QW_FA_READ) != QW_OK) {
        return -1;
    }

    uint8_t header[4] = { 0 };
    if (qw_f_read(fp, header, 4, &br) != QW_OK || br != 4) {
        goto err_handler;
    }

    //根据版本号确定数据应该如何解析，后续如果数据确实发生了变化，则应同步修改这里的实现
    if (header[0] != 'Q' || header[1] != 'W' || header[2] != 1 || header[3] != 0) {
        goto err_handler;
    }

    const uint8_t run_statistical_size = sizeof(lib_run_statistical_outputs_t);
    const uint8_t ride_statistical_size = sizeof(lib_ride_statistical_outputs_t);
    const uint8_t adaptability_statistical_size = sizeof(lib_adaptability_fitness_outputs_t);
    uint8_t statistical_header[5] = { 0 };
    if (qw_f_read(fp, statistical_header, 5, &br) != QW_OK || br != 5) {
        goto err_handler;
    }

    if (statistical_header[0] != 1 || statistical_header[1] != 1 ||
        statistical_header[2] != run_statistical_size||
        statistical_header[3] != ride_statistical_size ||
        statistical_header[4] != adaptability_statistical_size) {
        goto err_handler;
    }

    if (qw_f_read(fp, &p_lib_gm->run_statistical, run_statistical_size, &br) != QW_OK || br != run_statistical_size) {
    }

    if (qw_f_read(fp, &p_lib_gm->ride_statistical, ride_statistical_size, &br) != QW_OK || br != run_statistical_size) {
    }

    if (qw_f_read(fp, &p_lib_gm->adaptability_fitness, adaptability_statistical_size, &br) != QW_OK ||
        br != adaptability_statistical_size) {
    }

    const uint8_t run_historical_record_size = sizeof(lib_run_historical_record_t);
    uint8_t run_historical_header[3] = { 0 };
    if (qw_f_read(fp, run_historical_header, 3, &br) != QW_OK || br != 3) {
    }
    if (run_historical_header[0] != 2 || run_historical_header[1] > LIB_GM_HISTORICAL_MAX_NUM ||
        run_historical_header[2] != run_historical_record_size) {
    }
    for (uint8_t i = 0; i < run_historical_header[1]; i++) {
        if (qw_f_read(fp, p_lib_gm->historical.run_records + i, run_historical_record_size, &br) != QW_OK ||
            br != run_historical_record_size) {
        }
    }
    const uint8_t ride_historical_record_size = sizeof(lib_ride_historical_record_t);
    uint8_t ride_historical_header[3] = { 0 };
    if (qw_f_read(fp, ride_historical_header, 3, &br) != QW_OK || br != 3) {
    }
    if (ride_historical_header[0] != 2 || ride_historical_header[1] > LIB_GM_HISTORICAL_MAX_NUM ||
        ride_historical_header[2] != ride_historical_record_size) {
    }
    for (uint8_t i = 0; i < ride_historical_header[1]; i++) {
        if (qw_f_read(fp, p_lib_gm->historical.ride_records + i, ride_historical_record_size, &br) != QW_OK ||
            br != ride_historical_record_size) {
        }
    }
    const uint8_t workout_historical_record_size = sizeof(historical_workout_status_t);
    uint8_t workout_historical_header[3] = { 0 };
    if (qw_f_read(fp, workout_historical_header, 3, &br) != QW_OK || br != 3) {
    }
    if (workout_historical_header[0] != 2 || workout_historical_header[1] > LIB_GM_HISTORICAL_MAX_NUM ||
        workout_historical_header[2] != workout_historical_record_size) {
    }
    for (uint8_t i = 0; i < workout_historical_header[1]; i++) {
        if (qw_f_read(fp, p_lib_gm->historical.workout_status + i, workout_historical_record_size, &br) != QW_OK ||
            br != workout_historical_record_size) {
        }
    }
    p_lib_gm->historical.run_num = run_historical_header[1];
    p_lib_gm->historical.ride_num = ride_historical_header[1];
    p_lib_gm->historical.workout_num = workout_historical_header[1];

    qw_f_close(fp);
    return 0;

err_handler:
    qw_f_close(fp);
    return -1;
}

//骑行持久性的数据进行合并
static void lib_ride_persistent_data_merge(lib_ride_statistical_outputs_t *statistical, lib_gm_historical_data_t *historical)
{
    if (statistical != NULL && historical != NULL && statistical->VO2max != 0xffff) {
        lib_adaptability_fitness_outputs_t adaptability = p_lib_gm->adaptability_fitness;
        //将本次活动个人生理参数统计数据写入历史记录中
        adaptability.start_time -= adaptability.start_time % 86400U;
        if (historical->ride_num > 0) {
            //历史数据中已无效的数据数量
            uint8_t invalid_num = 0;

            //首先检查是否出现了来自“未来”的数据，这说明设备时间已经异常，则所有历史数据无效
            for (uint8_t i = 0; i < historical->ride_num; i++) {
                if (historical->ride_records[i].timestamp > adaptability.start_time) {
                    invalid_num = historical->ride_num;
                    break;
                }
            }

            if (invalid_num == 0) {
                //历史数据是正常的，则以当天为基准，从过去某个时刻开始算起的数据是仍有效、应当保留的；historical_start_time即是那个时刻
                const uint32_t historical_start_time = adaptability.start_time - (LIB_GM_HISTORICAL_MAX_NUM - 1) * 86400U;
                for (uint8_t i = 0; i < historical->ride_num; i++) {
                    if (historical->ride_records[i].timestamp < historical_start_time) {
                        invalid_num += 1;
                    }
                }
            }

            //剔除已无效的数据
            if (invalid_num > 0) {
                if (invalid_num < historical->ride_num) {
                    for (uint8_t i = invalid_num; i < historical->ride_num; i++) {
                        historical->ride_records[i - invalid_num] = historical->ride_records[i];
                    }
                    historical->ride_num -= invalid_num;
                } else {
                    historical->ride_num = 0;
                }
            }
        }

        lib_ride_historical_record_t *record = NULL;

        if (historical->ride_num > 0) {
            //现在检查本次统计数据应归入哪一天
            if (adaptability.start_time == historical->ride_records[historical->ride_num - 1].timestamp) {
                //本次活动所属当天已有统计数据，合并数据项
                record = historical->ride_records + historical->ride_num - 1;
            } else {
                record = historical->ride_records + historical->ride_num;
                historical->ride_num += 1;
            }
        } else {
            record = historical->ride_records;
            historical->ride_num += 1;
        }

        record->timestamp = adaptability.start_time;
        record->VO2max = statistical->VO2max;
        record->FTP = statistical->FTP;
        record->MAP = statistical->MAP;
    }
}

//跑步持久性的数据进行合并
static void lib_run_persistent_data_merge(lib_run_statistical_outputs_t *statistical, lib_gm_historical_data_t *historical)
{
    if (statistical != NULL && historical != NULL && statistical->VO2max != 0xffff) {
        lib_adaptability_fitness_outputs_t adaptability = p_lib_gm->adaptability_fitness;
        //将本次活动个人生理参数统计数据写入历史记录中
        adaptability.start_time -= adaptability.start_time % 86400U;
        if (historical->run_num > 0) {
            //历史数据中已无效的数据数量
            uint8_t invalid_num = 0;

            //首先检查是否出现了来自“未来”的数据，这说明设备时间已经异常，则所有历史数据无效
            for (uint8_t i = 0; i < historical->run_num; i++) {
                if (historical->run_records[i].timestamp > adaptability.start_time) {
                    invalid_num = historical->run_num;
                    break;
                }
            }

            if (invalid_num == 0) {
                //历史数据是正常的，则以当天为基准，从过去某个时刻开始算起的数据是仍有效、应当保留的；historical_start_time即是那个时刻
                const uint32_t historical_start_time = adaptability.start_time - (LIB_GM_HISTORICAL_MAX_NUM - 1) * 86400U;
                for (uint8_t i = 0; i < historical->run_num; i++) {
                    if (historical->run_records[i].timestamp < historical_start_time) {
                        invalid_num += 1;
                    }
                }
            }

            //剔除已无效的数据
            if (invalid_num > 0) {
                if (invalid_num < historical->run_num) {
                    for (uint8_t i = invalid_num; i < historical->run_num; i++) {
                        historical->run_records[i - invalid_num] = historical->run_records[i];
                    }
                    historical->run_num -= invalid_num;
                } else {
                    historical->run_num = 0;
                }
            }
        }

        lib_run_historical_record_t *record = NULL;

        if (historical->run_num > 0) {
            //现在检查本次统计数据应归入哪一天
            if (adaptability.start_time == historical->run_records[historical->run_num - 1].timestamp) {
                //本次活动所属当天已有统计数据，合并数据项
                record = historical->run_records + historical->run_num - 1;
            } else {
                record = historical->run_records + historical->run_num;
                historical->run_num += 1;
            }
        } else {
            record = historical->run_records;
            historical->run_num += 1;
        }

        record->timestamp = adaptability.start_time;
        record->VO2max = statistical->VO2max;
    }
}

static void lib_workout_persistent_data_merge(lib_gm_historical_data_t *historical)
{
    lib_adaptability_fitness_outputs_t adaptability = p_lib_gm->adaptability_fitness;
    if (historical != NULL && (adaptability.tlTrend != 0xff || adaptability.training_load != 0xffff)) {
        //将本次活动个人生理参数统计数据写入历史记录中
        adaptability.start_time -= adaptability.start_time % 86400U;
        if (historical->workout_num > 0) {
            //历史数据中已无效的数据数量
            uint8_t invalid_num = 0;

            //首先检查是否出现了来自“未来”的数据，这说明设备时间已经异常，则所有历史数据无效
            for (uint8_t i = 0; i < historical->workout_num; i++) {
                if (historical->workout_status[i].timestamp > adaptability.start_time) {
                    invalid_num = historical->workout_num;
                    break;
                }
            }

            if (invalid_num == 0) {
                //历史数据是正常的，则以当天为基准，从过去某个时刻开始算起的数据是仍有效、应当保留的；historical_start_time即是那个时刻
                const uint32_t historical_start_time = adaptability.start_time - (LIB_GM_HISTORICAL_MAX_NUM - 1) * 86400U;
                for (uint8_t i = 0; i < historical->workout_num; i++) {
                    if (historical->workout_status[i].timestamp < historical_start_time) {
                        invalid_num += 1;
                    }
                }
            }

            //剔除已无效的数据
            if (invalid_num > 0) {
                if (invalid_num < historical->workout_num) {
                    for (uint8_t i = invalid_num; i < historical->workout_num; i++) {
                        historical->workout_status[i - invalid_num] = historical->workout_status[i];
                    }
                    historical->workout_num -= invalid_num;
                } else {
                    historical->workout_num = 0;
                }
            }
        }

        historical_workout_status_t *record = NULL;

        if (historical->workout_num > 0) {
            //现在检查本次统计数据应归入哪一天
            if (adaptability.start_time == historical->workout_status[historical->workout_num - 1].timestamp) {
                //本次活动所属当天已有统计数据，合并数据项
                record = historical->workout_status + historical->workout_num - 1;
                record->trainingLoad += adaptability.training_load;
            } else {
                record = historical->workout_status + historical->workout_num;
                historical->workout_num += 1;
                record->trainingLoad = adaptability.training_load;
            }
        } else {
            record = historical->workout_status;
            historical->workout_num += 1;
            record->trainingLoad = adaptability.training_load;
        }
        record->timestamp = adaptability.start_time;
        record->trainingStatus = adaptability.training_status;
        record->tlTrend = adaptability.tlTrend;
    }
}

//骑行活动中多段累积数据更新
static void lib_gm_accumulated_update(lib_gm_accumulated_t *accumulated, const fitness_rtdata_t *indexIO)
{
    if (accumulated != NULL && indexIO != NULL)
    {
        if (indexIO->kcalOut >= 0.0f)
        {
            if (accumulated->kcal < 0.0f)
            {
                accumulated->kcal = 0.0f;
            }

            accumulated->kcal += indexIO->kcalOut;
        }

        if (indexIO->fatOut >= 0.0f)
        {
            if (accumulated->fatOut < 0.0f)
            {
                accumulated->fatOut = 0.0f;
            }

            accumulated->fatOut += indexIO->fatOut;
        }

        if (indexIO->carbOut >= 0.0f)
        {
            if (accumulated->carbOut < 0.0f)
            {
                accumulated->carbOut = 0.0f;
            }

            accumulated->carbOut += indexIO->carbOut;
        }

        if (indexIO->fitnessOut.fitness.stamina >= 0.0f)
        {
            if (accumulated->stamina_start < 0.0f)
            {
                accumulated->stamina_start = indexIO->fitnessOut.fitness.stamina;
            }

            if (accumulated->stamina_min < 0.0f)
            {
                accumulated->stamina_min = indexIO->fitnessOut.fitness.stamina;
            }

            if (indexIO->fitnessOut.fitness.stamina < accumulated->stamina_min)
            {
                accumulated->stamina_min = indexIO->fitnessOut.fitness.stamina;
            }
        }

        if (indexIO->fitnessOut.fitness.teAerobic > accumulated->teAerobic)
        {
            accumulated->teAerobic = indexIO->fitnessOut.fitness.teAerobic;
        }

        if (indexIO->fitnessOut.fitness.teAnaerobic > accumulated->teAnaerobic)
        {
            accumulated->teAnaerobic = indexIO->fitnessOut.fitness.teAnaerobic;
        }

        //训练负荷仅在pause时累积，这里无需处理

    }
}

void lib_sport_event_fitness_rt_update(const fitness_rtdata_t *p_rt_outputs)
{
    if (p_rt_outputs == NULL) {
        return;
    }
    lib_gm_accumulated_update(&p_lib_gm->accumulated, p_rt_outputs);

    if (p_rt_outputs->fitnessOut.fitness.stamina >= 0.0f)
    {
        p_lib_gm->rt_outputs.stamina = (uint8_t)p_rt_outputs->fitnessOut.fitness.stamina;
    }

    if (p_lib_gm->accumulated.stamina_start >= 0.0f)
    {
        p_lib_gm->rt_outputs.stamina_start = (uint8_t)p_lib_gm->accumulated.stamina_start;
    }

    if (p_lib_gm->accumulated.stamina_min >= 0.0f)
    {
        p_lib_gm->rt_outputs.stamina_min = (uint8_t)p_lib_gm->accumulated.stamina_min;
    }

    if (p_lib_gm->accumulated.teAerobic >= 0.0f)
    {
        p_lib_gm->rt_outputs.teAerobic = (uint8_t)(p_lib_gm->accumulated.teAerobic * 10.0f);
    }

    if (p_lib_gm->accumulated.teAnaerobic >= 0.0f)
    {
        p_lib_gm->rt_outputs.teAnaerobic = (uint8_t)(p_lib_gm->accumulated.teAnaerobic * 10.0f);
    }
    p_lib_gm->rt_outputs.trainingLoad = p_lib_gm->accumulated.trainingLoad + p_rt_outputs->fitnessOut.fitness.trainingLoad;
    p_lib_gm->rt_outputs.auto_pause_event = p_rt_outputs->fitnessNotifierGPS;
}

/**
 * @brief 设置自动采集开关
 *
 * @param enable 使能状态
 */
void service_sport_ability_auto_collect_set(bool enable)
{
    g_fitness_auto_enable = enable;
}

#ifndef SIMULATOR
#if (FITNESS_AUTO_ENABLE_WITH_SPORT == 1)
/**
 * @brief fitness数据采集开关随运动开启关闭
 *
 * @param sports_type  运动类型
 * @param enable 是否使能 true 使能采集 false 关闭采集
 */
static void service_sport_ability_collect_auto_proc(sport_type_t sports_type, bool enable)
{
    if (!g_fitness_auto_enable)
    {
        return;
    }
    DEVELOPER_MODEL_TYPE_E index = DEVELOPER_MODEL_MAX;
    switch (sports_type)
    {
        case EXERCISE_OUTDOOR_RUNNING:             // 跑步 操场跑步
        case EXERCISE_INDOOR_RUNNING:              // 室内跑步
        case EXERCISE_TRAIL_RUNNING:               // 越野跑
            index = DM_COLLECT_RUN;
            break;
        case EXERCISE_OUTDOOR_CYCLING:              // 骑行
        case EXERCISE_INDOOR_CYCLING:               // 室内骑行
            index = DM_COLLECT_CYCLE;
            break;
        case EXERCISE_OUTDOOR_WLAK:                  //步行
            index = DM_COLLECT_WALK;
            break;
        case EXERCISE_SWIMMING:                      // 游泳
            index = DM_COLLECT_SWIM;
            break;
        case EXERCISE_JUMP_ROPE:                     // 跳绳
            index = DM_COLLECT_JUMP_ROPE;
            break;
        case EXERCISE_DUMBBELL:                      // 力量训练
            index = DM_COLLECT_DUMBBELL;
            break;
        case EXERCISE_INDOOR_AEROBICS:               // 锻炼、有氧
            index = DM_COLLECT_OTHERS;
            break;
        case EXERCISE_ELLIPTICAL_MACHINE:            // 椭圆机
            index = DM_COLLECT_ELLIPTICAL;
            break;
        case EXERCISE_ROWING:                        // 划船机
            index = DM_COLLECT_ROWING;
            break;
        case EXERCISE_MOUNTAINEERING_HIKING:         // 登山、徒步
            index = DM_COLLECT_MOUNTAIN_CLIMBING;
            break;
        case EXERCISE_SKIING:                        // 滑雪
            index = DM_COLLECT_SKI;
            break;
        default:
            return;
    }
    developer_mode_run_cb(index, enable);
}
#endif
#endif

//TEST SWIM
#if 1 //__SWIM_LOG
#include "service_datetime.h"
extern QW_FIL *g_fp_swim;
#endif

/**
 * @brief 运动结束fitness出值 业务数据保存
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_sport_fitness_in_callback(const void *in, uint32_t len)
{
    const algo_fitness_pub_t* fitness = (algo_fitness_pub_t *)in;
    if (fitness->data_type == FITNESS_SUMMARY_DATA) {
        const fitness_summary_t* p_fitness = &fitness->data.summary;
        RUN_ABIL_LOG_I("--------------algo_sport_fitness_in_callback----FITNESS_SUMMARY_DATA----");
        //lib_gm_statistical_outputs_update(&p_lib_gm->ride_statistical, p_fitness);
        /*统计骑行和跑步结束 运动能力数据*/
        if (p_lib_gm->sport_type == EXERCISE_OUTDOOR_CYCLING || p_lib_gm->sport_type == EXERCISE_INDOOR_CYCLING) {
            lib_ride_statistical_outputs_update(&p_lib_gm->ride_statistical, p_fitness);
        } else if (p_lib_gm->sport_type == EXERCISE_OUTDOOR_RUNNING || p_lib_gm->sport_type == EXERCISE_INDOOR_RUNNING) {
            lib_run_statistical_outputs_update(&p_lib_gm->run_statistical, p_fitness);
        }

        /*把单次数据合并到历史数据中*/
        if (p_lib_gm->sport_type == EXERCISE_OUTDOOR_CYCLING || p_lib_gm->sport_type == EXERCISE_INDOOR_CYCLING) {
            lib_ride_persistent_data_merge(&p_lib_gm->ride_statistical, &p_lib_gm->historical);
        } else if (p_lib_gm->sport_type == EXERCISE_OUTDOOR_RUNNING || p_lib_gm->sport_type == EXERCISE_INDOOR_RUNNING) {
            lib_run_persistent_data_merge(&p_lib_gm->run_statistical, &p_lib_gm->historical);
        }


        // 训练适应性数据更新
        p_lib_gm->adaptability_fitness.training_status = p_fitness->trainingStatus[0];
        p_lib_gm->adaptability_fitness.training_load = p_lib_gm->rt_outputs.trainingLoad;
        p_lib_gm->adaptability_fitness.stamina = p_lib_gm->rt_outputs.stamina;
        if (p_fitness->tlTrend[0] >= 0.0f) {
            p_lib_gm->adaptability_fitness.tlTrend = (uint8_t)(p_fitness->tlTrend[0] * 10.0f);
        }
        p_lib_gm->adaptability_fitness.recovery_time = (int32_t)(p_fitness->recoveryTime[0]);
        p_lib_gm->adaptability_fitness.end_time = service_datetime_get_gmt_time();
        RUN_ABIL_LOG_I("sprot end  recovery_time =%d training_status=%d training_load=%d tlTrend=%d, stamina=%d\n",
            p_lib_gm->adaptability_fitness.recovery_time,
            p_lib_gm->adaptability_fitness.training_status,
            p_lib_gm->adaptability_fitness.training_load,
            p_lib_gm->adaptability_fitness.tlTrend,
            p_lib_gm->adaptability_fitness.stamina);
        lib_workout_persistent_data_merge(&p_lib_gm->historical);
        //运动实时数据
        p_lib_gm->summary.teAerobic = p_lib_gm->rt_outputs.teAerobic;
        p_lib_gm->summary.teAnaerobic = p_lib_gm->rt_outputs.teAnaerobic;
        p_lib_gm->summary.trainingLoad = p_lib_gm->rt_outputs.trainingLoad;
        if (p_lib_gm->accumulated.stamina_start >= 0.0f)
        {
            p_lib_gm->summary.stamina_start = (uint8_t)p_lib_gm->accumulated.stamina_start;
        }

        p_lib_gm->summary.stamina_end = p_lib_gm->rt_outputs.stamina;

        if (p_lib_gm->accumulated.stamina_min >= 0.0f)
        {
            p_lib_gm->summary.stamina_min = (uint8_t)p_lib_gm->accumulated.stamina_min;
        }

        if (p_lib_gm->accumulated.kcal > 0.0f && p_lib_gm->accumulated.fatOut >= 0.0f && p_lib_gm->accumulated.carbOut >= 0.0f)
        {
            p_lib_gm->summary.fatPct = (uint8_t)(p_lib_gm->accumulated.fatOut / p_lib_gm->accumulated.kcal * 100.0f);
            p_lib_gm->summary.carbPct = 100 - p_lib_gm->summary.fatPct;
        }

        uint8_t data = 0;
        if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_FITNESS_SAVE_END, &data, sizeof(data)) != ERRO_CODE_OK)
        {
            RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_FITNESS_SAVE_END error");
        }

        RUN_ABIL_LOG_I("sprot end::sport end fatPct= %d carbPct= %d stamina_start=%d stamina_min=%d teAerobic=%d teAnaerobic=%d trainingLoad=%d\n", p_lib_gm->summary.fatPct,p_lib_gm->summary.carbPct,p_lib_gm->summary.stamina_start,p_lib_gm->summary.stamina_min,
            p_lib_gm->summary.teAerobic,p_lib_gm->summary.teAnaerobic,p_lib_gm->summary.trainingLoad);
        // 判断数据有效性
        thread_pool_add_task(lib_gm_persistent_data_save, NULL, NULL, osPriorityBelowNormal2);
        lib_gm_rt_outputs_init(&p_lib_gm->rt_outputs);
        thread_pool_add_task(db_fitness_report_save, NULL, NULL, osPriorityBelowNormal2);
        //取消订阅 CONFIG_QW_EVENT_NAME_SPORT_STATUS需要换
        qw_dataserver_unsubscribe(CONFIG_QW_ALG_NAME_FITNESS, algo_sport_fitness_in_callback);
#ifndef SIMULATOR
#if (FITNESS_AUTO_ENABLE_WITH_SPORT == 1)
        //  随运动关闭fitness数据采集
        service_sport_ability_collect_auto_proc(p_lib_gm->sport_type, false);
#endif
#endif
    }
    else if (fitness->data_type == FITNESS_REAKTIME_DATA)
    {
        if (fitness->data.rt_data.fitnessNotifierGPS != last_fitnessNotifierGPS &&
            fitness->data.rt_data.fitnessNotifierGPS > 0) {
            last_fitnessNotifierGPS = fitness->data.rt_data.fitnessNotifierGPS;
            algo_move_status_drv_pub_t data = {0};
            data.move_status_drv = last_fitnessNotifierGPS - 1;
            if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_MOVE_STATUS_DRV, &data, sizeof(algo_move_status_drv_pub_t)) != ERRO_CODE_OK) {
                RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_MOVE_STATUS_DRV error");
            }
            RUN_ABIL_LOG_I("algo_sport_fitness_in_callback fitnessNotifierGPS=%u", fitness->data.rt_data.fitnessNotifierGPS);
        }
        if (p_lib_gm->sport_status == SPORT_START) {
            RUN_ABIL_LOG_I("start fitness algo subscribe--------sport_type=%d,atuo_control_swich=%d", p_lib_gm->sport_type,get_sport_auto_pause_en(sport_start_type));
            p_lib_gm->sport_status = SPORT_ING;
            notify_algo_sport remind;
            remind.sport_status = SPORT_ING;
            remind.sport_type = p_lib_gm->sport_type;
            remind.atuo_control_swich = get_sport_auto_pause_en(sport_start_type);
            int auto_pause_type = get_sport_auto_pause_type(sport_start_type);
            if (auto_pause_type == STILLNESS) {
                remind.min_pause_speed = 0;
            } else if (auto_pause_type == CONF_SPEED) {
                remind.min_pause_speed = 3600.0f / get_sport_auto_pause_min_value(sport_start_type);
            } else if (auto_pause_type == SPEED_LESS) {
                remind.min_pause_speed = get_sport_auto_pause_min_value(sport_start_type);
            }
            if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK) {
                RUN_ABIL_LOG_E("publish CONFIG_QW_EVENT_FITNESS_EVENT error");
            }
        }
        const struct UpdateRunOutput *p_run = NULL;
        const struct UpdateJumpRopeOutput *p_jump = NULL;
        const struct UpdateSwimOutput *p_swim = NULL;
        if (p_lib_gm->sport_type == EXERCISE_OUTDOOR_RUNNING || p_lib_gm->sport_type == EXERCISE_INDOOR_RUNNING ||
            p_lib_gm->sport_type == EXERCISE_MOUNTAINEERING_HIKING || p_lib_gm->sport_type == EXERCISE_OUTDOOR_WLAK) {
            p_run =&fitness->data.rt_data.fitnessOut.workout.run;
        } else if (p_lib_gm->sport_type == EXERCISE_JUMP_ROPE) {
            p_jump = &fitness->data.rt_data.fitnessOut.workout.jumpRope;
        } else if (p_lib_gm->sport_type == EXERCISE_SWIMMING) {
            p_swim = &fitness->data.rt_data.fitnessOut.workout.swim;
        }

        //运动实时数据处理

        const fitness_rtdata_t *rt_data = &fitness->data.rt_data;
        lib_sport_event_fitness_rt_update(rt_data);

        algo_kcal_pub_t kcal_info = {0};
        kcal_info.calories_delta = rt_data->activityKcalOut < 0 ? 0xffff : (uint16_t)(rt_data->activityKcalOut * 1000);
        kcal_info.fat_delta = rt_data->fatOut < 0 ? 0xffff : (uint16_t)(rt_data->fatOut * 1000);
        kcal_info.carb_delta = rt_data->carbOut < 0 ? 0xffff : (uint16_t)(rt_data->carbOut * 1000);
        if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_CALORIES_DRV, &kcal_info, sizeof(algo_kcal_pub_t)) != ERRO_CODE_OK)
        {
            RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_CALORIES_DRV error");
        }

        if (NULL != p_run)
        {
            algo_rd_drv_pub_t drv_rd;
            drv_rd.vertical_oscillation = p_run->verticalOscillation < 0 ? 0xffff : (uint16_t)(p_run->verticalOscillation * 10);
            drv_rd.ground_contact_time = p_run->stance < 0 ? 0xffff : (uint16_t)(p_run->stance * 10);
            drv_rd.stance_time_percent = (p_run->stance < 0 || p_run->flight < 0) ? 0xffff : (uint16_t)(p_run->stance * 10000 / (p_run->stance + p_run->flight));
            drv_rd.ground_contact_balance = p_run->staBalance < 0 ? 0xffff : (uint16_t)(p_run->staBalance * 100);
            drv_rd.vertical_ratio = (p_run->verticalOscillation < 0 || p_run->stepLen < 0) ? 0xffff : (uint16_t)(p_run->verticalOscillation * 10 / p_run->stepLen);
            drv_rd.step_length = p_run->stepLen < 0 ? 0xffff : (uint16_t)(p_run->stepLen * 1000);
            drv_rd.run_power = p_run->runPowerWatt < 0 ? 0xffff : (uint16_t)p_run->runPowerWatt;

            if (p_lib_gm->sport_type == EXERCISE_MOUNTAINEERING_HIKING || p_lib_gm->sport_type == EXERCISE_OUTDOOR_WLAK) {
                p_lib_gm->accumulated.step_count += rt_data->stepCountOut;
                drv_rd.step_count =  (uint8_t)((int)p_lib_gm->accumulated.step_count % 128);
                drv_rd.cadence = 0xff == rt_data->cadenceOut ? 0xffff : (uint16_t)rt_data->cadenceOut;
            } else {
                drv_rd.step_count = p_run->stepCnt < 0 ? 0xff : (uint8_t)(p_run->stepCnt % 128);
                drv_rd.cadence = p_run->cadence < 0 ? 0xffff : (uint16_t)p_run->cadence;
            }

            // RUN_ABIL_LOG_E("CONFIG_QW_ALG_NAME_RD_DRV cadence=%f, stepLen=%f\n", p_run->cadence, p_run->stepLen);

            if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_RD_DRV, &drv_rd, sizeof(algo_rd_drv_pub_t)) != ERRO_CODE_OK)
            {
                RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_RD_DRV error");
            }
        }
        else if (NULL != p_jump)
        {
            algo_jumprope_drv_pub_t drv_jr;
            drv_jr.count = p_jump->totalCount < 0 ? UINT16_MAX : p_jump->totalCount;
            drv_jr.trip = p_jump->tripCount < 0 ? UINT16_MAX : p_jump->tripCount;
            drv_jr.combo = p_jump->sessionCount < 0 ? UINT16_MAX : p_jump->sessionCount;

            // RUN_ABIL_LOG_E("CONFIG_QW_ALG_NAME_JUMPROPE_DRV count=%d, trip=%d, combo=%d\n", p_jump->totalCount, p_jump->tripCount, p_jump->sessionCount);

            if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_JUMPROPE_DRV, &drv_jr, sizeof(algo_jumprope_drv_pub_t)) != ERRO_CODE_OK)
            {
                RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_JUMPROPE_DRV error");
            }
        }
        else if (NULL != p_swim)
        {
//TEST_SWIM
#if 1 //__SWIM_LOG
            uint32_t bytes;
            pool_swim_drv_pub_t pool_swim_drv;
            if (NULL != g_fp_swim)
            {
                pool_swim_drv.timestamp = service_datetime_get_fit_time();
                pool_swim_drv.startIndex = p_swim->startIndex;
                pool_swim_drv.endIndex = p_swim->endIndex;
                pool_swim_drv.realtimeLapCnt = p_swim->realtimeLapCnt;
                pool_swim_drv.cntStroke = p_swim->cntStroke;
                pool_swim_drv.updateType = p_swim->updateType;
                pool_swim_drv.frontcrawlPerc = p_swim->frontcrawlPerc;
                pool_swim_drv.backstrokePerc = p_swim->backstrokePerc;
                pool_swim_drv.breaststrokePerc = p_swim->breaststrokePerc;
                pool_swim_drv.butterflyPerc = p_swim->butterflyPerc;
                pool_swim_drv.updateStatus = p_swim->updateStatus;
                qw_f_write(g_fp_swim, &pool_swim_drv, sizeof(pool_swim_drv_pub_t), &bytes);

                if (qw_dataserver_publish(CONFIG_QW_ALG_NAME_POOL_SWIM_DRV, &pool_swim_drv, sizeof(pool_swim_drv_pub_t)) != ERRO_CODE_OK)
                {
                    RUN_ABIL_LOG_E("publish CONFIG_QW_ALG_NAME_POOL_SWIM_DRV error");
                }
            }
#endif
        }
    }
}

//设备上电后调用，预初始化，主要是加载个人生理参数用于展示，仅在记录log场景下使用
int lib_gm_pre_init(void)
{
    lib_run_statistical_outputs_init(&p_lib_gm->run_statistical);
    lib_ride_statistical_outputs_init(&p_lib_gm->ride_statistical);
    lib_adaptability_statistical_outputs_init(&p_lib_gm->adaptability_fitness);
    lib_gm_historical_data_init(&p_lib_gm->historical);

    return 0;
}

//开始运动调用
int lib_gm_start(FIT_SPORTS sports_type)
{
    if (p_lib_gm->sport_status != SPORT_END && p_lib_gm->sport_status != SPORT_INVALID) {
        RUN_ABIL_LOG_E("lib_gm_start sport_status error =%d",p_lib_gm->sport_status);
        return -1;
    }

    const uint32_t time_now  = service_datetime_get_gmt_time();
    p_lib_gm->last_time = time_now;
    p_lib_gm->adaptability_fitness.start_time = time_now;

    //开启算法输出订阅
    optional_config_t config = {.sampling_rate = 0};
    int value = 0;
    uint16_t lib_type = 0;

    switch (sports_type)
    {
        case FIT_SPORTS_TREADMILL:              //跑步机
            config.value = EXERCISE_INDOOR_RUNNING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_INDOOR_RUNNING;
            break;
        case FIT_SPORTS_RUNNING:                //跑步
        case FIT_SPORTS_PLAYGROUND:             //操场跑步
        case FIT_SPORTS_TRAIL_RUNNING:          //越野跑
            config.value = EXERCISE_OUTDOOR_RUNNING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_OUTDOOR_RUNNING;
            break;
        case FIT_SPORTS_CYCLING:                //骑行
        case FIT_SPORTS_ROAD_CYCLING:           //公路骑行
        case FIT_SPORTS_COMMUTING:              //通勤骑行
        case FIT_SPORTS_TRIP_CYCLING:           //长途骑行
        case FIT_SPORTS_MOUNTAIN_CYCLING:       //山地骑行
            config.value = EXERCISE_OUTDOOR_CYCLING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_OUTDOOR_CYCLING;
            break;
        case FIT_SPORTS_INDOOR_CYCLING:         //室内自行车
            config.value = EXERCISE_INDOOR_CYCLING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_INDOOR_CYCLING;
            break;
        case FIT_SPORTS_WALKING:                //步行
        case FIT_SPORTS_INDOOR_RUNNING:         //室内步行
            config.value = EXERCISE_OUTDOOR_WLAK << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_OUTDOOR_WLAK;
            break;
        case FIT_SPORTS_POOL_SWIMMING:          //室内游泳
            value = (int)get_sport_swim_value();
            config.value = EXERCISE_SWIMMING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_SWIMMING;
            break;
        case FIT_SPORTS_OPEN_WATER_SWIMMING:    //室外游泳
            config.value = EXERCISE_SWIMMING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_SWIMMING;
            break;
        case FIT_SPORTS_JUMP_ROPE:              //跳绳
            config.value = EXERCISE_JUMP_ROPE << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_JUMP_ROPE;
            break;
        case FIT_SPORTS_STRENGTH_TRAINING:      //力量训练
            config.value = EXERCISE_DUMBBELL << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_DUMBBELL;
            break;
        case FIT_SPORTS_INDOOR_AEROBIC:         //室内有氧
        case FIT_SPORTS_OUTDOOR_AEROBIC:        //户外有氧
        case FIT_SPORTS_EXERCISE:               //锻炼
            config.value = EXERCISE_INDOOR_AEROBICS << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_INDOOR_AEROBICS;
            break;
        case FIT_SPORTS_ELLIPTICAL_MACHINE:     //椭圆机
            config.value = EXERCISE_ELLIPTICAL_MACHINE << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_ELLIPTICAL_MACHINE;
            break;
        case FIT_SPORTS_ROWING_MACHINE:         //划船机
            config.value = EXERCISE_ROWING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_ROWING;
            break;
        case FIT_SPORTS_MOUNTAINEERING:         //登山
        case FIT_SPORTS_HIKING:                 //徒步
            config.value = EXERCISE_MOUNTAINEERING_HIKING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_MOUNTAINEERING_HIKING;
            break;
        case FIT_SPORTS_SKIING:                 //滑雪
            config.value = EXERCISE_SKIING << 16 | (value & 0xffff);
            p_lib_gm->sport_type = EXERCISE_SKIING;
            break;
        // case FIT_SPORTS_TRIATHLON:           //铁人三项
        //     break;
        // case FIT_SPORTS_COMPOUND_MOTION:     //复合运动
        //     break;
        //
        //     break;
        default:
            RUN_ABIL_LOG_E("start fitness sport type error%d",sports_type);
            sport_start_type = 0;
            return -1;
            break;
    }
    sport_start_type = sports_type;
#ifndef SIMULATOR
#if (FITNESS_AUTO_ENABLE_WITH_SPORT == 1)
    // 随运动启动打开fitness数据采集
    service_sport_ability_collect_auto_proc(p_lib_gm->sport_type, true);
#endif
#endif
    if(qw_dataserver_subscribe(CONFIG_QW_ALG_NAME_FITNESS, algo_sport_fitness_in_callback, &config) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("subscribe CONFIG_QW_ALG_NAME_FITNESS  error");
        return -1;
    }
    //通知算法开始运动 运动类型
    notify_algo_sport remind;
    remind.sport_status = SPORT_START;
    remind.sport_type = p_lib_gm->sport_type;
    if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("publish CONFIG_QW_EVENT_FITNESS_EVENT error");
        //return -1;
    }
    RUN_ABIL_LOG_I("start fitness algo subscribe--------");

    lib_gm_rt_outputs_init(&p_lib_gm->rt_outputs);
    lib_gm_summary_outputs_init(&p_lib_gm->summary);
    lib_gm_accumulated_init(&p_lib_gm->accumulated);

    p_lib_gm->sport_status = SPORT_START;

    return 0;
}

//暂停运动时调用
int lib_gm_pause()
{
    if (p_lib_gm->sport_status != SPORT_ING && p_lib_gm->sport_status != SPORT_START) {
        RUN_ABIL_LOG_E("error pause fitness algo subscribe----sport_status=%d",p_lib_gm->sport_status);
        return -1;
    }
    RUN_ABIL_LOG_I("pause fitness algo subscribe--------sport_type=%d,sport_status=%d", p_lib_gm->sport_type,p_lib_gm->sport_status);
    notify_algo_sport remind;
    remind.sport_status = SPORT_PAUSED;
    remind.sport_type = p_lib_gm->sport_type;
    remind.atuo_control_swich = get_sport_auto_pause_en(sport_start_type);
    int auto_pause_type = get_sport_auto_pause_type(sport_start_type);
    if (auto_pause_type == STILLNESS) {
        remind.min_pause_speed = 0;
    } else if (auto_pause_type == CONF_SPEED) {
        remind.min_pause_speed = 3600.0f / get_sport_auto_pause_min_value(sport_start_type);
    } else if (auto_pause_type == SPEED_LESS) {
        remind.min_pause_speed = get_sport_auto_pause_min_value(sport_start_type);
    }
    if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("publish CONFIG_QW_EVENT_FITNESS_EVENT error");
        //return -1;
    }
    p_lib_gm->sport_status = SPORT_PAUSED;
    return 0;
}

//继续运动时调用
int lib_gm_continue()
{
    if (p_lib_gm->sport_status != SPORT_PAUSED) {
        RUN_ABIL_LOG_E("error continue fitness algo subscribe----sport_status=%d",p_lib_gm->sport_status);
        return -1;
    }

    notify_algo_sport remind;
    remind.sport_status = SPORT_CONTINUE;
    remind.sport_type = p_lib_gm->sport_type;
    remind.atuo_control_swich = get_sport_auto_pause_en(sport_start_type);
    int auto_pause_type = get_sport_auto_pause_type(sport_start_type);
    if (auto_pause_type == STILLNESS) {
        remind.min_pause_speed = 0;
    } else if (auto_pause_type == CONF_SPEED) {
        remind.min_pause_speed = 3600.0f / get_sport_auto_pause_min_value(sport_start_type);
    } else if (auto_pause_type == SPEED_LESS) {
        remind.min_pause_speed = get_sport_auto_pause_min_value(sport_start_type);
    }
    RUN_ABIL_LOG_I("continue fitness algo subscribe--------sport_type=%d,atuo_control_swich=%f", p_lib_gm->sport_type,remind.min_pause_speed);
    if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("publish CONFIG_QW_EVENT_FITNESS_EVENT error");
        //return -1;
    }
    p_lib_gm->sport_status = SPORT_ING;

    return 0;
}

//结束活动时调用，完成计算
int lib_gm_end(FIT_SPORTS sports_type)
{
    if (p_lib_gm->sport_status != SPORT_ING && p_lib_gm->sport_status != SPORT_PAUSED)
    {
        RUN_ABIL_LOG_I("error end fitness algo subscribe----sport_status=%d",p_lib_gm->sport_status);
        return -1;
    }
    RUN_ABIL_LOG_I("end fitness algo subscribe----sport_status=%d",p_lib_gm->sport_status);
    //通知算法结束运动 运动类型
    if (sports_type != sport_start_type) {
        RUN_ABIL_LOG_E("error end fitness algo lib_gm_end----sports_type=%d sport_start_type=%d", sports_type, sport_start_type);
        return -1;
    }
    notify_algo_sport remind;
    remind.sport_status = SPORT_END;
    remind.sport_type = p_lib_gm->sport_type;
    remind.atuo_control_swich = get_sport_auto_pause_en(sport_start_type);
    int auto_pause_type = get_sport_auto_pause_type(sport_start_type);
    if (auto_pause_type == STILLNESS) {
        remind.min_pause_speed = 0;
    } else if (auto_pause_type == CONF_SPEED) {
        remind.min_pause_speed = 3600.0f / get_sport_auto_pause_min_value(sport_start_type);
    } else if (auto_pause_type == SPEED_LESS) {
        remind.min_pause_speed = get_sport_auto_pause_min_value(sport_start_type);
    }

    if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK)
    {
        RUN_ABIL_LOG_E("[service_sport_ability] @%s@ start_sport publish error", __FUNCTION__);
        return -1;
    }
    p_lib_gm->sport_status = SPORT_END;
    sport_start_type = 0;

    return 0;
}

//放弃活动时调用，中止算法模块的执行
int lib_gm_terminate(void)
{
    if (p_lib_gm->sport_status != SPORT_ING) {
        return -1;
    }

    //通知算法开始运动 运动类型
    notify_algo_sport remind;
    remind.sport_status = SPORT_CANCEL;
    remind.sport_type = EXERCISE_OUTDOOR_CYCLING;
    if (qw_dataserver_publish(CONFIG_QW_EVENT_FITNESS_EVENT, &remind, sizeof(notify_algo_sport)) != ERRO_CODE_OK) {
        RUN_ABIL_LOG_E("[service_sport_ability] @%s@ start_sport publish error", __FUNCTION__);
        return -1;
    }
    //需要先传入&p_lib_gm->cache 再输出 &p_lib_gm->cache
    //if (LIB_GM_FITNESS_DATA_save() != 0) {
        //return -1;
    //}
    //需要算法输出一次值 rt_outputs
    lib_gm_rt_outputs_init(&p_lib_gm->rt_outputs);

    p_lib_gm->sport_status = SPORT_CANCEL;
    return 0;
}

//关机前调用，退出算法模块，并进行一些清理工作
int lib_gm_exit(void)
{
    if (p_lib_gm->sport_status != SPORT_START && p_lib_gm->sport_status != SPORT_PAUSED) {
        return -1;
    }

    //此处不校验返回值，即使失败，也必须向下执行
    //LIB_GM_FITNESS_DATA_save();

    MY_FREE(p_lib_gm->cache.p_sdk_mem);
    MY_FREE(p_lib_gm->cache.p_previoud_data);

    p_lib_gm->sport_status = SPORT_INVALID;

    return 0;
}

//骑行活动中获取实时输出，仅当算法执行中时可以获取
lib_gm_rt_outputs_t *lib_gm_rt_outputs_get(void)
{
    return &p_lib_gm->rt_outputs;
}

//活动结束时获取summary数据
lib_gm_summary_outputs_t *lib_gm_summary_outputs_get(void)
{
    return &p_lib_gm->summary;
}

//骑行活动结束时获取个人生理统计数据
lib_ride_statistical_outputs_t *lib_ride_statistical_outputs_get(void)
{
    return &p_lib_gm->ride_statistical;
}

//跑步活动结束时获取个人生理统计数据
lib_run_statistical_outputs_t *lib_run_statistical_outputs_get(void)
{
    return &p_lib_gm->run_statistical;
}

//获取个人生理历史数据
lib_gm_historical_data_t *lib_gm_historical_data_get(void)
{
    return &p_lib_gm->historical;
}

//活动结束时获取运动适应性数据
lib_adaptability_fitness_outputs_t *lib_adaptability_statistical_outputs_get(void)
{
    return &p_lib_gm->adaptability_fitness;
}

//fitness历史数据存文件
bool db_fitness_report_save()
{
    UINT byte = 0;
    QW_FIL* fp = NULL;
    char *p_data = get_algo_share_mem();
    uint32_t header = 0;
    int size = 0;
    if (p_data == NULL) {
        RUN_ABIL_LOG_E("fitness report save p_data failed");
        return false;
    }
    memcpy(&size, p_data, sizeof(size));
    memcpy(&header, p_data+4, sizeof(header));
    if (header > 2048) {
        RUN_ABIL_LOG_E("fitness report save header failed");
        return false;
    }
    if (qw_f_open(&fp, LIB_GM_FITNESS_DATA, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK) {
        RUN_ABIL_LOG_E("open report save LIB_GM_PERSISTENT_DATA failed");
        return false;
    }
    if (qw_f_write(fp, &size, sizeof(size), &byte) != QW_OK || byte != sizeof(size)) {
        RUN_ABIL_LOG_E("fp db LIB_GM_FITNESS_DATA write size error");
        qw_f_close(fp);
        return false;
    }
    if (qw_f_write(fp, &header, sizeof(header), &byte) != QW_OK || byte != sizeof(header)) {
        RUN_ABIL_LOG_E("fp db LIB_GM_FITNESS_DATA write head error");
        qw_f_close(fp);
        return false;
    }
    if (qw_f_write(fp, p_data+8, header, &byte) != QW_OK || byte != header) {
        RUN_ABIL_LOG_E("fp db LIB_GM_FITNESS_DATA write date error");
        qw_f_close(fp);
        return false;
    }
    qw_f_close(fp);
    return true;
}

#include <finsh.h>

static void service_run_ability(int argc, char* argv[])
{
    if (argc < 2) {
        return;
    }
    if (strcmp(argv[1], "test1") == 0) {
        p_lib_gm->run_statistical.power_ability = 101;
        p_lib_gm->run_statistical.anaerobic_ability = 118;
        p_lib_gm->run_statistical.aerobic_power_ability = 331;
        p_lib_gm->run_statistical.aerobic_abiltiy = 791;
        p_lib_gm->run_statistical.ultra_endurance_ability = 791;
        p_lib_gm->run_statistical.endurance_abiltiy = 451;
        p_lib_gm->run_statistical.stamina_level = 660;
        p_lib_gm->adaptability_fitness.training_status = 3;
        lib_gm_persistent_data_save();
    } else if (strcmp(argv[1], "test2") == 0) {
        p_lib_gm->run_statistical.power_ability = 0;
        p_lib_gm->run_statistical.anaerobic_ability = 0;
        p_lib_gm->run_statistical.aerobic_power_ability = 0;
        p_lib_gm->run_statistical.aerobic_abiltiy = 0;
        p_lib_gm->run_statistical.ultra_endurance_ability = 0;
        p_lib_gm->run_statistical.endurance_abiltiy = 0;
        p_lib_gm->run_statistical.stamina_level = 0;
        p_lib_gm->adaptability_fitness.training_status = 0;
        lib_gm_persistent_data_load();
    } else if (strcmp(argv[1], "test3") == 0) {
        rt_kprintf("yangzhao::LOAD stamina =%d\n", p_lib_gm->adaptability_fitness.stamina);
        rt_kprintf("yangzhao::LOAD tlTrend =%d\n", p_lib_gm->adaptability_fitness.tlTrend);
        rt_kprintf("yangzhao::LOAD recovery_time =%d\n", p_lib_gm->adaptability_fitness.recovery_time);
        rt_kprintf("yangzhao::LOAD training_status =%d\n", p_lib_gm->adaptability_fitness.training_status);
        rt_kprintf("yangzhao::LOAD ride_statistical.FTP =%d\n", p_lib_gm->ride_statistical.FTP);
    } else if (strcmp(argv[1], "test4") == 0) {
        rt_kprintf("yangzhao::LOAD run_num =%d\n", p_lib_gm->historical.run_num);
        for (int i = 0; i < 28; i++) {
            if (p_lib_gm->historical.run_records[i].VO2max) {
                rt_kprintf(" i=%d VO2max=%d timestamp=%d",i,p_lib_gm->historical.run_records[i].VO2max,p_lib_gm->historical.run_records[i].timestamp);
            }

        }
        rt_kprintf("yangzhao::LOAD run_num end =\n");
    } else if (strcmp(argv[1], "test5") == 0) {
        p_lib_gm->historical.run_records[0].VO2max = 200;
        p_lib_gm->historical.run_records[0].timestamp = 1740009600;
        p_lib_gm->historical.run_records[1].VO2max = 210;
        p_lib_gm->historical.run_records[1].timestamp = 1740096000;
        p_lib_gm->historical.run_records[2].VO2max = 220;
        p_lib_gm->historical.run_records[2].timestamp = 1740355200;
        p_lib_gm->historical.run_records[3].VO2max = 630;
        p_lib_gm->historical.run_records[3].timestamp = 1740528000;
        p_lib_gm->historical.run_records[4].VO2max = 640;
        p_lib_gm->historical.run_records[4].timestamp = 1740700800;
        p_lib_gm->historical.run_num = 5;
    }
}

MSH_CMD_EXPORT(service_run_ability, daily_test);


