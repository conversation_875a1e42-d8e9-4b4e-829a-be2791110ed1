/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwHalfScreen.cpp
@Time    :   2024/12/23 19:32:39
*
**************************************************************************/

#include "QwHalfScreen.h"
#include <new>

QwHalfScreen::QwHalfScreen()
{
    high_contaner_ = nullptr;
    memset(static_cast<void*>(&half_screen_union_), 0, sizeof(half_screen_union_));
    title_font_ = nullptr;
    text_font_ = nullptr;
    sub_text_wide_text_action_ = WIDE_TEXT_WORDWRAP_DOT;
}

QwHalfScreen::~QwHalfScreen()
{
    if(high_contaner_ != nullptr)
    {
        high_contaner_->~Container();
        high_contaner_ = nullptr;
        memset(static_cast<void*>(&half_screen_union_), 0, sizeof(half_screen_union_));
    }
}

/**
 * @brief 生成提示框
 */
void QwHalfScreen::setup()
{
    setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);

    add(high_bg_);
    add(low_bg_);
    add(sub_title_);
    add(sub_text_);
}

/**
 * @brief 刷新
 */
void QwHalfScreen::on_notify()
{
    high_bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT/2);
    high_bg_.setColor(lv_color_hex(0x333333));
    low_bg_.setPosition(0, HAL::DISPLAY_HEIGHT/2, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT/2);
    low_bg_.setColor(lv_color_hex(0x000000));
    low_bg_.setAlpha(LV_OPA_80);

    int offset = 56;
    if(sub_title_.isVisible())
    {
        offset = 24;
        if(sub_title_type_ == QHSTT_NUMBER)
        {
            sub_title_.setWidthHeight(386, 48);
		    sub_title_.setTextFont(&NUMBER_NO_48_FONT);
		    sub_title_.setColor(lv_color_hex(0xFFFFFF));
		    sub_title_.setTextAlignment(CENTER);
		    sub_title_.setTextVerticalAlignment(ALIGN_Y_MID);
		    sub_title_.setLabelAlpha(LV_OPA_TRANSP);
		    sub_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		    sub_title_.resizeHeightToCurrentText();
        }
        else if(sub_title_type_ == QHSTT_TEXT)
        {
            sub_title_.setWidthHeight(386, PUBLIC_NO_32_M_FONT.line_height);
            if(title_font_ != nullptr)
            {
                sub_title_.setTextFont(title_font_);
            }
            else
            {
                sub_title_.setTextFont(&PUBLIC_NO_32_M_FONT);
            }
		    sub_title_.setColor(lv_color_hex(0xFFFFFF));
		    sub_title_.setTextAlignment(CENTER);
		    sub_title_.setTextVerticalAlignment(ALIGN_Y_MID);
		    sub_title_.setLabelAlpha(LV_OPA_TRANSP);
		    sub_title_.setWideTextAction(WIDE_TEXT_WORDWRAP_DOT);
		    sub_title_.resizeHeightToCurrentText();
        }
        
        sub_text_.setWidthHeight(386, PUBLIC_NO_32_R_FONT.line_height);
        if(text_font_ != nullptr)
        {
            sub_text_.setTextFont(text_font_);
        }
        else
        {
            sub_text_.setTextFont(&PUBLIC_NO_32_R_FONT);
        }
		sub_text_.setColor(lv_color_hex(0xFFFFFF));
        sub_text_.setAlpha(LV_OPA_60);
		sub_text_.setTextAlignment(CENTER);
		sub_text_.setTextVerticalAlignment(ALIGN_Y_MID);
		sub_text_.setLabelAlpha(LV_OPA_TRANSP);
		sub_text_.setWideTextAction(sub_text_wide_text_action_);
		sub_text_.resizeHeightToCurrentText();
    }
    else
    {
        offset = 56;
        sub_title_.setWidthHeight(0, 0);

        sub_text_.setWidthHeight(386, 466);
		sub_text_.setTextFont(&PUBLIC_NO_32_R_FONT);
		sub_text_.setColor(lv_color_hex(0xFFFFFF));
		sub_text_.setTextAlignment(CENTER);
		sub_text_.setTextVerticalAlignment(ALIGN_Y_MID);
		sub_text_.setLabelAlpha(LV_OPA_TRANSP);
		sub_text_.setWideTextAction(sub_text_wide_text_action_);
		sub_text_.resizeHeightToCurrentText();
        if(sub_text_.getHeight() > PUBLIC_NO_32_R_FONT.line_height)
        {
            offset = 24;
        }
        if(sub_text_.getHeight() > PUBLIC_NO_32_R_FONT.line_height * 2)
        {
            sub_text_.setHeight(PUBLIC_NO_28_R_FONT.line_height * 2);
		    sub_text_.setTextFont(&PUBLIC_NO_28_R_FONT);
		    sub_text_.resizeHeightToCurrentText();
        }
    }

    if(high_contaner_)
    {
        high_contaner_->setPosition(40, offset, 386, 100);
        switch (type_)
        {
        case QHST_NUMBER_UNIT:
        {
            QhstNumberUnit* tmp = dynamic_cast<QhstNumberUnit*>(high_contaner_);
            tmp->on_notify();
        }
            break;
        case QHST_IMG_TEXT:
        {
            QhstImgText* tmp = dynamic_cast<QhstImgText*>(high_contaner_);
            tmp->on_notify();
        }
            break;
        case QHST_ICON:
        {
            QhstIcon* tmp = dynamic_cast<QhstIcon*>(high_contaner_);
            tmp->on_notify();
        }
            break;

        default:
	    	assert(false && "[QwHalfScreen:8001]QwHalfScreen type is not expected");
            break;
        }
    }

    if(sub_title_.isVisible())
    {
        sub_title_.setAlign(ALIGN_IN_TM, 0, offset + 100 + 8);
        sub_text_.setAlignTo(sub_title_, ALIGN_OUT_BM, 0, 0);
    }
    else
    {
        sub_text_.setAlign(ALIGN_IN_TM, 0, offset + 100 + 8);
    }

}

void QwHalfScreen::handleTickEvent()
{
	event_poster_.handle_tick();
}

void QwHalfScreen::handleKeyEvent(uint8_t c)
{
}

void QwHalfScreen::handleClickEvent(const ClickEvent& evt)
{
	if(evt.getType() == ClickEvent::PRESSED)
	{
		event_poster_.hit_item(this);
	}
	event_poster_.handle_touch(evt);
}

void QwHalfScreen::handleDragEvent(const DragEvent& evt)
{
	event_poster_.handle_drag(evt);
}

void QwHalfScreen::handleGestureEvent(const GestureEvent& evt)
{
	event_poster_.handle_gesture(evt);
}

/**
 * @brief 设置类型
 * @param type 类型
 */
void QwHalfScreen::set_type(QW_HALF_SCREEN_TYPE type)
{
    type_ = type;
    if(high_contaner_ != nullptr)
    {
        remove(*high_contaner_);
        high_contaner_->~Container();
        high_contaner_ = nullptr;
        memset(static_cast<void*>(&half_screen_union_), 0, sizeof(half_screen_union_));
    }

    switch (type)
    {
    case QHST_NUMBER_UNIT:
    {
        high_contaner_ = new (static_cast<void*>(&half_screen_union_)) QhstNumberUnit();
        add(*high_contaner_);
        QhstNumberUnit* tmp = dynamic_cast<QhstNumberUnit*>(high_contaner_);
        tmp->setup();
    }
        break;
    case QHST_IMG_TEXT:
    {
        high_contaner_ = new (static_cast<void*>(&half_screen_union_)) QhstImgText();
        add(*high_contaner_);
        QhstImgText* tmp = dynamic_cast<QhstImgText*>(high_contaner_);
        tmp->setup();
    }
        break;
    case QHST_ICON:
    {
        high_contaner_ = new (static_cast<void*>(&half_screen_union_)) QhstIcon();
        add(*high_contaner_);
        QhstIcon* tmp = dynamic_cast<QhstIcon*>(high_contaner_);
        tmp->setup();
    }
        break;
    default:
		assert(false && "[QwHalfScreen:8001]QwHalfScreen type is not expected");
        break;
    }
}

/**
 * @brief 设置类型
 * @param type 类型
 */
Container* QwHalfScreen::get_high_contaner()
{
    return high_contaner_;
}

/**
 * @brief 设置主文本类型
 * @param t 文本内容
 */
void QwHalfScreen::set_sub_title_type(QHSTT_SUB_TITLE_TYPE t)
{
    sub_title_type_ = t;
}

/**
 * @brief 设置主文本内容
 * @param t 文本内容
 */
void QwHalfScreen::set_typed_dynamic_sub_title(TypedTextId t)
{
    sub_title_.setTypedDynamicText(t);
    sub_title_.setVisible(true);
}

 /**
 * @brief 设置主文本内容
 * @param t 文本内容
 */
void QwHalfScreen::set_typed_dynamic_sub_text(TypedTextId t)
{
    sub_text_.setTypedDynamicText(t);
    sub_text_.setVisible(true);
}

 /**
 * @brief 重置该组件
 */
void QwHalfScreen::set_reset()
{
    sub_title_.setVisible(false);
    sub_text_.setVisible(false);
}

void QwHalfScreen::set_sub_title_font(lv_font_t *font_)
{
    title_font_ = font_;
}

void QwHalfScreen::set_sub_text_font(lv_font_t *font_)
{
    text_font_ = font_;
}

/**
 * @brief 设置副文本的换行模式
 * @param action 换行模式
 */
void QwHalfScreen::set_sub_text_wide_text_action(WideTextAction action)
{
    sub_text_wide_text_action_ = action;
}

