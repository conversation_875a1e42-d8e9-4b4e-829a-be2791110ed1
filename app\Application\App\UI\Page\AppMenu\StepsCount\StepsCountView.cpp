/****************************************Copyright (c)****************************************
* <PERSON><PERSON> Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : yanxuqiang
* LastEditors: yanxuqiang
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2024-08-2 14:00:00: yanx<PERSON>qi<PERSON> 原始版本
*
*********************************************************************************************/

#include "StepsCountView.h"
#include "Image/images.h"
#include "qw_os_gui.h"

StepsCountView::StepsCountView(PageManager *manager)
    : PageView(manager)
    , day_data_(nullptr)
    , week_data_(nullptr)
    , update_day_data_(this, &StepsCountView::update_day_data_info)
    , update_week_data_(this, &StepsCountView::update_week_data_info)
{}

StepsCountView::~StepsCountView()
{}

void StepsCountView::setup()
{
    add(bg_);
    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(0x000000);

    add(swipe_);
    swipe_.setXY(0, 0);
    swipe_.setWidthHeight(HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    swipe_.setPageIndicatorBitmaps(Bitmap(&normalPage), Bitmap(&highlightedPage));
    swipe_.setPageIndicatorXY(12, 166);
    swipe_.setSwipeCutoff(30);
    swipe_.setEndSwipeElasticWidth(30);


    if (day_data_ != nullptr)
    {
        daily_page_.setup(day_data_->get_val(0)->today_data, static_cast<int *>(day_data_->get_val(0)->hours_data), day_data_->get_val(0)->today_goal);
    }
    else
    {
        daily_page_.setup(0, NULL, 5000);   //int today_data, int *hours_data, int today_goal
    }
    swipe_.add(daily_page_);

    if (week_data_ != nullptr)
    {
        //int weekTotaldata, int todayGoalData, int *dailyData int *dailyGoalData
        week_page_.setup(week_data_->get_val(0)->weekTotaldata, week_data_->get_val(0)->todayGoalData, static_cast<int *>(week_data_->get_val(0)->dailyData),
                         static_cast<int *>(week_data_->get_val(0)->dailyGoalData));
    }
    else
    {
        week_page_.setup(0, 5000, NULL, NULL);
    }

    swipe_.add(week_page_);

    swipe_.setSelectedPage(0);
    bg_.invalidate();
}

void StepsCountView::handleTickEvent()
{
    swipe_.handleTickEvent();
}

void StepsCountView::handleKeyEvent(uint8_t c)
{
    swipe_.handleKeyEvent(c);
    if (c == KEY_CLK_BACK)
    {
        manager_->push("MenuCard");
    }
}

void StepsCountView::handleClickEvent(const ClickEvent &evt)
{
    swipe_.handleClickEvent(evt);
}

void StepsCountView::handleDragEvent(const DragEvent &evt)
{
    swipe_.handleDragEvent(evt);
}

void StepsCountView::handleGestureEvent(const GestureEvent &evt)
{
    swipe_.handleGestureEvent(evt);
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL)
    {
        if (evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
            manager_->push("MenuCard");
        }
    }
}

void StepsCountView::set_update_day_data(ObserverDrawable<Drawable, StepsCountModel::DayData, 1> *observer)
{
    if (observer != nullptr)
    {
        day_data_ = observer;
        observer->bind_ctrl(0, daily_page_);
        observer->bind_notify(update_day_data_);
    }
}

void StepsCountView::update_day_data_info(Drawable *ctrl, Parameters<StepsCountModel::DayData> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        daily_page_.updateDailyPageData(data->get_val().today_data, static_cast<int *>(data->get_val().hours_data),
                                        data->get_val().today_goal);   //(int today_data, int *hours_data, int today_goal)
        bg_.invalidate();
    }
}

void StepsCountView::set_update_week_data(ObserverDrawable<Drawable, StepsCountModel::WeekData, 1> *observer)
{
    if (observer != nullptr)
    {
        week_data_ = observer;
        observer->bind_ctrl(0, week_page_);
        observer->bind_notify(update_week_data_);
    }
}

void StepsCountView::update_week_data_info(Drawable *ctrl, Parameters<StepsCountModel::WeekData> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        week_page_.updataWeekPageData(data->get_val().weekTotaldata, data->get_val().todayGoalData, static_cast<int *>(data->get_val().dailyData),
                                      static_cast<int *>(data->get_val().dailyGoalData));
        bg_.invalidate();
    }
}