/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgIntelligentNotify.cpp
@Time    :   2024/12/23 16:26:37
*
**************************************************************************/

#include "MsgIntelligentNotify.h"
#include "gui_event_service.h"
#include "qw_time_util.h"

MsgIntelligentNotify::MsgIntelligentNotify(int index) :
    push_message_details_callback_(this, &MsgIntelligentNotify::on_push_message_details)
{
    info_.id = index;
    // info_.priority = MSG_PRIORITY::TIP_1;
    // info_.scope = (uint16_t) MSG_SCOPE::ALL;
    info_.msg = "enumPOPUP_INTELLIGENT_NOTIFY";
    // info_.msg_page_type = PAGE_TYPE::MSG_TYPE_HALF_MESSAGE;

    msg_index_ = index;
    qw_msg_message_notify_info_t* msg_info = get_msg_message_notify_info();

    set_push_message_details_handle(push_message_details_callback_);
}

void MsgIntelligentNotify::set_info_parameter()
{
    qw_msg_message_notify_info_t* msg_info = get_msg_message_notify_info();
#ifndef SIMULATOR
    ins_data_t* ins_data = static_cast<ins_data_t*>(info_.user_data);
    if(ins_data == nullptr)
    {
        return;
    }

    msg_info->sub_title_type = QHSTT_TEXT;
    msg_info->sub_title = reinterpret_cast<const char*>(ins_data->title);
    
    if(ins_data->msg_type == enumMSG_INCOMING_TYPE)//来电
    {
        msg_info->qhst_icon_info.img_ = &notification_call_76;
    }
    else if(ins_data->msg_type == enumMSG_NOTE_TYPE)//短信
    {
        msg_info->sub_text = reinterpret_cast<const char*>(ins_data->message);
        msg_info->qhst_icon_info.img_ = &notification_message_76;
    }
    else if(ins_data->msg_type == enumMSG_APP_TYPE)//APP
    {
        msg_info->sub_text = reinterpret_cast<const char*>(ins_data->message);
        msg_info->qhst_icon_info.img_ = &notification_app_76;
    }
    set_title_font(const_cast<lv_font_t *>(&TEXT_NO32_M_ALL_FONT));
    set_text_font(const_cast<lv_font_t *>(&TEXT_NO32_M_ALL_FONT));
    set_sub_text_wide_text_action(WIDE_TEXT_CHARWRAP_SCROLL);

    msg_index_ = ins_data->index;
#endif
}

void MsgIntelligentNotify::on_push_message_details()
{
    // 跳转到消息详情页面
    PageManager* manager = static_cast<PageManager*>(get_msg_info().evt_manager);
    if (manager) {
        const char* page = manager->get_cur_page();
        static uint16_t index = 0;
#ifndef SIMULATOR
        index = ins_data_get_by_data_index(msg_index_);
#endif
        manager->page_command("NotificationDetails", 0, &index);//设置index
        manager->page_command("NotificationDetails", 1, const_cast<void*>(static_cast<const void*>(page)));//设置返回页面
        manager->push("NotificationDetails");
    }
}
